<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yxt</groupId>
        <artifactId>yxt-xframe</artifactId>
        <version>2.16.5</version>
        <relativePath/>
    </parent>
    <artifactId>purchase-order-center</artifactId>
    <version>1.0-SNAPSHOT</version>
    <description>采购中台</description>
    <packaging>pom</packaging>
    <modules>
        <module>purchase-order-bootstrap</module>
        <module>purchase-order-application</module>
        <module>purchase-order-common</module>
        <module>purchase-order-domain</module>
        <module>purchase-order-infrastructure</module>
        <module>purchase-order-interface</module>
        <module>purchase-order-sdk</module>
    </modules>
    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.6.13</spring-boot.version>
        <yxt-spring-boot-starter>4.6.4</yxt-spring-boot-starter>
        <module.deploy.skip>false</module.deploy.skip>
        <hutool.version>5.8.16</hutool.version>
        <okhttp.version>4.2.2</okhttp.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>purchase-order-interface</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>purchase-order-application</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>purchase-order-bootstrap</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>purchase-order-common</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>purchase-order-domain</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>purchase-order-infrastructure</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.open.purchase</groupId>
                <artifactId>purchase-order-sdk</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.yxt</groupId>
                <artifactId>yxt-core-spring-boot-starter</artifactId>
                <version>${yxt-spring-boot-starter}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.8</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.3.3</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>poi-ooxml-schemas</artifactId>
                        <groupId>org.apache.poi</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- Hutool工具类所有模块 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!-- okhttp -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version> <!--$NO-MVN-MAN-VER$ -->
            </dependency>
        </dependencies>

    </dependencyManagement>
    <build>
        <plugins>
            <!--获取主目录路径-->
            <plugin>
                <groupId>org.commonjava.maven.plugins</groupId>
                <artifactId>directory-maven-plugin</artifactId>
                <version>0.1</version>
                <executions>
                    <execution>
                        <id>directories</id>
                        <goals>
                            <goal>highest-basedir</goal>
                        </goals>
                        <phase>initialize</phase>
                        <configuration>
                            <property>main.basedir</property>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>
