package com.yxt.purchase.application.service;

import com.yxt.purchase.open.sdk.req.ImportPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.resp.AsyncTaskResult;
import java.util.concurrent.CompletableFuture;
import org.springframework.web.multipart.MultipartFile;

/**
 * 异步任务服务接口
 */
public interface AsyncTaskService {
    
    /**
     * 异步导入采购单
     *
     * @param file 导入文件
     * @param userId 用户ID
     * @param request 导入请求参数
     * @param taskId 任务ID
     * @return 异步任务结果
     */
    CompletableFuture<Void> asyncImportPurchaseOrder(MultipartFile file, String userId, 
                                                     ImportPurchaseOrderRequest request, String taskId);
    
    /**
     * 查询异步任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态
     */
    AsyncTaskResult queryAsyncTaskStatus(String taskId);
    
    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 状态
     * @param progress 进度
     * @param result 结果
     * @param errorMessage 错误信息
     */
    void updateTaskStatus(String taskId, String status, Integer progress, String result, String errorMessage);
}
