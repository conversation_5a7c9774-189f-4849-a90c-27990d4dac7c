package com.yxt.purchase.application.convert;

import cn.hutool.core.date.DatePattern;
import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderResult;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

/**
 * 采购单转换器
 */
@Component
public class PurchaseOrderAssembler {

  /**
   * 将DTO转换为响应对象
   */
  public PurchaseOrderResult toResult(PurchaseOrderAggregate dto) {
    if (dto == null) {
      return null;
    }

    PurchaseOrderResult result = new PurchaseOrderResult();

    // 设置基本信息
    result.setId(dto.getId());
    result.setPurchaseOrderNo(dto.getPurchaseOrderNo().getPurchaseOrderNo());
    result.setState(dto.getState().getCode());
    result.setRelatedOrderNo(dto.getRelatedOrderNo());
    result.setParentPurchaseOrderNo(dto.getParentPurchaseOrderNo());
    result.setAutoPayment(dto.getAutoPayment());
    result.setOverduePaymentTime(dto.getOverduePaymentTime());
    result.setCompanyCode(dto.getCompanyCode());
    result.setCompanyName(dto.getCompanyName());
    result.setOrganizationCode(dto.getOrganizationCode());
    result.setOrganizationName(dto.getOrganizationName());
    result.setPurchaseOrderLabel(dto.getPurchaseOrderLabel().getCode());
    result.setRemark(dto.getMsg());
    // 设置用户相关信息
    result.setConfirmBy(dto.getConfirmBy().getUserName());
    result.setConfirmUserId(dto.getConfirmBy().getUserId());
    result.setCreatedBy(dto.getCreatedBy().getUserName());
    result.setCreatedUserId(dto.getCreatedBy().getUserId());
    result.setUpdatedBy(dto.getModifiedBy().getUserName());
    result.setUpdatedUserId(dto.getModifiedBy().getUserId());
    // 设置时间相关信息
    if (dto.getConfirmTime() != null) {
      result.setConfirmTime(dto.getConfirmTime().format(DatePattern.NORM_DATETIME_FORMATTER));
    }
    if (dto.getCreatedTime() != null) {
      result.setCreatedTime(dto.getCreatedTime().format(DatePattern.NORM_DATETIME_FORMATTER));
    }
    if (dto.getModifiedTime() != null) {
      result.setUpdatedTime(dto.getModifiedTime().format(DatePattern.NORM_DATETIME_FORMATTER));
    }

    return result;
  }

  /**
   * 将DTO列表转换为响应对象列表
   */
  public List<PurchaseOrderResult> toResultList(List<PurchaseOrderAggregate> dos) {
    if (dos == null) {
      return null;
    }

    return dos.stream()
        .map(this::toResult)
        .collect(Collectors.toList());
  }
}