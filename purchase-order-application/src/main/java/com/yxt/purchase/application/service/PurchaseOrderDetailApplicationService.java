package com.yxt.purchase.application.service;

import com.yxt.purchase.application.entitys.PurchaseOrderDetailEntity;
import com.yxt.purchase.open.sdk.req.CreatePurchaseOrderDetailRequest;
import com.yxt.purchase.open.sdk.req.RemovePurchaseDetailByIdRequest;
import com.yxt.purchase.open.sdk.req.UpdatePurchaseOrderDetailRequest;
import java.util.List;
import java.util.Optional;

/**
 * 采购商品明细应用服务接口
 */
public interface PurchaseOrderDetailApplicationService {

  PurchaseOrderDetailEntity createDetail(CreatePurchaseOrderDetailRequest request);
  /**
   * 根据ID查询采购商品明细
   *
   * @param id 明细ID
   * @return 采购商品明细DTO
   */
  Optional<PurchaseOrderDetailEntity> getDetailById(Long id);

  /**
   * 根据采购单号查询采购商品明细列表
   *
   * @param purchaseOrderNo 采购单号
   * @return 采购商品明细DTO列表
   */
  List<PurchaseOrderDetailEntity> getDetailsByPurchaseOrderNo(String purchaseOrderNo);

  /**
   * 更新采购商品明细
   *
   * @param request 更新采购商品明细请求
   * @return 更新后的采购商品明细DTO
   */
  PurchaseOrderDetailEntity updateDetail(UpdatePurchaseOrderDetailRequest request);

  /**
   * 将采购商品明细标记为异常状态
   *
   * @param id 明细ID
   * @param errorMessage 错误信息
   * @return 更新后的采购商品明细DTO
   */
  PurchaseOrderDetailEntity markDetailAsException(Long id, String errorMessage);

  /**
   * 将采购商品明细标记为正常状态
   *
   * @param id 明细ID
   * @return 更新后的采购商品明细DTO
   */
  PurchaseOrderDetailEntity markDetailAsNormal(Long id);

  /**
   * 根据ID删除采购商品明细
   *
   * @param id 明细ID
   * @return 是否删除成功
   */
  boolean deleteDetailById(Long id);

  /**
   * 根据请求删除采购商品明细
   *
   * @param request 删除采购商品明细请求
   * @return 是否删除成功
   */
  boolean deleteDetail(RemovePurchaseDetailByIdRequest request);

  /**
   * 根据采购单号删除采购商品明细
   *
   * @param purchaseOrderNo 采购单号
   * @return 是否删除成功
   */
  boolean deleteDetailsByPurchaseOrderNo(String purchaseOrderNo);
}