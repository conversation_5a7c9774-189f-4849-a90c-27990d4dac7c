package com.yxt.purchase.application.convert;

import com.yxt.purchase.application.entitys.ImportHistoryEntity;
import com.yxt.purchase.domain.ImportHistoryAggregate;
import com.yxt.purchase.open.sdk.resp.ImportHistoryQueryResult;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

/**
 * 导入历史转换器
 */
@Component
public class ImportHistoryAssembler {
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 将领域聚合根转换为应用层实体
     */
    public ImportHistoryEntity toDTO(ImportHistoryAggregate aggregate) {
        if (aggregate == null) {
            return null;
        }
        
        ImportHistoryEntity entity = new ImportHistoryEntity();
        entity.setImportId(aggregate.getImportId());
        entity.setFileName(aggregate.getFileName());
        entity.setImportStatus(aggregate.getImportStatus() != null ? aggregate.getImportStatus().getCode() : null);
        entity.setImportStatusDesc(aggregate.getImportStatus() != null ? aggregate.getImportStatus().getDesc() : null);
        entity.setImportTime(aggregate.getImportTime());
        entity.setImportBy(aggregate.getImportBy());
        entity.setImportByName(aggregate.getImportByName());
        entity.setTotalCount(aggregate.getTotalCount());
        entity.setSuccessCount(aggregate.getSuccessCount());
        entity.setFailCount(aggregate.getFailCount());
        entity.setErrorMessage(aggregate.getErrorMessage());
        entity.setErrorFilePath(aggregate.getErrorFilePath());
        entity.setPurchaseOrderNos(aggregate.getPurchaseOrderNos());
        
        return entity;
    }
    
    /**
     * 将领域聚合根列表转换为应用层实体列表
     */
    public List<ImportHistoryEntity> toDTOList(List<ImportHistoryAggregate> aggregates) {
        if (aggregates == null) {
            return null;
        }
        
        return aggregates.stream()
            .map(this::toDTO)
            .collect(Collectors.toList());
    }
    
    /**
     * 将应用层实体转换为响应对象
     */
    public ImportHistoryQueryResult toResult(ImportHistoryEntity entity) {
        if (entity == null) {
            return null;
        }
        
        ImportHistoryQueryResult result = new ImportHistoryQueryResult();
        result.setFileName(entity.getFileName());
        result.setOperationTime(entity.getImportTime());
        result.setOperationId(entity.getImportBy());
        result.setOperationName(entity.getImportByName());
        result.setStatus(entity.getImportStatus());
        result.setErrorFilePath(entity.getErrorFilePath());
        result.setPurchaseOrders(entity.getPurchaseOrderNos());
        
        return result;
    }
    
    /**
     * 将应用层实体列表转换为响应对象列表
     */
    public List<ImportHistoryQueryResult> toResultList(List<ImportHistoryEntity> entities) {
        if (entities == null) {
            return null;
        }
        
        return entities.stream()
            .map(this::toResult)
            .collect(Collectors.toList());
    }
}
