package com.yxt.purchase.application.convert;

import com.yxt.purchase.application.entitys.BizLogEntity;
import com.yxt.purchase.domain.entity.BizLog;
import com.yxt.purchase.open.sdk.resp.BizLogResult;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

/**
 * 业务日志转换器
 */
@Component
public class BizLogAssembler {

  /**
   * 将领域实体转换为DTO
   */
  public BizLogEntity toDTO(BizLog bizLog) {
    if (bizLog == null) {
      return null;
    }

    BizLogEntity dto = new BizLogEntity();
    dto.setId(bizLog.getId());
    dto.setBizId(bizLog.getBizId());
    dto.setBizType(bizLog.getBizType());
    dto.setOperationType(bizLog.getOperationType());
    dto.setOperationContent(bizLog.getOperationContent());
    dto.setOperatorId(bizLog.getOperatorId());
    dto.setOperatorName(bizLog.getOperatorName());
    dto.setOperationTime(bizLog.getOperationTime());
    
    return dto;
  }

  /**
   * 将领域实体列表转换为DTO列表
   */
  public List<BizLogEntity> toDTOList(List<BizLog> bizLogs) {
    if (bizLogs == null) {
      return null;
    }

    return bizLogs.stream()
        .map(this::toDTO)
        .collect(Collectors.toList());
  }

  /**
   * 将DTO转换为响应对象
   */
  public BizLogResult toResult(BizLogEntity dto) {
    if (dto == null) {
      return null;
    }

    BizLogResult result = new BizLogResult();
    result.setId(dto.getId());
    result.setBizId(dto.getBizId());
    result.setBizType(dto.getBizType());
    result.setOperationType(dto.getOperationType());
    result.setOperationContent(dto.getOperationContent());
    result.setOperatorId(dto.getOperatorId());
    result.setOperatorName(dto.getOperatorName());
    
    if (dto.getOperationTime() != null) {
      result.setOperationTime(dto.getOperationTime().toString());
    }
    
    return result;
  }

  /**
   * 将DTO列表转换为响应对象列表
   */
  public List<BizLogResult> toResultList(List<BizLogEntity> dtos) {
    if (dtos == null) {
      return null;
    }

    return dtos.stream()
        .map(this::toResult)
        .collect(Collectors.toList());
  }
}
