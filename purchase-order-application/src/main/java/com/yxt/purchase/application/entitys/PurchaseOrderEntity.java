package com.yxt.purchase.application.entitys;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 采购单实体
 */
@Data
public class PurchaseOrderEntity {

  /**
   * 主键ID
   */
  private Long id;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 采购单号
   */
  private String purchaseOrderNo;

  /**
   * 采购单状态 待确认:WAIT_CONFIRM 已确认:CONFIRMED 已完成:FINISHED 已取消:CANCELED
   */
  private String state;

  /**
   * 关联订单号，采购转订单后的单号
   */
  private String relatedOrderNo;

  /**
   * 父采购单号 关联一次请求或者一次导入
   */
  private String parentPurchaseOrderNo;

  /**
   * 是否超时自动支付
   */
  private Boolean autoPayment;

  /**
   * 设置超期自动支付时间 天为单位，0 表示超期不会自动支付
   */
  private Integer overduePaymentTime;

  /**
   * 分公司编码
   */
  private String companyCode;

  /**
   * 分公司名称
   */
  private String companyName;

  /**
   * 所属机构编码
   */
  private String organizationCode;

  /**
   * 所属机构名称
   */
  private String organizationName;

  /**
   * 采购单标签 办公用品:CONSUMABLES 导入铺货: MULTIPLE_STORE
   */
  private String purchaseOrderLabel;

  /**
   * 确认人ID
   */
  private String confirmBy;

  /**
   * 确认人姓名
   */
  private String confirmUserName;

  /**
   * 确认时间
   */
  private LocalDateTime confirmTime;

  /**
   * 创建人ID
   */
  private String createdBy;

  /**
   * 创建人姓名
   */
  private String createdUserName;



  /**
   * 创建时间
   */
  private LocalDateTime createdTime;

  /**
   * 更新时间
   */
  private LocalDateTime updatedTime;

  /**
   * 备注
   */
  private String remark;
}