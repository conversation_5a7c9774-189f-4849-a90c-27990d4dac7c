package com.yxt.purchase.application.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.yxt.purchase.application.entitys.ImportPurchaseOrderEntity;
import com.yxt.purchase.domain.command.PurchaseOrderCreateCommand;
import com.yxt.purchase.domain.entity.PurchaseOrderDetailModel;
import com.yxt.purchase.domain.service.PurchaseOrderDomainService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import org.springframework.stereotype.Component;


@Component
public class PurchaseOrderExcelListener implements ReadListener<ImportPurchaseOrderEntity> {

  /**
   * -- GETTER -- 获取结果
   *
   * @return 结果
   */
  private final List<PurchaseOrderDetailModel> excelData=new ArrayList<>();
  public PurchaseOrderExcelListener() {
  }
  /**
   * 读取异常后 调用此方法
   */
  @Override
  public void onException(Exception exception, AnalysisContext context) throws Exception {
    throw new Exception("文件异常，请重试");
  }

  /**
   * 读取表头的时候触发
   */
  @Override
  public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
    ReadListener.super.invokeHead(headMap, context);
  }
  /**
   * 这个每一条数据解析都会来调用
   *
   * @param importPurchaseOrderEntity one row value. It is same as
   *                                  {@link AnalysisContext#readRowHolder()}
   * @param analysisContext           analysis context
   */
  @Override
  public void invoke(ImportPurchaseOrderEntity importPurchaseOrderEntity,
      AnalysisContext analysisContext) {
    excelData.add(BeanUtil.toBean(importPurchaseOrderEntity, PurchaseOrderDetailModel.class));
  }

  @Override
  public void extra(CellExtra extra, AnalysisContext context) {
    ReadListener.super.extra(extra, context);
  }

  /**
   * 所有数据解析完成了 都会来调用
   */
  @Override
  public void doAfterAllAnalysed(AnalysisContext analysisContext) {

  }

  @Override
  public boolean hasNext(AnalysisContext context) {
    return ReadListener.super.hasNext(context);
  }


  public List<PurchaseOrderDetailModel> getResult() {
    return excelData;
  }

}
