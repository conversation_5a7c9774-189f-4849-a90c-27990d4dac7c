package com.yxt.purchase.application.service;

import com.yxt.purchase.open.sdk.req.CreatePurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.ImportPurchaseOrderRequest;
import java.io.IOException;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/**
 * 采购单应用服务接口
 */
public interface PurchaseOrderApplicationService {


    List<String> importPurchaseOrder(MultipartFile file , String userId, ImportPurchaseOrderRequest request)
        throws IOException;

    List<String> createPurchaseOrder(String userId, CreatePurchaseOrderRequest request);

}
