package com.yxt.purchase.application.service;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.purchase.open.sdk.req.CreatePurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.ImportPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.PurchaseOrderListQueryRequest;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderResult;
import java.io.IOException;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/**
 * 采购单应用服务接口
 */
public interface PurchaseOrderApplicationService {


    List<String> importPurchaseOrder(MultipartFile file , String userId, ImportPurchaseOrderRequest request)
        throws IOException;

    List<String> createPurchaseOrder(String userId, CreatePurchaseOrderRequest request);

    /**
     * 查询采购单列表
     *
     * @param userId 用户ID
     * @param request 查询请求
     * @return 采购单分页结果
     */
    PageDTO<PurchaseOrderResult> queryPurchaseOrders(String userId, PurchaseOrderListQueryRequest request);

    /**
     * 更新采购单自动扣款配置
     *
     * @param userId 用户ID
     * @param request 更新自动扣款配置请求
     * @return 是否更新成功
     */
    Boolean updateAutoDeductionConfig(String userId, UpdateAutoDeductionConfigRequest request);

}
