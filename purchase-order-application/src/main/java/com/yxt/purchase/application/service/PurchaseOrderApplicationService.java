package com.yxt.purchase.application.service;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.purchase.open.sdk.req.BatchConfirmRequest;
import com.yxt.purchase.open.sdk.req.BatchRejectPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.CopyPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.CreatePurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.ImportPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.PurchaseOrderInfoRequest;
import com.yxt.purchase.open.sdk.req.PurchaseOrderListQueryRequest;
import com.yxt.purchase.open.sdk.req.UpdateAutoDeductionConfigRequest;
import com.yxt.purchase.open.sdk.req.UpdatePurchaseOrderCancelRequest;
import com.yxt.purchase.open.sdk.req.UpdatePurchaseOrderCompletedRequest;
import com.yxt.purchase.open.sdk.resp.ImportHistoryQueryResult;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderInfoResult;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderResult;
import java.io.IOException;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/**
 * 采购单应用服务接口
 */
public interface PurchaseOrderApplicationService {


  List<String> importPurchaseOrder(MultipartFile file, String userId,
      ImportPurchaseOrderRequest request)
      throws IOException;

  List<String> createPurchaseOrder(String userId, CreatePurchaseOrderRequest request);

  /**
   * 查询采购单列表
   *
   * @param userId  用户ID
   * @param request 查询请求
   * @return 采购单分页结果
   */
  PageDTO<PurchaseOrderResult> queryPurchaseOrders(String userId,
      PurchaseOrderListQueryRequest request);

  /**
   * 更新采购单自动扣款配置
   *
   * @param userId  用户ID
   * @param request 更新自动扣款配置请求
   * @return 是否更新成功
   */
  Boolean updateAutoDeductionConfig(String userId, UpdateAutoDeductionConfigRequest request);

  /**
   * 批量驳回采购单
   *
   * @param userId  用户ID
   * @param request 批量驳回采购单请求
   * @return 驳回成功的采购单号列表
   */
  List<String> batchRejectPurchaseOrder(String userId, BatchRejectPurchaseOrderRequest request);

  /**
   * 查询采购单详情
   *
   * @param userId  用户ID
   * @param request 采购单详情查询请求
   * @return 采购单详情查询结果
   */
  PurchaseOrderInfoResult purchaseOrderInfoQuery(String userId, PurchaseOrderInfoRequest request);

  /**
   * 复制采购单
   *
   * @param userId  用户ID
   * @param request 复制采购单请求
   * @return 新创建的采购单号
   */
  String copyPurchaseOrder(String userId, CopyPurchaseOrderRequest request);

  List<String> batchReConfirmOrder(String userId,BatchConfirmRequest request);

  /**
   * 更新采购单为已完成状态
   *
   * @param request 更新采购单完成状态请求
   */
  void updatePurchaseOrderCompleted(UpdatePurchaseOrderCompletedRequest request);

  /**
   * 取消采购单
   *
   * @param request 取消采购单请求
   * @return 是否取消成功
   */
  Boolean updatePurchaseOrderCancel(UpdatePurchaseOrderCancelRequest request);

  /**
   * 查询导入历史记录
   *
   * @param userId 用户ID
   * @return 导入历史记录列表
   */
  List<ImportHistoryQueryResult> importHistoryQuery(String userId);

}
