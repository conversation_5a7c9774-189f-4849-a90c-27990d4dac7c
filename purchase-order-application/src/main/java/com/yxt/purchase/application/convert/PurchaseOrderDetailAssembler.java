package com.yxt.purchase.application.convert;

import com.yxt.purchase.application.entitys.PurchaseOrderDetailEntity;
import com.yxt.purchase.domain.PurchaseOrderDetailAggregate;
import com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDetailDO;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderDetailResult;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

/**
 * 采购商品明细转换器
 */
@Component
public class PurchaseOrderDetailAssembler {

  /**
   * 将聚合根转换为DTO
   */
  public PurchaseOrderDetailEntity toDTO(PurchaseOrderDetailAggregate aggregate) {
    if (aggregate == null) {
      return null;
    }

    PurchaseOrderDetailEntity dto = new PurchaseOrderDetailEntity();
    dto.setId(aggregate.getId());
    dto.setPurchaseOrderNo(aggregate.getPurchaseOrderNo());
    dto.setErpCode(aggregate.getErpCode());
    dto.setErpName(aggregate.getErpName());
    dto.setCommoditySpec(aggregate.getCommoditySpec());
    dto.setManufacture(aggregate.getManufacture());
    dto.setCommodityCount(aggregate.getCommodityCount());
    dto.setStatus(aggregate.getStatus());
    dto.setExMsg(aggregate.getExMsg());
    dto.setSysCreateTime(aggregate.getSysCreateTime());
    dto.setSysUpdateTime(aggregate.getSysUpdateTime());

    return dto;
  }

  /**
   * 将聚合根列表转换为DTO列表
   */
  public List<PurchaseOrderDetailEntity> toDTOList(List<PurchaseOrderDetailAggregate> aggregates) {
    if (aggregates == null) {
      return null;
    }

    return aggregates.stream()
        .map(this::toDTO)
        .collect(Collectors.toList());
  }

  /**
   * 将DTO转换为响应对象
   */
  public PurchaseOrderDetailResult toResult(PurchaseOrderDetailEntity dto) {
    if (dto == null) {
      return null;
    }

    PurchaseOrderDetailResult result = new PurchaseOrderDetailResult();
    result.setId(dto.getId());
    result.setPurchaseOrderNo(dto.getPurchaseOrderNo());
    result.setErpCode(dto.getErpCode());
    result.setErpName(dto.getErpName());
    result.setCommoditySpec(dto.getCommoditySpec());
    result.setManufacture(dto.getManufacture());
    result.setCommodityCount(dto.getCommodityCount());
    result.setStatus(dto.getStatus());
    result.setExMsg(dto.getExMsg());
    result.setSysCreateTime(dto.getSysCreateTime());
    result.setSysUpdateTime(dto.getSysUpdateTime());

    return result;
  }

  /**
   * 将DTO列表转换为响应对象列表
   */
  public List<PurchaseOrderDetailResult> toResultList(List<PurchaseOrderDetailEntity> dtos) {
    if (dtos == null) {
      return null;
    }

    return dtos.stream()
        .map(this::toResult)
        .collect(Collectors.toList());
  }

  /**
   * 将数据对象转换为聚合根
   */
  public PurchaseOrderDetailAggregate toAggregate(PurchaseOrderDetailDO dataObject) {
    if (dataObject == null) {
      return null;
    }

    PurchaseOrderDetailAggregate aggregate = new PurchaseOrderDetailAggregate();
    aggregate.setId(dataObject.getId());
    aggregate.setPurchaseOrderNo(dataObject.getPurchaseOrderNo());
    aggregate.setErpCode(dataObject.getErpCode());
    aggregate.setErpName(dataObject.getErpName());
    aggregate.setCommoditySpec(dataObject.getCommoditySpec());
    aggregate.setManufacture(dataObject.getManufacture());
    aggregate.setCommodityCount(dataObject.getCommodityCount());
    aggregate.setStatus(dataObject.getStatus());
    aggregate.setExMsg(dataObject.getExMsg());
    aggregate.setSysCreateTime(dataObject.getSysCreateTime());
    aggregate.setSysUpdateTime(dataObject.getSysUpdateTime());

    return aggregate;
  }

  /**
   * 将数据对象列表转换为聚合根列表
   */
  public List<PurchaseOrderDetailAggregate> toAggregateList(List<PurchaseOrderDetailDO> dataObjects) {
    if (dataObjects == null) {
      return null;
    }

    return dataObjects.stream()
        .map(this::toAggregate)
        .collect(Collectors.toList());
  }

  /**
   * 将聚合根转换为数据对象
   */
  public PurchaseOrderDetailDO toDO(PurchaseOrderDetailAggregate aggregate) {
    if (aggregate == null) {
      return null;
    }

    PurchaseOrderDetailDO dataObject = new PurchaseOrderDetailDO();
    dataObject.setId(aggregate.getId());
    dataObject.setPurchaseOrderNo(aggregate.getPurchaseOrderNo());
    dataObject.setErpCode(aggregate.getErpCode());
    dataObject.setErpName(aggregate.getErpName());
    dataObject.setCommoditySpec(aggregate.getCommoditySpec());
    dataObject.setManufacture(aggregate.getManufacture());
    dataObject.setCommodityCount(aggregate.getCommodityCount());
    dataObject.setStatus(aggregate.getStatus());
    dataObject.setExMsg(aggregate.getExMsg());
    dataObject.setSysCreateTime(aggregate.getSysCreateTime());
    dataObject.setSysUpdateTime(aggregate.getSysUpdateTime());

    return dataObject;
  }
}