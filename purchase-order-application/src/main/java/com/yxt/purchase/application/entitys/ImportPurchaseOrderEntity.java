package com.yxt.purchase.application.entitys;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ImportPurchaseOrderEntity {

  /**
   * 组织编码
   */
  @ExcelProperty(value = "*门店编码（必填）")
  private String organizationCode;
  /**
   * erp编码
   */
  @ExcelProperty(value = "*商品编码（必填）")
  private String erpCode;

  /**
   * 数量
   */
  @ExcelProperty(value = "*采购数量（必填）")
  private String qty;

}
