package com.yxt.purchase.application.service.impl;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.purchase.application.convert.PurchaseOrderDetailAssembler;
import com.yxt.purchase.application.entitys.PurchaseOrderDetailEntity;
import com.yxt.purchase.application.service.PurchaseOrderDetailApplicationService;
import com.yxt.purchase.domain.PurchaseOrderDetailAggregate;
import com.yxt.purchase.domain.command.CreatePurchaseOrderDetailCommand;
import com.yxt.purchase.domain.command.PurchaseOrderDetailQueryCommand;
import com.yxt.purchase.domain.command.RemovePurchaseOrderDetailCommand;
import com.yxt.purchase.domain.command.UpdatePurchaseOrderDetailCommand;
import com.yxt.purchase.domain.service.PurchaseOrderDetailDomainService;
import com.yxt.purchase.domain.entity.PurchaseOrderDetailQueryResult;
import com.yxt.purchase.open.sdk.req.CreatePurchaseOrderDetailRequest;
import com.yxt.purchase.open.sdk.req.PurchaseOrderDetailQueryRequest;
import com.yxt.purchase.open.sdk.req.RemovePurchaseDetailByIdRequest;
import com.yxt.purchase.open.sdk.req.UpdatePurchaseOrderDetailRequest;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderDetailResult;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 采购商品明细应用服务实现类
 */
@Service
public class PurchaseOrderDetailApplicationServiceImpl implements
    PurchaseOrderDetailApplicationService {

  @Autowired
  private PurchaseOrderDetailDomainService purchaseOrderDetailDomainService;

  @Autowired
  private PurchaseOrderDetailAssembler purchaseOrderDetailAssembler;

  @Override
  public PurchaseOrderDetailEntity createDetail(CreatePurchaseOrderDetailRequest request) {
    // 参数校验
    if (request == null) {
      throw new IllegalArgumentException("请求参数不能为空");
    }
    if (request.getPurchaseOrderNo() == null || request.getPurchaseOrderNo().isEmpty()) {
      throw new IllegalArgumentException("采购订单编号不能为空");
    }
    if (request.getErpCode() == null || request.getErpCode().isEmpty()) {
      throw new IllegalArgumentException("商品编码不能为空");
    }
    if (request.getErpName() == null || request.getErpName().isEmpty()) {
      throw new IllegalArgumentException("商品名称不能为空");
    }
    if (request.getCommodityCount() == null
        || request.getCommodityCount().compareTo(new BigDecimal("0")) <= 0) {
      throw new IllegalArgumentException("商品数量必须大于0");
    }
    // 转换请求为领域命令
    CreatePurchaseOrderDetailCommand command = new CreatePurchaseOrderDetailCommand();
    command.setPurchaseOrderNo(request.getPurchaseOrderNo());
    command.setErpCode(request.getErpCode());
    command.setErpName(request.getErpName());
    command.setCommoditySpec(request.getCommoditySpec());
    command.setManufacture(request.getManufacture());
    command.setCommodityCount(request.getCommodityCount());

    // 调用领域服务
    PurchaseOrderDetailAggregate detailAggregate = purchaseOrderDetailDomainService.createDetail(
        command);

    // 转换聚合根为DTO
    return purchaseOrderDetailAssembler.toDTO(detailAggregate);
  }

  @Override
  public Optional<PurchaseOrderDetailEntity> getDetailById(Long id) {
    if (id == null || id <= 0) {
      throw new IllegalArgumentException("明细ID不能为空且必须大于0");
    }

    // 调用领域服务
    Optional<PurchaseOrderDetailAggregate> detailAggregate = purchaseOrderDetailDomainService.getDetailById(
        id);

    // 转换聚合根为DTO
    return detailAggregate.map(purchaseOrderDetailAssembler::toDTO);
  }

  @Override
  public List<PurchaseOrderDetailEntity> getDetailsByPurchaseOrderNo(String purchaseOrderNo) {
    if (purchaseOrderNo == null || purchaseOrderNo.isEmpty()) {
      throw new IllegalArgumentException("采购订单编号不能为空");
    }

    // 调用领域服务
    List<PurchaseOrderDetailAggregate> detailAggregates = purchaseOrderDetailDomainService.getDetailsByPurchaseOrderNo(
        purchaseOrderNo);

    // 转换聚合根列表为DTO列表
    return detailAggregates.stream()
        .map(purchaseOrderDetailAssembler::toDTO)
        .collect(Collectors.toList());
  }

  @Override
  public PurchaseOrderDetailEntity updateDetail(String userId,
      UpdatePurchaseOrderDetailRequest request) {

    // 转换请求为领域命令
    UpdatePurchaseOrderDetailCommand command = new UpdatePurchaseOrderDetailCommand();
    command.setId(Long.parseLong(request.getPurchaseOrderDetailId()));
    command.setPurchaseOrderNo(request.getPurchaseOrderNo());
    command.setCommodityCount(request.getCommodityCount());
    command.setUserId(userId);
    // 调用领域服务
    PurchaseOrderDetailAggregate detailAggregate = purchaseOrderDetailDomainService.updateDetail(
        command);
    // 转换聚合根为DTO
    return purchaseOrderDetailAssembler.toDTO(detailAggregate);
  }

  @Override
  public PurchaseOrderDetailEntity markDetailAsException(Long id, String errorMessage) {
    // 参数校验
    if (id == null || id <= 0) {
      throw new IllegalArgumentException("明细ID不能为空且必须大于0");
    }
    if (errorMessage == null || errorMessage.isEmpty()) {
      throw new IllegalArgumentException("错误信息不能为空");
    }

    // 调用领域服务
    PurchaseOrderDetailAggregate detailAggregate = purchaseOrderDetailDomainService.markDetailAsException(
        id, errorMessage);

    // 转换聚合根为DTO
    return purchaseOrderDetailAssembler.toDTO(detailAggregate);
  }

  @Override
  public PurchaseOrderDetailEntity markDetailAsNormal(Long id) {
    // 参数校验
    if (id == null || id <= 0) {
      throw new IllegalArgumentException("明细ID不能为空且必须大于0");
    }

    // 调用领域服务
    PurchaseOrderDetailAggregate detailAggregate = purchaseOrderDetailDomainService.markDetailAsNormal(
        id);

    // 转换聚合根为DTO
    return purchaseOrderDetailAssembler.toDTO(detailAggregate);
  }

  @Override
  public boolean deleteDetailById(Long id) {
    // 参数校验
    if (id == null || id <= 0) {
      throw new IllegalArgumentException("明细ID不能为空且必须大于0");
    }
    // 调用领域服务
    return purchaseOrderDetailDomainService.deleteDetailById(id);
  }

  @Override
  public boolean deleteDetail(String userId,RemovePurchaseDetailByIdRequest request) {

    // 转换请求为领域命令
    RemovePurchaseOrderDetailCommand command = new RemovePurchaseOrderDetailCommand();
    command.setId(Long.parseLong(request.getPurchaseOrderDetailId()));
    command.setPurchaseOrderNo(request.getPurchaseOrderNo());
    command.setUserId(userId);
    // 调用领域服务
    return purchaseOrderDetailDomainService.deleteDetail(command);
  }

  @Override
  public boolean deleteDetailsByPurchaseOrderNo(String purchaseOrderNo) {
    // 参数校验
    if (purchaseOrderNo == null || purchaseOrderNo.isEmpty()) {
      throw new IllegalArgumentException("采购订单编号不能为空");
    }
    // 调用领域服务
    return purchaseOrderDetailDomainService.deleteDetailsByPurchaseOrderNo(purchaseOrderNo);
  }

  @Override
  public PageDTO<PurchaseOrderDetailResult> queryPurchaseOrderDetails(String userId,
      PurchaseOrderDetailQueryRequest request) {
    // 参数校验
    if (request == null) {
      throw new IllegalArgumentException("查询请求不能为空");
    }


    // 转换请求为领域命令
    PurchaseOrderDetailQueryCommand command = new PurchaseOrderDetailQueryCommand();
    command.setPurchaseOrderNo(request.getPurchaseOrderNo());
    command.setStatus(request.getStatus());
    command.setErpName(request.getErpName());
    command.setErpCode(request.getErpCode());
    command.setCurrentPage(request.getCurrentPage().intValue());
    command.setPageSize(request.getPageSize().intValue());
    command.setUserId(userId);

    // 调用领域服务查询采购单明细
    PurchaseOrderDetailQueryResult queryResult = purchaseOrderDetailDomainService.queryPurchaseOrderDetails(
        command);

    // 转换聚合根列表为DTO列表
    List<PurchaseOrderDetailEntity> detailEntities = queryResult.getPurchaseOrderDetailAggregates()
        .stream()
        .map(purchaseOrderDetailAssembler::toDTO)
        .collect(Collectors.toList());

    // 转换DTO列表为响应对象列表
    List<PurchaseOrderDetailResult> detailResults = purchaseOrderDetailAssembler.toResultList(
        detailEntities);

    // 构建分页结果
    PageDTO<PurchaseOrderDetailResult> pageDTO = new PageDTO<>();
    pageDTO.setData(detailResults);
    pageDTO.setTotalCount(queryResult.getTotalCount());
    pageDTO.setTotalPage(queryResult.getTotalPage());
    pageDTO.setCurrentPage(queryResult.getCurrentPage().longValue());
    pageDTO.setPageSize(queryResult.getPageSize().longValue());

    return pageDTO;
  }
}