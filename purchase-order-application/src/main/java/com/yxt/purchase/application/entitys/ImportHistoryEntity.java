package com.yxt.purchase.application.entitys;

import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 导入历史应用层实体
 */
@Data
public class ImportHistoryEntity {
    
    /**
     * 导入记录ID
     */
    private String importId;
    
    /**
     * 导入文件名
     */
    private String fileName;
    
    /**
     * 导入状态
     */
    private Integer importStatus;
    
    /**
     * 导入状态描述
     */
    private String importStatusDesc;
    
    /**
     * 导入时间
     */
    private LocalDateTime importTime;
    
    /**
     * 导入人ID
     */
    private String importBy;
    
    /**
     * 导入人姓名
     */
    private String importByName;
    
    /**
     * 总记录数
     */
    private Integer totalCount;
    
    /**
     * 成功记录数
     */
    private Integer successCount;
    
    /**
     * 失败记录数
     */
    private Integer failCount;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 错误文件路径
     */
    private String errorFilePath;
    
    /**
     * 生成的采购单号列表
     */
    private List<String> purchaseOrderNos;
}
