package com.yxt.purchase.application.service.impl;

import com.alibaba.excel.EasyExcel;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.purchase.application.convert.PurchaseOrderAssembler;
import com.yxt.purchase.application.entitys.ImportPurchaseOrderEntity;
import com.yxt.purchase.application.service.PurchaseOrderApplicationService;
import com.yxt.purchase.domain.command.BatchRejectPurchaseOrderCommand;
import com.yxt.purchase.domain.command.CopyPurchaseOrderCommand;
import com.yxt.purchase.domain.command.PurchaseOrderCreateCommand;
import com.yxt.purchase.domain.command.PurchaseOrderInfoQueryCommand;
import com.yxt.purchase.domain.command.PurchaseOrderQueryCommand;
import com.yxt.purchase.domain.command.UpdateAutoDeductionCommand;
import com.yxt.purchase.domain.entity.PurchaseOrderDetailModel;
import com.yxt.purchase.domain.service.PurchaseOrderDomainService;
import com.yxt.purchase.domain.entity.PurchaseOrderInfoQueryResult;
import com.yxt.purchase.domain.entity.PurchaseOrderQueryResult;
import com.yxt.purchase.open.sdk.req.BatchConfirmRequest;
import com.yxt.purchase.open.sdk.req.BatchRejectPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.CopyPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.CreatePurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.ImportPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.PurchaseOrderInfoRequest;
import com.yxt.purchase.open.sdk.req.PurchaseOrderListQueryRequest;
import com.yxt.purchase.open.sdk.req.UpdateAutoDeductionConfigRequest;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderInfoResult;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderResult;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * 采购单应用服务实现类
 */
@Slf4j
@Service
public class PurchaseOrderApplicationServiceImpl implements PurchaseOrderApplicationService {

  @Autowired
  private PurchaseOrderDomainService purchaseOrderDomainService;

  @Autowired
  private PurchaseOrderAssembler purchaseOrderAssembler;

  @Override
  public List<String> importPurchaseOrder(
      MultipartFile file, String userId, ImportPurchaseOrderRequest request) throws IOException {
    // 读取Excel文件
    PurchaseOrderExcelListener purchaseOrderExcelListener = new PurchaseOrderExcelListener();
    EasyExcel.read(file.getInputStream(), ImportPurchaseOrderEntity.class,
        purchaseOrderExcelListener).sheet().doRead();
    // 创建command 执行新增操作
    PurchaseOrderCreateCommand purchaseOrderCreateCommand = new PurchaseOrderCreateCommand();
    purchaseOrderCreateCommand.setAutoConfirm(request.getAutoConfirm());
    purchaseOrderCreateCommand.setIsAutoPayment(request.getIsAutoPayment());
    purchaseOrderCreateCommand.setOverduePaymentTime(request.getOverduePaymentTime());
    purchaseOrderCreateCommand.setUserId(userId);
    purchaseOrderCreateCommand.setItems(purchaseOrderExcelListener.getResult());
    // 创建PurchaseOrder
    return purchaseOrderDomainService.createPurchaseOrder(purchaseOrderCreateCommand);
  }

  @Override
  public List<String> createPurchaseOrder(String userId, CreatePurchaseOrderRequest request) {
    PurchaseOrderCreateCommand purchaseOrderCreateCommand = new PurchaseOrderCreateCommand();
    purchaseOrderCreateCommand.setAutoConfirm(request.getAutoConfirm());
    purchaseOrderCreateCommand.setIsAutoPayment(request.getIsAutoPayment());
    purchaseOrderCreateCommand.setOverduePaymentTime(request.getOverduePaymentTime());
    purchaseOrderCreateCommand.setUserId(userId);
    purchaseOrderCreateCommand.setItems(request.getItems().stream().map(item -> {
      PurchaseOrderDetailModel purchaseOrderDetailModel = new PurchaseOrderDetailModel();
      purchaseOrderDetailModel.setErpCode(item.getErpCode());
      purchaseOrderDetailModel.setOrganizationCode(item.getOrganizationCode());
      purchaseOrderDetailModel.setQty(item.getQty().toString());
      return purchaseOrderDetailModel;

    }).collect(
        Collectors.toList()));
    // 创建PurchaseOrder
    return purchaseOrderDomainService.createPurchaseOrder(purchaseOrderCreateCommand);
  }

  @Override
  public PageDTO<PurchaseOrderResult> queryPurchaseOrders(String userId,
      PurchaseOrderListQueryRequest request) {
    // 参数校验
    if (request == null) {
      throw new IllegalArgumentException("查询请求不能为空");
    }

    // 转换请求为领域命令
    PurchaseOrderQueryCommand command = new PurchaseOrderQueryCommand();
    command.setCompanyCode(request.getCompanyCode());
    command.setOrganizationCode(request.getOrganizationCode());
    command.setPurchaseOrderLabel(request.getPurchaseOrderLabel());
    command.setState(request.getState());
    command.setPurchaseOrderNo(request.getPurchaseOrderNo());
    command.setParentPurchaseOrderNo(request.getParentPurchaseOrderNo());
    command.setRelatedOrderNo(request.getRelatedOrderNo());
    command.setUserId(userId);
    command.setOrderStartTime(request.getOrderStartTime());
    command.setOrderEndTime(request.getOrderEndTime());
    command.setIsRelatedOrder(request.getIsRelatedOrder());
    command.setCurrentPage(request.getCurrentPage().intValue());
    command.setPageSize(request.getPageSize().intValue());

    // 调用领域服务查询采购单列表
    PurchaseOrderQueryResult queryResult = purchaseOrderDomainService.queryPurchaseOrders(command);

    // 转换DTO列表为响应对象列表
    List<PurchaseOrderResult> purchaseOrderResults = purchaseOrderAssembler.toResultList(
        queryResult.getPurchaseOrderAggregates());

    // 构建分页结果
    PageDTO<PurchaseOrderResult> pageDTO = new PageDTO<>();
    pageDTO.setData(purchaseOrderResults);
    pageDTO.setTotalCount(queryResult.getTotalCount());
    pageDTO.setTotalPage(queryResult.getTotalPage());
    pageDTO.setCurrentPage(queryResult.getCurrentPage().longValue());
    pageDTO.setPageSize(queryResult.getPageSize().longValue());

    return pageDTO;
  }

  @Override
  public Boolean updateAutoDeductionConfig(String userId, UpdateAutoDeductionConfigRequest request) {
    // 参数校验
    if (request == null) {
      throw new IllegalArgumentException("请求参数不能为空");
    }

    // 转换请求为领域命令
    UpdateAutoDeductionCommand command = new UpdateAutoDeductionCommand();
    command.setPurchaseOrderId(request.getPurchaseOrderId());
    command.setIsAutoPayment(request.getAutoDeduction());
    command.setOverduePaymentTime(request.getPaymentTimeout());
    command.setUserId(userId);

    // 调用领域服务更新自动扣款配置
    return purchaseOrderDomainService.updateAutoDeductionConfig(command);
  }

  @Override
  public List<String> batchRejectPurchaseOrder(String userId, BatchRejectPurchaseOrderRequest request) {
    // 参数校验
    if (request == null) {
      throw new IllegalArgumentException("请求参数不能为空");
    }

    // 转换请求为领域命令
    BatchRejectPurchaseOrderCommand command = new BatchRejectPurchaseOrderCommand();
    command.setPurchaseOrderIds(request.getPurchaseOrderIds());
    command.setCompanyCode(request.getCompanyCode());
    command.setOrganizationCode(request.getOrganizationCode());
    command.setPurchaseOrderLabel(request.getPurchaseOrderLabel());
    command.setState(request.getState());
    command.setPurchaseOrderNo(request.getPurchaseOrderNo());
    command.setParentPurchaseOrderNo(request.getParentPurchaseOrderNo());
    command.setRelatedOrderNo(request.getRelatedOrderNo());
    command.setOrderStartTime(request.getOrderStartTime());
    command.setOrderEndTime(request.getOrderEndTime());
    command.setRejectReason("用户驳回"); // 设置驳回原因
    command.setUserId(userId);

    // 调用领域服务批量驳回采购单
    return purchaseOrderDomainService.batchRejectPurchaseOrder(command);
  }

  @Override
  public PurchaseOrderInfoResult purchaseOrderInfoQuery(String userId, PurchaseOrderInfoRequest request) {

    // 转换请求为领域命令
    PurchaseOrderInfoQueryCommand command = new PurchaseOrderInfoQueryCommand();
    command.setPurchaseOrderNo(request.getPurchaseOrderNo());
    command.setUserId(userId);

    // 调用领域服务查询采购单详情
    PurchaseOrderInfoQueryResult queryResult = purchaseOrderDomainService.queryPurchaseOrderInfo(command);

    // 转换DTO为响应对象
    PurchaseOrderResult purchaseOrderResult = purchaseOrderAssembler.toResult(queryResult.getPurchaseOrderAggregate());

    // 构建查询结果
    PurchaseOrderInfoResult result = new PurchaseOrderInfoResult();
    result.setPurchaseOrder(purchaseOrderResult);

    return result;
  }

  @Override
  public String copyPurchaseOrder(String userId, CopyPurchaseOrderRequest request) {


    // 转换请求为领域命令
    CopyPurchaseOrderCommand command = new CopyPurchaseOrderCommand();
    command.setSourcePurchaseOrderNo(request.getPurchaseOrderIds());
    command.setUserId(userId);

    // 设置默认配置，可以根据实际需求调整
    command.setIsAutoPayment(true); // 默认自动支付
    command.setOverduePaymentTime(30); // 默认30天超时支付
    command.setAutoConfirm(false); // 默认不自动确认

    // 调用领域服务复制采购单
    return purchaseOrderDomainService.copyPurchaseOrder(command);
  }

  @Override
  public List<String> batchReConfirmOrder(String userId, BatchConfirmRequest request) {
    return Collections.emptyList();
  }
}
