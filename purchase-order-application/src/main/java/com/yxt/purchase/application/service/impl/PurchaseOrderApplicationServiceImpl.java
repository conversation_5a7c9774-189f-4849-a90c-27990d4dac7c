package com.yxt.purchase.application.service.impl;

import com.alibaba.excel.EasyExcel;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.purchase.application.convert.PurchaseOrderAssembler;
import com.yxt.purchase.application.entitys.ImportPurchaseOrderEntity;
import com.yxt.purchase.application.entitys.PurchaseOrderEntity;
import com.yxt.purchase.application.service.PurchaseOrderApplicationService;
import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.domain.command.PurchaseOrderCreateCommand;
import com.yxt.purchase.domain.command.PurchaseOrderQueryCommand;
import com.yxt.purchase.domain.entity.PurchaseOrder;
import com.yxt.purchase.domain.entity.PurchaseOrderDetailModel;
import com.yxt.purchase.domain.service.PurchaseOrderDomainService;
import com.yxt.purchase.domain.service.PurchaseOrderQueryResult;
import com.yxt.purchase.open.sdk.req.CreatePurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.ImportPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.PurchaseOrderListQueryRequest;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderResult;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * 采购单应用服务实现类
 */
@Slf4j
@Service
public class PurchaseOrderApplicationServiceImpl implements PurchaseOrderApplicationService {

  @Autowired
  private PurchaseOrderDomainService purchaseOrderDomainService;

  @Autowired
  private PurchaseOrderAssembler purchaseOrderAssembler;

  @Override
  public List<String> importPurchaseOrder(
      MultipartFile file, String userId, ImportPurchaseOrderRequest request) throws IOException {
    // 读取Excel文件
    PurchaseOrderExcelListener purchaseOrderExcelListener = new PurchaseOrderExcelListener();
    EasyExcel.read(file.getInputStream(), ImportPurchaseOrderEntity.class,
        purchaseOrderExcelListener).sheet().doRead();
    // 创建command 执行新增操作
    PurchaseOrderCreateCommand purchaseOrderCreateCommand = new PurchaseOrderCreateCommand();
    purchaseOrderCreateCommand.setAutoConfirm(request.getAutoConfirm());
    purchaseOrderCreateCommand.setIsAutoPayment(request.getIsAutoPayment());
    purchaseOrderCreateCommand.setOverduePaymentTime(request.getOverduePaymentTime());
    purchaseOrderCreateCommand.setUserId(userId);
    purchaseOrderCreateCommand.setItems(purchaseOrderExcelListener.getResult());
    // 创建PurchaseOrder
    return purchaseOrderDomainService.createPurchaseOrder(purchaseOrderCreateCommand);
  }

  @Override
  public List<String> createPurchaseOrder(String userId, CreatePurchaseOrderRequest request) {
    PurchaseOrderCreateCommand purchaseOrderCreateCommand = new PurchaseOrderCreateCommand();
    purchaseOrderCreateCommand.setAutoConfirm(request.getAutoConfirm());
    purchaseOrderCreateCommand.setIsAutoPayment(request.getIsAutoPayment());
    purchaseOrderCreateCommand.setOverduePaymentTime(request.getOverduePaymentTime());
    purchaseOrderCreateCommand.setUserId(userId);
    purchaseOrderCreateCommand.setItems(request.getItems().stream().map(item -> {
      PurchaseOrderDetailModel purchaseOrderDetailModel = new PurchaseOrderDetailModel();
      purchaseOrderDetailModel.setErpCode(item.getErpCode());
      purchaseOrderDetailModel.setOrganizationCode(item.getOrganizationCode());
      purchaseOrderDetailModel.setQty(item.getQty().toString());
      return purchaseOrderDetailModel;

    }).collect(
        Collectors.toList()));
    // 创建PurchaseOrder
    return purchaseOrderDomainService.createPurchaseOrder(purchaseOrderCreateCommand);
  }

  @Override
  public PageDTO<PurchaseOrderResult> queryPurchaseOrders(String userId,
      PurchaseOrderListQueryRequest request) {
    // 参数校验
    if (request == null) {
      throw new IllegalArgumentException("查询请求不能为空");
    }

    // 转换请求为领域命令
    PurchaseOrderQueryCommand command = new PurchaseOrderQueryCommand();
    command.setCompanyCode(request.getCompanyCode());
    command.setOrganizationCode(request.getOrganizationCode());
    command.setPurchaseOrderLabel(request.getPurchaseOrderLabel());
    command.setState(request.getState());
    command.setPurchaseOrderNo(request.getPurchaseOrderNo());
    command.setParentPurchaseOrderNo(request.getParentPurchaseOrderNo());
    command.setRelatedOrderNo(request.getRelatedOrderNo());
    command.setUserId(userId);
    command.setOrderStartTime(request.getOrderStartTime());
    command.setOrderEndTime(request.getOrderEndTime());
    command.setIsRelatedOrder(request.getIsRelatedOrder());
    command.setCurrentPage(request.getCurrentPage().intValue());
    command.setPageSize(request.getPageSize().intValue());

    // 调用领域服务查询采购单列表
    PurchaseOrderQueryResult queryResult = purchaseOrderDomainService.queryPurchaseOrders(command);

    // 转换聚合根列表为DTO列表
    List<PurchaseOrderEntity> purchaseOrderEntities = purchaseOrderAssembler.toDTOList(
        queryResult.getPurchaseOrderAggregates());

    // 转换DTO列表为响应对象列表
    List<PurchaseOrderResult> purchaseOrderResults = purchaseOrderAssembler.toResultList(
        queryResult.getPurchaseOrderAggregates());

    // 构建分页结果
    PageDTO<PurchaseOrderResult> pageDTO = new PageDTO<>();
    pageDTO.setData(purchaseOrderResults);
    pageDTO.setTotalCount(queryResult.getTotalCount());
    pageDTO.setTotalPage(queryResult.getTotalPage());
    pageDTO.setCurrentPage(queryResult.getCurrentPage().longValue());
    pageDTO.setPageSize(queryResult.getPageSize().longValue());

    return pageDTO;
  }
}
