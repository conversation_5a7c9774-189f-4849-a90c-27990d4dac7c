package com.yxt.purchase.application.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.org.read.opensdk.emp.dto.response.EmployeeResDTO;
import com.yxt.purchase.application.convert.PurchaseOrderAssembler;
import com.yxt.purchase.application.entitys.ImportPurchaseOrderEntity;
import com.yxt.purchase.application.service.PurchaseOrderApplicationService;
import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.domain.command.BatchReConfirmCommand;
import com.yxt.purchase.domain.command.BatchRejectPurchaseOrderCommand;
import com.yxt.purchase.domain.command.CopyPurchaseOrderCommand;
import com.yxt.purchase.domain.command.PurchaseOrderCreateCommand;
import com.yxt.purchase.domain.command.PurchaseOrderInfoQueryCommand;
import com.yxt.purchase.domain.command.PurchaseOrderQueryCommand;
import com.yxt.purchase.domain.command.UpdateAutoDeductionCommand;
import com.yxt.purchase.domain.command.UpdatePurchaseOrderCancelCommand;
import com.yxt.purchase.domain.command.UpdatePurchaseOrderCompletedCommand;
import com.yxt.purchase.domain.entity.PurchaseOrderDetailModel;
import com.yxt.purchase.domain.service.PurchaseOrderDomainService;
import com.yxt.purchase.domain.entity.PurchaseOrderInfoQueryResult;
import com.yxt.purchase.domain.entity.PurchaseOrderQueryResult;
import com.yxt.purchase.infrastructure.api.MiddleBaseInfoService;
import com.yxt.purchase.open.sdk.req.BatchConfirmRequest;
import com.yxt.purchase.open.sdk.req.BatchRejectPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.CopyPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.CreatePurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.ImportPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.PurchaseOrderInfoRequest;
import com.yxt.purchase.open.sdk.req.PurchaseOrderListQueryRequest;
import com.yxt.purchase.open.sdk.req.UpdateAutoDeductionConfigRequest;
import com.yxt.purchase.open.sdk.req.UpdatePurchaseOrderCancelRequest;
import com.yxt.purchase.open.sdk.req.UpdatePurchaseOrderCompletedRequest;
import com.yxt.purchase.open.sdk.resp.BizLogDto;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderInfoResult;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderResult;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * 采购单应用服务实现类
 */
@Slf4j
@Service
public class PurchaseOrderApplicationServiceImpl implements PurchaseOrderApplicationService {

  @Autowired
  private PurchaseOrderDomainService purchaseOrderDomainService;

  @Autowired
  private PurchaseOrderAssembler purchaseOrderAssembler;

  @Autowired
  private MiddleBaseInfoService middleBaseInfoService;

  @Override
  public List<String> importPurchaseOrder(MultipartFile file, String userId,
      ImportPurchaseOrderRequest request) throws IOException {
    // 读取Excel文件
    PurchaseOrderExcelListener purchaseOrderExcelListener = new PurchaseOrderExcelListener();
    EasyExcel.read(file.getInputStream(), ImportPurchaseOrderEntity.class,
        purchaseOrderExcelListener).sheet().doRead();
    // 创建command 执行新增操作
    PurchaseOrderCreateCommand purchaseOrderCreateCommand = new PurchaseOrderCreateCommand();
    purchaseOrderCreateCommand.setAutoConfirm(request.getAutoConfirm());
    purchaseOrderCreateCommand.setIsAutoPayment(request.getIsAutoPayment());
    purchaseOrderCreateCommand.setOverduePaymentTime(request.getOverduePaymentTime());
    purchaseOrderCreateCommand.setUserId(userId);
    purchaseOrderCreateCommand.setItems(purchaseOrderExcelListener.getResult());
    if (CollUtil.isNotEmpty(purchaseOrderCreateCommand.getItems())
        && purchaseOrderCreateCommand.getItems().size() > 10000) {
      throw new RuntimeException("单次导入采购单不能超过10000条");
    }
    // 创建PurchaseOrder
    return purchaseOrderDomainService.createPurchaseOrder(purchaseOrderCreateCommand);
  }

  @Override
  public List<String> createPurchaseOrder(String userId, CreatePurchaseOrderRequest request) {
    PurchaseOrderCreateCommand purchaseOrderCreateCommand = new PurchaseOrderCreateCommand();
    purchaseOrderCreateCommand.setAutoConfirm(request.getAutoConfirm());
    purchaseOrderCreateCommand.setIsAutoPayment(request.getIsAutoPayment());
    purchaseOrderCreateCommand.setOverduePaymentTime(request.getOverduePaymentTime());
    purchaseOrderCreateCommand.setUserId(userId);
    purchaseOrderCreateCommand.setItems(request.getItems().stream().map(item -> {
      PurchaseOrderDetailModel purchaseOrderDetailModel = new PurchaseOrderDetailModel();
      purchaseOrderDetailModel.setErpCode(item.getErpCode());
      purchaseOrderDetailModel.setOrganizationCode(item.getOrganizationCode());
      purchaseOrderDetailModel.setQty(item.getQty().toString());
      return purchaseOrderDetailModel;

    }).collect(Collectors.toList()));
    // 创建PurchaseOrder
    return purchaseOrderDomainService.createPurchaseOrder(purchaseOrderCreateCommand);
  }

  @Override
  public PageDTO<PurchaseOrderResult> queryPurchaseOrders(String userId,
      PurchaseOrderListQueryRequest request) {
    // 参数校验
    if (request == null) {
      throw new IllegalArgumentException("查询请求不能为空");
    }

    // 转换请求为领域命令
    PurchaseOrderQueryCommand command = new PurchaseOrderQueryCommand();
    command.setCompanyCode(request.getCompanyCode());
    command.setOrganizationCode(request.getOrganizationCode());
    command.setPurchaseOrderLabel(request.getPurchaseOrderLabel());
    command.setState(request.getState());
    command.setPurchaseOrderNo(request.getPurchaseOrderNo());
    command.setParentPurchaseOrderNo(request.getParentPurchaseOrderNo());
    command.setRelatedOrderNo(request.getRelatedOrderNo());
    command.setUserId(userId);
    command.setCreatedBy(request.getCreatedBy());
    command.setOrderStartTime(request.getOrderStartTime());
    command.setOrderEndTime(request.getOrderEndTime());
    command.setIsRelatedOrder(request.getIsRelatedOrder());
    command.setCurrentPage(request.getCurrentPage().intValue());
    command.setPageSize(request.getPageSize().intValue());

    // 调用领域服务查询采购单列表
    PurchaseOrderQueryResult queryResult = purchaseOrderDomainService.queryPurchaseOrders(command);

    // 转换DTO列表为响应对象列表
    List<PurchaseOrderResult> purchaseOrderResults = purchaseOrderAssembler.toResultList(
        queryResult.getPurchaseOrderAggregates());
    List<String> userIds = new ArrayList<>();
    purchaseOrderResults.forEach(item -> {
      userIds.add(item.getCreatedUserId());
      userIds.add(item.getConfirmUserId());
      userIds.add(item.getUpdatedUserId());
    });

    Map<String, EmployeeResDTO> employeeResDTOList = middleBaseInfoService.getSysEmployeeInfo(
        userIds, "500001");
    for (PurchaseOrderResult purchaseOrderResult : purchaseOrderResults) {
      initPurchaseOrderResultUserName(purchaseOrderResult, employeeResDTOList);
    }

    // 构建分页结果
    PageDTO<PurchaseOrderResult> pageDTO = new PageDTO<>();
    pageDTO.setData(purchaseOrderResults);
    pageDTO.setTotalCount(queryResult.getTotalCount());
    pageDTO.setTotalPage(queryResult.getTotalPage());
    pageDTO.setCurrentPage(queryResult.getCurrentPage().longValue());
    pageDTO.setPageSize(queryResult.getPageSize().longValue());

    return pageDTO;
  }

  @Override
  public Boolean updateAutoDeductionConfig(String userId,
      UpdateAutoDeductionConfigRequest request) {
    // 参数校验
    if (request == null) {
      throw new IllegalArgumentException("请求参数不能为空");
    }

    // 转换请求为领域命令
    UpdateAutoDeductionCommand command = new UpdateAutoDeductionCommand();
    command.setPurchaseOrderId(request.getPurchaseOrderId());
    command.setIsAutoPayment(request.getAutoDeduction());
    command.setOverduePaymentTime(
        request.getPaymentTimeout() != null ? request.getPaymentTimeout().intValue() : 0);
    command.setUserId(userId);

    // 调用领域服务更新自动扣款配置
    return purchaseOrderDomainService.updateAutoDeductionConfig(command);
  }

  @Override
  public List<String> batchRejectPurchaseOrder(String userId,
      BatchRejectPurchaseOrderRequest request) {
    // 参数校验
    if (request == null) {
      throw new IllegalArgumentException("请求参数不能为空");
    }

    // 转换请求为领域命令
    BatchRejectPurchaseOrderCommand command = new BatchRejectPurchaseOrderCommand();
    command.setPurchaseOrderIds(request.getPurchaseOrderIds());
    command.setCompanyCode(request.getCompanyCode());
    command.setOrganizationCode(request.getOrganizationCode());
    command.setPurchaseOrderLabel(request.getPurchaseOrderLabel());
    command.setState(request.getState());
    command.setPurchaseOrderNo(request.getPurchaseOrderNo());
    command.setParentPurchaseOrderNo(request.getParentPurchaseOrderNo());
    command.setRelatedOrderNo(request.getRelatedOrderNo());
    command.setOrderStartTime(request.getOrderStartTime());
    command.setOrderEndTime(request.getOrderEndTime());
    command.setRejectReason("用户驳回"); // 设置驳回原因
    command.setUserId(userId);
    command.setCreatedBy(request.getCreatedBy());
    // 调用领域服务批量驳回采购单
    return purchaseOrderDomainService.batchRejectPurchaseOrder(command);
  }

  @Override
  public PurchaseOrderInfoResult purchaseOrderInfoQuery(String userId,
      PurchaseOrderInfoRequest request) {

    // 转换请求为领域命令
    PurchaseOrderInfoQueryCommand command = new PurchaseOrderInfoQueryCommand();
    command.setPurchaseOrderNo(request.getPurchaseOrderNo());
    command.setUserId(userId);

    // 调用领域服务查询采购单详情
    PurchaseOrderInfoQueryResult queryResult = purchaseOrderDomainService.queryPurchaseOrderInfo(
        command);

    // 转换DTO为响应对象
    PurchaseOrderResult purchaseOrderResult = purchaseOrderAssembler.toResult(
        queryResult.getPurchaseOrderAggregate());
    // 处理人员信息
    Map<String, EmployeeResDTO> employeeResDTOList = middleBaseInfoService.getSysEmployeeInfo(
        Arrays.asList(purchaseOrderResult.getCreatedUserId(),
            purchaseOrderResult.getConfirmUserId(), purchaseOrderResult.getUpdatedUserId()),
        "500001");

    initPurchaseOrderResultUserName(purchaseOrderResult, employeeResDTOList);
    // 构建查询结果
    PurchaseOrderInfoResult result = new PurchaseOrderInfoResult();
    result.setPurchaseOrder(purchaseOrderResult);
    result.setOperationLogs(BeanUtil.copyToList(queryResult.getOperationLogs(), BizLogDto.class));

    return result;
  }

  private void initPurchaseOrderResultUserName(PurchaseOrderResult purchaseOrderResult,
      Map<String, EmployeeResDTO> employeeResDTOList) {
    if (CollUtil.isEmpty(employeeResDTOList)) {
      return;
    }
    if (StrUtil.isNotBlank(purchaseOrderResult.getCreatedUserId())
        && employeeResDTOList.containsKey(purchaseOrderResult.getCreatedUserId())) {
      purchaseOrderResult.setCreatedBy(String.format("(%s)%s",
          employeeResDTOList.get(purchaseOrderResult.getCreatedUserId()).getEmpCode(),
          employeeResDTOList.get(purchaseOrderResult.getCreatedUserId()).getEmpName()));
    }
    if (StrUtil.isNotBlank(purchaseOrderResult.getConfirmUserId())
        && employeeResDTOList.containsKey(purchaseOrderResult.getConfirmUserId())) {
      purchaseOrderResult.setConfirmBy(String.format("(%s)%s",
          employeeResDTOList.get(purchaseOrderResult.getConfirmUserId()).getEmpCode(),
          employeeResDTOList.get(purchaseOrderResult.getConfirmUserId()).getEmpName()));
    }
    if (StrUtil.isNotBlank(purchaseOrderResult.getUpdatedUserId())
        && employeeResDTOList.containsKey(purchaseOrderResult.getUpdatedUserId())) {
      purchaseOrderResult.setUpdatedBy(String.format("(%s)%s",
          employeeResDTOList.get(purchaseOrderResult.getUpdatedUserId()).getEmpCode(),
          employeeResDTOList.get(purchaseOrderResult.getUpdatedUserId()).getEmpName()));
    }
  }

  @Override
  public String copyPurchaseOrder(String userId, CopyPurchaseOrderRequest request) {

    // 转换请求为领域命令
    CopyPurchaseOrderCommand command = new CopyPurchaseOrderCommand();
    command.setSourcePurchaseOrderId(request.getPurchaseOrderIds());
    command.setUserId(userId);

    // 调用领域服务复制采购单
    return purchaseOrderDomainService.copyPurchaseOrder(command);
  }

  @Override
  public List<String> batchReConfirmOrder(String userId, BatchConfirmRequest request) {
    BatchReConfirmCommand command = new BatchReConfirmCommand();
    command.setCompanyCode(request.getCompanyCode());
    command.setOrganizationCode(request.getOrganizationCode());
    command.setPurchaseOrderLabel(request.getPurchaseOrderLabel());
    command.setPurchaseOrderIds(request.getPurchaseOrderIds());
    command.setState(request.getState());
    command.setPurchaseOrderNo(request.getPurchaseOrderNo());
    command.setParentPurchaseOrderNo(request.getParentPurchaseOrderNo());
    command.setRelatedOrderNo(request.getRelatedOrderNo());
    command.setUserId(userId);
    command.setCreatedBy(request.getCreatedBy());
    command.setOrderStartTime(request.getOrderStartTime());
    command.setOrderEndTime(request.getOrderEndTime());
    command.setIsRelatedOrder(request.getIsRelatedOrder());

    return purchaseOrderDomainService.batchReConfirmOrder(command);
  }

  @Override
  public void updatePurchaseOrderCompleted(UpdatePurchaseOrderCompletedRequest request) {
    // 参数校验
    if (request == null) {
      throw new IllegalArgumentException("请求参数不能为空");
    }
    if (StrUtil.isBlank(request.getPurchaseOrderNo())) {
      throw new IllegalArgumentException("采购单号不能为空");
    }

    // 转换请求为领域命令
    UpdatePurchaseOrderCompletedCommand command = new UpdatePurchaseOrderCompletedCommand();
    command.setPurchaseOrderNo(request.getPurchaseOrderNo());
    command.setCompletedRemark(request.getCompletedRemark());
    command.setUserId("system");
    // 调用领域服务更新采购单状态
    PurchaseOrderAggregate updatedAggregate = purchaseOrderDomainService.updatePurchaseOrderCompleted(command);
  }

  @Override
  public Boolean updatePurchaseOrderCancel(UpdatePurchaseOrderCancelRequest request) {
    // 参数校验
    if (request == null) {
      throw new IllegalArgumentException("请求参数不能为空");
    }
    if (StrUtil.isBlank(request.getPurchaseOrderNo())) {
      throw new IllegalArgumentException("采购单号不能为空");
    }

    // 转换请求为领域命令
    UpdatePurchaseOrderCancelCommand command = new UpdatePurchaseOrderCancelCommand();
    command.setPurchaseOrderNo(request.getPurchaseOrderNo());
    command.setCancelReason(request.getCancelReason());
    command.setUserId("system");
    // 调用领域服务取消采购单
    PurchaseOrderAggregate updatedAggregate = purchaseOrderDomainService.updatePurchaseOrderCancel(command);
    return updatedAggregate != null;
  }
}
