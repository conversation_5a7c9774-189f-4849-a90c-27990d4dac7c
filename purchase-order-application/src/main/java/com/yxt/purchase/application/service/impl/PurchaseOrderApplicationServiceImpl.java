package com.yxt.purchase.application.service.impl;

import com.alibaba.excel.EasyExcel;
import com.yxt.purchase.application.entitys.ImportPurchaseOrderEntity;
import com.yxt.purchase.application.service.PurchaseOrderApplicationService;
import com.yxt.purchase.domain.command.PurchaseOrderCreateCommand;
import com.yxt.purchase.domain.entity.PurchaseOrder;
import com.yxt.purchase.domain.entity.PurchaseOrderDetailModel;
import com.yxt.purchase.domain.service.PurchaseOrderDomainService;
import com.yxt.purchase.open.sdk.req.CreatePurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.ImportPurchaseOrderRequest;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * 采购单应用服务实现类
 */
@Slf4j
@Service
@AllArgsConstructor
public class PurchaseOrderApplicationServiceImpl implements PurchaseOrderApplicationService {

  @Autowired
  private PurchaseOrderDomainService purchaseOrderDomainService;

  @Override
  public List<String> importPurchaseOrder(
      MultipartFile file, String userId, ImportPurchaseOrderRequest request) throws IOException {
    // 读取Excel文件
    PurchaseOrderExcelListener purchaseOrderExcelListener = new PurchaseOrderExcelListener();
    EasyExcel.read(file.getInputStream(), ImportPurchaseOrderEntity.class,
        purchaseOrderExcelListener).sheet().doRead();
    // 创建command 执行新增操作
    PurchaseOrderCreateCommand purchaseOrderCreateCommand = new PurchaseOrderCreateCommand();
    purchaseOrderCreateCommand.setAutoConfirm(request.getAutoConfirm());
    purchaseOrderCreateCommand.setIsAutoPayment(request.getIsAutoPayment());
    purchaseOrderCreateCommand.setOverduePaymentTime(request.getOverduePaymentTime());
    purchaseOrderCreateCommand.setUserId(userId);
    purchaseOrderCreateCommand.setItems(purchaseOrderExcelListener.getResult());
    // 创建PurchaseOrder
    return purchaseOrderDomainService.createPurchaseOrder(purchaseOrderCreateCommand);
  }

  @Override
  public List<String> createPurchaseOrder(String userId, CreatePurchaseOrderRequest request) {
    PurchaseOrderCreateCommand purchaseOrderCreateCommand = new PurchaseOrderCreateCommand();
    purchaseOrderCreateCommand.setAutoConfirm(request.getAutoConfirm());
    purchaseOrderCreateCommand.setIsAutoPayment(request.getIsAutoPayment());
    purchaseOrderCreateCommand.setOverduePaymentTime(request.getOverduePaymentTime());
    purchaseOrderCreateCommand.setUserId(userId);
    purchaseOrderCreateCommand.setItems(request.getItems().stream().map(item -> {
      PurchaseOrderDetailModel purchaseOrderDetailModel = new PurchaseOrderDetailModel();
      purchaseOrderDetailModel.setErpCode(item.getErpCode());
      purchaseOrderDetailModel.setOrganizationCode(item.getOrganizationCode());
      purchaseOrderDetailModel.setQty(item.getQty().toString());
      return purchaseOrderDetailModel;

    }).collect(
        Collectors.toList()));
    // 创建PurchaseOrder
    return purchaseOrderDomainService.createPurchaseOrder(purchaseOrderCreateCommand);
  }
}
