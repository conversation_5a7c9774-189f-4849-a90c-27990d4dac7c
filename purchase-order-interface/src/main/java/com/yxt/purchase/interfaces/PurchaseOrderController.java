package com.yxt.purchase.interfaces;

import com.yxt.lang.constants.response.ResponseCodeType;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.purchase.application.convert.PurchaseOrderDetailAssembler;
import com.yxt.purchase.application.entitys.PurchaseOrderDetailEntity;
import com.yxt.purchase.application.service.PurchaseOrderApplicationService;
import com.yxt.purchase.application.service.PurchaseOrderDetailApplicationService;
import com.yxt.purchase.open.sdk.PurchaseOrderApi;
import com.yxt.purchase.open.sdk.req.BatchConfirmRequest;
import com.yxt.purchase.open.sdk.req.BatchRejectPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.CopyPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.CreatePurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.ImportPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.PurchaseOrderDetailQueryRequest;
import com.yxt.purchase.open.sdk.req.PurchaseOrderInfoRequest;
import com.yxt.purchase.open.sdk.req.PurchaseOrderListQueryRequest;
import com.yxt.purchase.open.sdk.req.RemovePurchaseDetailByIdRequest;
import com.yxt.purchase.open.sdk.req.UpdateAutoDeductionConfigRequest;
import com.yxt.purchase.open.sdk.req.UpdatePurchaseOrderCancelRequest;
import com.yxt.purchase.open.sdk.req.UpdatePurchaseOrderCompletedRequest;
import com.yxt.purchase.open.sdk.req.UpdatePurchaseOrderDetailRequest;
import com.yxt.purchase.open.sdk.resp.CreatePurchaseOrderResult;
import com.yxt.purchase.open.sdk.resp.ImportHistoryQueryResult;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderDetailResult;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderInfoResult;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderResult;
import com.yxt.starter.controller.AbstractController;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@AllArgsConstructor
public class PurchaseOrderController extends AbstractController implements PurchaseOrderApi {

  @Resource
  private PurchaseOrderApplicationService purchaseOrderApplicationService;

  @Resource
  private PurchaseOrderDetailApplicationService purchaseOrderDetailApplicationService;

  @Resource
  private PurchaseOrderDetailAssembler purchaseOrderDetailAssembler;

  @Override
  public ResponseBase<CreatePurchaseOrderResult> importPurchaseOrder(String userId,
      MultipartFile file,
      Boolean isAutoPayment, Integer overduePaymentTime, Boolean autoConfirm) {


    try {
      ImportPurchaseOrderRequest request = new ImportPurchaseOrderRequest();
      request.setIsAutoPayment(isAutoPayment);
      request.setOverduePaymentTime(overduePaymentTime);
      request.setAutoConfirm(autoConfirm);
      List<String> result = purchaseOrderApplicationService.importPurchaseOrder(file, userId,
          request);
      CreatePurchaseOrderResult purchaseOrderResult = new CreatePurchaseOrderResult();
      purchaseOrderResult.setPurchaseOrderIds(result);
      purchaseOrderResult.setIsSuccess(Boolean.TRUE);
      purchaseOrderResult.setMsg("创建采购单成功");
      return ResponseBase.success(purchaseOrderResult);
    } catch (Exception ex) {
      log.error("导入采购单失败", ex);
      return
          ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION, ex.getMessage());
    }
  }

//  @Async
//  public void asyncImportPurchaseOrder(String userId,
//      MultipartFile file,
//      Boolean isAutoPayment, Integer overduePaymentTime, Boolean autoConfirm){
//
//    try {
//      ImportPurchaseOrderRequest request = new ImportPurchaseOrderRequest();
//      request.setIsAutoPayment(isAutoPayment);
//      request.setOverduePaymentTime(overduePaymentTime);
//      request.setAutoConfirm(autoConfirm);
//      List<String> result = purchaseOrderApplicationService.importPurchaseOrder(file, userId,
//          request);
//      CreatePurchaseOrderResult purchaseOrderResult = new CreatePurchaseOrderResult();
//      purchaseOrderResult.setPurchaseOrderIds(result);
//      purchaseOrderResult.setIsSuccess(Boolean.TRUE);
//      purchaseOrderResult.setMsg("创建采购单成功");
//    } catch (Exception ex) {
//      log.error("导入采购单失败", ex);
//
//    }
//  }

  @Override
  public ResponseBase<CreatePurchaseOrderResult> createPurchaseOrder(String userId,
      CreatePurchaseOrderRequest request) {
    List<String> result = purchaseOrderApplicationService.createPurchaseOrder(userId,
        request);
    CreatePurchaseOrderResult purchaseOrderResult = new CreatePurchaseOrderResult();
    purchaseOrderResult.setPurchaseOrderIds(result);
    purchaseOrderResult.setIsSuccess(Boolean.TRUE);
    purchaseOrderResult.setMsg("创建采购单成功");
    return ResponseBase.success(purchaseOrderResult);
  }

  @Override
  public ResponseBase<PageDTO<PurchaseOrderResult>> queryPurchaseOrder(String userId,
      PurchaseOrderListQueryRequest request) {
    try {
      // 调用应用服务查询采购单列表
      PageDTO<PurchaseOrderResult> pageDTO = purchaseOrderApplicationService.queryPurchaseOrders(
          userId, request);
      return ResponseBase.success(pageDTO);
    } catch (Exception e) {
      log.error("查询采购单失败", e);
      return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION, e.getMessage());
    }
  }

  @Override
  public ResponseBase<Boolean> updateAutoDeductionConfig(String userId,
      UpdateAutoDeductionConfigRequest request) {
    try {
      // 调用应用服务更新自动扣款配置
      Boolean result = purchaseOrderApplicationService.updateAutoDeductionConfig(userId, request);
      return ResponseBase.success(result);
    } catch (Exception e) {
      log.error("更新自动扣款配置失败", e);
      return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION, e.getMessage());
    }
  }

  @Override
  public ResponseBase<PageDTO<PurchaseOrderDetailResult>> queryPurchaseOrderDetail(
      String userId,
      PurchaseOrderDetailQueryRequest request) {
    try {
      // 调用应用服务查询采购单明细
      PageDTO<PurchaseOrderDetailResult> pageDTO = purchaseOrderDetailApplicationService.queryPurchaseOrderDetails(
          userId, request);
      return ResponseBase.success(pageDTO);
    } catch (Exception e) {
      log.error("查询采购单明细失败", e);
      return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION, e.getMessage());
    }
  }

  @Override
  public ResponseBase<PurchaseOrderDetailResult> updatePurchaseOrderDetail(String userId,
      UpdatePurchaseOrderDetailRequest request) {
    try {
      // 调用应用服务更新采购单明细
      PurchaseOrderDetailEntity detailEntity = purchaseOrderDetailApplicationService.updateDetail(
          userId,
          request);

      // 使用转换器将实体转换为响应对象
      PurchaseOrderDetailResult result = purchaseOrderDetailAssembler.toResult(detailEntity);

      return ResponseBase.success(result);
    } catch (Exception e) {
      log.error("更新采购单明细失败", e);
      return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION, e.getMessage());
    }
  }

  @Override
  public ResponseBase<Boolean> removePurchaseOrderDetail(String userId,
      RemovePurchaseDetailByIdRequest request) {
    try {
      // 调用应用服务删除采购单明细
      boolean result = purchaseOrderDetailApplicationService.deleteDetail(userId, request);
      return ResponseBase.success(result);
    } catch (Exception e) {
      log.error("删除采购单明细失败", e);
      return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION, e.getMessage());
    }
  }

  /**
   * 复制采购单信息
   *
   * @param userId  当前用户
   * @param request 被复制的源采购单号
   * @return 复制新增的采购单号
   */
  @Override
  public ResponseBase<String> copyPurchaseOrder(String userId,
      CopyPurchaseOrderRequest request) {
    try {
      // 调用应用服务复制采购单
      String newPurchaseOrderNo = purchaseOrderApplicationService.copyPurchaseOrder(userId,
          request);
      return ResponseBase.success(newPurchaseOrderNo);
    } catch (Exception e) {
      log.error("复制采购单失败", e);
      return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION, e.getMessage());
    }
  }

  /**
   * 批量确认采购单
   *
   * @param userId  当前用户
   * @param request 采购单号集合
   * @return 处理成功的采购单号集合
   */
  @Override
  public ResponseBase<List<String>> batchConfirmPurchaseOrder(String userId,
      BatchConfirmRequest request) {
    try {
      return ResponseBase.success(
          purchaseOrderApplicationService.batchReConfirmOrder(userId, request));
    } catch (Exception e) {
      log.error("批量确认采购单失败", e);
      return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION, e.getMessage());
    }

  }

  /**
   * 设置采购单为已取消
   *
   * @param userId
   * @param request
   * @return
   */
  @Override
  public ResponseBase<List<String>> batchRejectPurchaseOrder(String userId,
      BatchRejectPurchaseOrderRequest request) {
    try {
      // 调用应用服务批量驳回采购单
      List<String> rejectedOrderNos = purchaseOrderApplicationService.batchRejectPurchaseOrder(
          userId, request);
      return ResponseBase.success(rejectedOrderNos);
    } catch (Exception e) {
      log.error("批量驳回采购单失败", e);
      return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION, e.getMessage());
    }
  }

  @Override
  public ResponseBase<PurchaseOrderInfoResult> purchaseOrderInfoQuery(String userId,
      PurchaseOrderInfoRequest request) {
    try {
      // 调用应用服务查询采购单详情
      PurchaseOrderInfoResult result = purchaseOrderApplicationService.purchaseOrderInfoQuery(
          userId, request);
      return ResponseBase.success(result);
    } catch (Exception e) {
      log.error("查询采购单详情失败", e);
      return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION, e.getMessage());
    }
  }

  @Override
  public ResponseBase<Boolean> updatePurchaseOrderCompleted(
      UpdatePurchaseOrderCompletedRequest request) {
    try {
      // 调用应用服务更新采购单为已完成状态
      purchaseOrderApplicationService.updatePurchaseOrderCompleted(request);
      return ResponseBase.success(Boolean.TRUE);
    } catch (Exception e) {
      log.error("更新采购单为已完成状态失败", e);
      return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION, e.getMessage());
    }
  }

  @Override
  public ResponseBase<Boolean> updatePurchaseOrderCancel(UpdatePurchaseOrderCancelRequest request) {
    try {
      // 调用应用服务取消采购单
      Boolean result = purchaseOrderApplicationService.updatePurchaseOrderCancel(request);
      return ResponseBase.success(result);
    } catch (Exception e) {
      log.error("取消采购单失败", e);
      return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION, e.getMessage());
    }
  }

  @Override
  public ResponseBase<List<ImportHistoryQueryResult>> importHistoryQuery(String userId) {
    try {
      // 调用应用服务查询导入历史
      List<ImportHistoryQueryResult> results = purchaseOrderApplicationService.importHistoryQuery(userId);
      return ResponseBase.success(results);
    } catch (Exception e) {
      log.error("查询导入历史失败", e);
      return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION, e.getMessage());
    }
  }
}
