package com.yxt.purchase.interfaces;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.purchase.application.service.PurchaseOrderApplicationService;
import com.yxt.purchase.open.sdk.PurchaseOrderApi;
import com.yxt.purchase.open.sdk.req.BatchConfirmRequest;
import com.yxt.purchase.open.sdk.req.BatchReCreateRequest;
import com.yxt.purchase.open.sdk.req.BatchRejectPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.CopyPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.CreatePurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.ImportPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.PurchaseOrderDetailQueryRequest;
import com.yxt.purchase.open.sdk.req.PurchaseOrderInfoRequest;
import com.yxt.purchase.open.sdk.req.PurchaseOrderListQueryRequest;
import com.yxt.purchase.open.sdk.req.RemovePurchaseDetailByIdRequest;
import com.yxt.purchase.open.sdk.req.UpdateAutoDeductionConfigRequest;
import com.yxt.purchase.open.sdk.req.UpdatePurchaseOrderDetailRequest;
import com.yxt.purchase.open.sdk.resp.BizLogDto;
import com.yxt.purchase.open.sdk.resp.CreatePurchaseOrderResult;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderDetailResult;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderInfoResult;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderResult;
import com.yxt.starter.controller.AbstractController;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@AllArgsConstructor
public class PurchaseOrderController extends AbstractController implements PurchaseOrderApi {

  @Resource
  private PurchaseOrderApplicationService purchaseOrderApplicationService;

  /**
   * 生成模拟的采购单结果
   */
  private PurchaseOrderResult createMockPurchaseOrder(String purchaseOrderNo, String state) {
    PurchaseOrderResult result = new PurchaseOrderResult();
    result.setId(10000L + (long) (Math.random() * 1000));
    result.setPurchaseOrderNo(purchaseOrderNo);
    result.setState(state);
    result.setRelatedOrderNo("RO" + purchaseOrderNo.substring(2));
    result.setParentPurchaseOrderNo("");
    result.setAutoPayment(true);
    result.setOverduePaymentTime(7);
    result.setCompanyCode("COMP001");
    result.setCompanyName("总公司");
    result.setOrganizationCode("ORG001");
    result.setOrganizationName("北京门店");
    result.setPurchaseOrderLabel("CONSUMABLES");

    if ("CONFIRMED".equals(state) || "FINISHED".equals(state)) {
      result.setConfirmBy("李四");
      result.setConfirmUserId("USER002");
      result.setConfirmTime(LocalDateTime.now().minusDays(1).toString());
    }

    result.setCreatedBy("张三");
    result.setCreatedUserId("USER001");
    result.setCreatedTime(LocalDateTime.now().minusDays(2).toString());
    result.setUpdatedTime(LocalDateTime.now().toString());
    result.setRemark("办公用品采购");

    return result;
  }

  /**
   * 生成模拟的采购单明细结果
   */
  private PurchaseOrderDetailResult createMockPurchaseOrderDetail(String purchaseOrderNo, Long id) {
    PurchaseOrderDetailResult result = new PurchaseOrderDetailResult();
    result.setId(id);
    result.setPurchaseOrderNo(purchaseOrderNo);
    result.setErpCode("SKU" + (1000 + id % 100));
    result.setErpName("商品" + (1000 + id % 100));
    result.setCommoditySpec("规格型号" + id % 10);
    result.setManufacture("生产厂家" + id % 5);
    result.setCommodityCount(new BigDecimal("10.0"));
    result.setStatus("0");
    result.setExMsg("");
    result.setSysCreateTime(LocalDateTime.now().minusDays(2));
    result.setSysUpdateTime(LocalDateTime.now());

    return result;
  }

  /**
   * 生成模拟的业务日志
   */
  private BizLogDto createMockBizLog(String operationType, String operationDesc,
      LocalDateTime operationTime) {
    BizLogDto log = new BizLogDto();
    // 为BizLogDto添加必要的字段
    log.setOperationId("OP" + UUID.randomUUID().toString().substring(0, 8));
    log.setOperationType(operationType);
    log.setOperationDesc(operationDesc);
    log.setOperationTime(operationTime.toString());
    log.setOperatorId("USER001");
    log.setOperatorName("张三");
    log.setOperatorRole("采购员");
    log.setDetails(operationDesc);

    return log;
  }

  @Override
  public ResponseBase<CreatePurchaseOrderResult> importPurchaseOrder(String userId,
      MultipartFile file,
      Boolean isAutoPayment, Integer overduePaymentTime, Boolean autoConfirm) {
    try {
      ImportPurchaseOrderRequest request = new ImportPurchaseOrderRequest();
      request.setIsAutoPayment(isAutoPayment);
      request.setOverduePaymentTime(overduePaymentTime);
      request.setAutoConfirm(autoConfirm);
      List<String> result = purchaseOrderApplicationService.importPurchaseOrder(file, userId,
          request);
      CreatePurchaseOrderResult purchaseOrderResult = new CreatePurchaseOrderResult();
      purchaseOrderResult.setPurchaseOrderIds(result);
      purchaseOrderResult.setIsSuccess(Boolean.TRUE);
      purchaseOrderResult.setMsg("创建采购单成功");
      return ResponseBase.success(purchaseOrderResult);
    } catch (Exception ex) {
      log.error("导入采购单失败", ex);
      return ResponseBase.fail("-1", ex.getMessage());
    }
  }

  @Override
  public ResponseBase<CreatePurchaseOrderResult> createPurchaseOrder(String userId,
      CreatePurchaseOrderRequest request) {
    List<String> result = purchaseOrderApplicationService.createPurchaseOrder(userId,
        request);
    CreatePurchaseOrderResult purchaseOrderResult = new CreatePurchaseOrderResult();
    purchaseOrderResult.setPurchaseOrderIds(result);
    purchaseOrderResult.setIsSuccess(Boolean.TRUE);
    purchaseOrderResult.setMsg("创建采购单成功");
    return ResponseBase.success(purchaseOrderResult);
  }

  @Override
  public ResponseBase<PageDTO<PurchaseOrderResult>> queryPurchaseOrder(String userId,
      PurchaseOrderListQueryRequest request) {
    try {
      // 调用应用服务查询采购单列表
      PageDTO<PurchaseOrderResult> pageDTO = purchaseOrderApplicationService.queryPurchaseOrders(userId, request);
      return ResponseBase.success(pageDTO);
    } catch (Exception e) {
      log.error("查询采购单失败", e);
      return ResponseBase.fail("-1", e.getMessage());
    }
  }

  @Override
  public ResponseBase<Boolean> updateAutoDeductionConfig(String userId,
      UpdateAutoDeductionConfigRequest request) {
    try {
      // 调用应用服务更新自动扣款配置
      Boolean result = purchaseOrderApplicationService.updateAutoDeductionConfig(userId, request);
      return ResponseBase.success(result);
    } catch (Exception e) {
      log.error("更新自动扣款配置失败", e);
      return ResponseBase.fail("-1", e.getMessage());
    }
  }

  @Override
  public ResponseBase<PageDTO<PurchaseOrderDetailResult>> queryPurchaseOrderDetail(
      String userId,
      PurchaseOrderDetailQueryRequest request) {
    // 创建模拟的采购单明细列表
    List<PurchaseOrderDetailResult> details = new ArrayList<>();

    // 添加3个采购单明细
    for (long i = 1; i <= 3; i++) {
      details.add(createMockPurchaseOrderDetail(request.getPurchaseOrderNo(), 1000L + i));
    }

    // 如果请求中指定了状态，则过滤结果
    if (request != null && request.getStatus() != null && !request.getStatus().isEmpty()) {
      details = details.stream()
          .filter(detail -> request.getStatus().equals(detail.getStatus()))
          .collect(Collectors.toList());
    }
    PageDTO<PurchaseOrderDetailResult> result = new PageDTO<>();
    result.setData(details);
    result.setTotalPage(1L);
    result.setTotalCount(3L);
    result.setCurrentPage(request.getCurrentPage());
    result.setPageSize(request.getPageSize());
    return ResponseBase.success(result);
  }

  @Override
  public ResponseBase<PurchaseOrderDetailResult> updatePurchaseOrderDetail(String userId,
      UpdatePurchaseOrderDetailRequest request) {
    // 创建模拟的更新后的采购单明细
    List<PurchaseOrderDetailResult> updatedDetails = new ArrayList<>();

    // 添加一个更新后的明细
    PurchaseOrderDetailResult detail = createMockPurchaseOrderDetail(request.getPurchaseOrderNo(),
        1001L);
    detail.setCommodityCount(request.getCommodityCount());
    updatedDetails.add(detail);

    return ResponseBase.success(updatedDetails);
  }

  @Override
  public ResponseBase<List<PurchaseOrderDetailResult>> removePurchaseOrderDetail(String userId,
      RemovePurchaseDetailByIdRequest request) {
    // 返回空列表，表示删除后没有明细了
    return ResponseBase.success(new ArrayList<>());
  }

  @Override
  public ResponseBase<List<PurchaseOrderDetailResult>> copyPurchaseOrder(String userId,
      CopyPurchaseOrderRequest request) {
    // 创建模拟的复制后的采购单明细列表
    List<PurchaseOrderDetailResult> copiedDetails = new ArrayList<>();

    // 添加3个复制后的明细
    for (long i = 1; i <= 3; i++) {
      copiedDetails.add(createMockPurchaseOrderDetail("PO98434", 2000L + i));
    }

    return ResponseBase.success(copiedDetails);
  }

  @Override
  public ResponseBase<List<String>> batchConfirmPurchaseOrder(String userId,
      BatchConfirmRequest request) {
    // 返回成功确认的采购单号列表
    return ResponseBase.success(new ArrayList<>());
  }

  @Override
  public ResponseBase<List<String>> batchReCreatePurchaseOrder(String userId,
      BatchReCreateRequest request) {
    return null;
  }

  @Override
  public ResponseBase<List<String>> batchRejectPurchaseOrder(String userId,
      BatchRejectPurchaseOrderRequest request) {
    // 返回成功驳回的采购单号列表
    return ResponseBase.success(new ArrayList<>());
  }

  @Override
  public ResponseBase<PurchaseOrderInfoResult> purchaseOrderInfoQuery(String userId,
      PurchaseOrderInfoRequest request) {
    // 创建模拟的采购单详情结果
    PurchaseOrderInfoResult result = new PurchaseOrderInfoResult();

    // 设置采购单信息
    result.setPurchaseOrder(createMockPurchaseOrder("PO2305120001", "WAIT_CONFIRM"));

    // 设置操作日志
    List<BizLogDto> operationLogs = new ArrayList<>();
    operationLogs.add(createMockBizLog("CREATE", "创建采购单", LocalDateTime.now().minusDays(2)));
    operationLogs.add(
        createMockBizLog("UPDATE", "修改采购单备注信息", LocalDateTime.now().minusDays(1)));
    operationLogs.add(
        createMockBizLog("VIEW", "查看采购单详情", LocalDateTime.now().minusHours(2)));

    result.setOperationLogs(operationLogs);

    return ResponseBase.success(result);
  }
}
