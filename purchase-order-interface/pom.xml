<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.yxt</groupId>
    <artifactId>purchase-order-center</artifactId>
    <version>1.0-SNAPSHOT</version>
  </parent>
  <artifactId>purchase-order-interface</artifactId>
  <version>1.0-SNAPSHOT</version>
  <properties>
    <java.version>1.8</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <spring-boot.version>2.6.13</spring-boot.version>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>purchase-order-common</artifactId>
    </dependency>

    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>purchase-order-application</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
    </dependency>
    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>yxt-core-spring-boot-starter</artifactId>
      <version>${yxt-spring-boot-starter}</version>
      <exclusions>
        <exclusion>
          <groupId>com.yxt</groupId>
          <artifactId>yxt-sentinel-spring-boot-starter</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>yxt-sentinel-spring-boot-starter</artifactId>
      <version>1.0.0</version>
    </dependency>

  </dependencies>



</project>
