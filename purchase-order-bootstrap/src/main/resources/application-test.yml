server:
  port: 8080
api:
  version: 1.0
  version2: 2.0
compensator:
  enabled: true
spring:
  profiles:
    active: test
    robot-send: true
  application:
    name: purchase-order-center
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 100MB
  cloud:
    nacos:
      discovery:
        server-addr: http://**********:8848; # 测试
        namespace: 63b6732e-80fc-49c2-ac83-4b09e119d48c # 测试
        metadata:
          department: NR
        register-enabled: false
  redis:
    password: yxt_redis123
    lettuce:
      pool:
        max-idle: 200
        min-idle: 50
        max-active: 5000
        max-wait: 1000
    timeout: 2000
    cluster: # 此处新增
      nodes: **********:9000,**********:9002,**********:9003,**********:9004,**********:9005     # 此处新增，集群地址（此处为测试环境地址）
      max-redirects: 3
  datasource:
    url: ***********************************************************************************************************************************************************  #开发
    username: agent
    password: WrHNOhOGHR8yzMEgKvao
    driver-class-name: com.mysql.cj.jdbc.Driver


management:
  endpoint:
    mappings:
      enabled: true
    httptrace:
      enabled: true
  endpoints:
    web:
      exposure:
        include: ["*"]
  health:
    mongo:
      enabled: false
  metrics:
    distribution:
      percentiles-histogram[http.server.requests]: true
      maximum-expected-value[http.server.requests]: 10000 #预期最大值
      minimum-expected-value[http.server.requests]: 1 #预期最小值

swagger:
  enable: true

web-log-filter:
  excluded-ant-pattern-uris:
    - /file/_upload*

feign:
  hystrix:
    enabled: false
  okhttp:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 60000
        readTimeout: 60000
        loggerLevel: full
      customer-config:
        connectTimeout: 6000
        readTimeout: 6000
        loggerLevel: full
ribbon:
  ConnectTimeout: 60000
  ReadTimeout: 60000
  MaxAutoRetries: 0
  MaxAutoRetriesNextServer: 0
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            strategy: SEMAPHORE
            timeoutInMilliseconds: 60000

weixin:
  robot-url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8e54dc74-d229-42c4-a68f-60d089a61ffb # 企业微信机器人地址



alarm:
  robot:
    # 是否开启机器人告警，默认开启；非必填
    enable: true
    # 值班人手机号，英文逗号分隔；非必填
    oncallMobile: 17710036783,17302856015

logging:
  level:
    com.xxl.rpc.remoting.net.impl.netty_http.client.NettyHttpClientHandler: off


# 线程池配置
threadpool:
  asyncCoreSize: 8
  asyncMaxSize: 48
  asyncKeepAliveTime: 0
  asyncCapacity: 128
# 开发联调时打开
grey:
  enable: true
  local-mappings:
    '[(.+)]': $1.svc.k8s.test.hxyxt.com
    #order-atom-service: 127.0.0.1:8081
    order-atom-service: order-atom-service.svc.k8s.test.hxyxt.com
    order-after-sale-atom-service: order-after-sale-atom.svc.k8s.test.hxyxt.com
    order-delivery-atom-service: order-delivery-atom.svc.k8s.test.hxyxt.com
    yxt-org-read: yxt-org-read.svc.k8s.test.hxyxt.com
    hydee-middle-baseinfo: hydee-middle-baseinfo.svc.k8s.test.hxyxt.com
    yxt-trade-center: yxt-trade-center.svc.k8s.test.hxyxt.com

trade:
  systemCode: PURCHASE_ORDER
  tradeMode: JOIN_B2B
# 特殊大码商品，不能被采购
special-commodity: 289911,289910
