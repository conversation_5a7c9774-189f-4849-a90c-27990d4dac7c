package com.yxt.purchase.bootstrap;


import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import springfox.documentation.swagger2.annotations.EnableSwagger2;


@MapperScan("com.yxt.purchase.infrastructure.mapper")
@EnableFeignClients(basePackages = {
    "com.yxt.purchase",
    "com.yxt.org.read.opensdk",
    "com.yxt.merchandise.search.sdk.api"
})
@EnableSwagger2
@ComponentScan(basePackages = {"com.yxt.purchase"})
@Slf4j
@SpringBootApplication
@EnableAsync
public class PurchaseOrderServiceBootstrap {

  public static void main(String[] args) {

    System.out.println("Classpath: " + System.getProperty("java.class.path"));
    SpringApplication.run(PurchaseOrderServiceBootstrap.class, args);
    log.info("PurchaseOrderServiceBootstrap 服务启动成功");
  }
}