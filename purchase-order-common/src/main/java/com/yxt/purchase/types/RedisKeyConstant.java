package com.yxt.purchase.types;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public interface RedisKeyConstant {
  String REDIS_KEY_PREFIX = "order-service:";
  String USER_AUTH_ORG_KEY = REDIS_KEY_PREFIX + "user-auth-org:%s";
  String USER_AUTH_ORG_LOCK_KEY = USER_AUTH_ORG_KEY + "-lock";

  String STORE_PURCHASE_ORDER_NO_INDEX_KEY = "purchase-order-index:%s:%s";
  static String getUserAuthOrgKey(String userId) {
    return String.format(USER_AUTH_ORG_KEY, userId);
  }
  static String getUserAuthOrgLockKey(String userId) {
    return String.format(USER_AUTH_ORG_LOCK_KEY, userId);
  }

  /**
   * 获取门店采购单号索引key
   *
   * @param storeCode 门店编码
   * @return String
   */
  static String getStorePurchaseOrderNoIndexKey(String storeCode) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    LocalDate localDate = new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    return String.format(STORE_PURCHASE_ORDER_NO_INDEX_KEY, localDate.format(formatter), storeCode);
  }

}
