package com.yxt.purchase.types;

import cn.hutool.core.date.LocalDateTimeUtil;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import lombok.Data;

@Data
public class PurchaseOrderNo {
  private String purchaseOrderNo;
  private PurchaseOrderNo() {
  }

  private PurchaseOrderNo(String purchaseOrderNo) {
    this.purchaseOrderNo = purchaseOrderNo;
  }

  /**
   * 创建采购单号
   * 规则:PO+yyMMdd + 6位流水号, 6位流水号从000001开始, 不足6位前面补0
   * @return
   */
  public static PurchaseOrderNo create(Long serialNumber, String storeCode) {
    String orderNo = String.format("PO%s%s%s",
        LocalDateTimeUtil.format(LocalDateTime.now(), DateTimeFormatter.ofPattern("yyyyMMdd")),
        storeCode, String.format("%06d", serialNumber));
    return new PurchaseOrderNo(orderNo);
  }
  public static PurchaseOrderNo create(String purchaseOrderNo) {
    return new PurchaseOrderNo(purchaseOrderNo);
  }

}
