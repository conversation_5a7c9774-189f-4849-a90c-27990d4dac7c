package com.yxt.purchase.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum SettlementStatusEnum {

  SETTLEMENT("SETTLEMENT", "提单成功"),
  CHECK_FAILED("CHECK_FAILED", "前置校验不过"),
  SETTLEMENT_FAILED("SETTLEMENT_FAILED", "提单失败"),
  WAIT_SETTLEMENT("WAIT_SETTLEMENT", "已完成");

  private final String code;
  private final String msg;

 public static SettlementStatusEnum getByCode(String code) {
    for (SettlementStatusEnum item : SettlementStatusEnum.values()) {
      if (item.getCode().equals(code)) {
        return item;
      }
    }
    return null;
  }

}
