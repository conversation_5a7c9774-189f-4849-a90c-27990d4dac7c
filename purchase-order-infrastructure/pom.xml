<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yxt</groupId>
        <artifactId>purchase-order-center</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>purchase-order-infrastructure</artifactId>
    <version>1.0-SNAPSHOT</version>
    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.6.13</spring-boot.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>purchase-order-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>purchase-order-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt.merchandise.search</groupId>
            <artifactId>yxt-merchandise-search-sdk</artifactId>
            <version>2.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.yxt.org</groupId>
            <artifactId>yxt-org-read-open-sdk</artifactId>
            <version>1.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.yxt.middle</groupId>
            <artifactId>yxt-middle-baseinfo-sdk</artifactId>
            <version>1.27.11-RELEASE</version>
        </dependency>
        <!-- redis -->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.32.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>redisson-spring-data-33</artifactId>
                    <groupId>org.redisson</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-data-21</artifactId>
            <version>3.32.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>2.6.2</version>
        </dependency>
        <dependency>
            <groupId>com.yxt.order.common</groupId>
            <artifactId>order-common</artifactId>
            <version>1.24.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>trade-service-open-sdk</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>
    </dependencies>

</project>
