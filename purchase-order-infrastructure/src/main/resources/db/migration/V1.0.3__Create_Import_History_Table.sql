-- 创建导入历史表
CREATE TABLE `import_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `import_id` varchar(64) NOT NULL COMMENT '导入记录ID',
  `file_name` varchar(255) NOT NULL COMMENT '导入文件名',
  `import_status` int(11) NOT NULL DEFAULT '0' COMMENT '导入状态 0:处理中 1:全部处理成功 -1:处理部分异常 -2:全部失败',
  `import_time` datetime NOT NULL COMMENT '导入时间',
  `import_by` varchar(64) NOT NULL COMMENT '导入人ID',
  `import_by_name` varchar(128) DEFAULT NULL COMMENT '导入人姓名',
  `error_file_path` varchar(512) DEFAULT NULL COMMENT '错误文件路径',
  `purchase_order_nos` text COMMENT '生成的采购单号列表多个用逗号隔开',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='导入历史表';
