package com.yxt.purchase.infrastructure.listener;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.purchase.domain.repository.PurchaseOrderRepository;

import com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDetailDO;
import com.yxt.purchase.infrastructure.event.SettlementFailEvent;
import com.yxt.purchase.infrastructure.mapper.impl.PurchaseOrderDetailMapperService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
public class SettlementFailEventListener implements ApplicationListener<SettlementFailEvent> {

  @Autowired
  private PurchaseOrderDetailMapperService purchaseOrderDetailMapperService;

  @Autowired
  private PurchaseOrderRepository purchaseOrderRepository;

  @Override
  public void onApplicationEvent(@NotNull SettlementFailEvent msg) {
    setErrMsg(msg);

  }


  private void setErrMsg(SettlementFailEvent msg) {

    // 处理错误的商品信息
    //{\"850774\":\"商品无法采购或不存在\",\"142881\":\"商品无法采购或不存在\",\"120200\":\"商品无法采购或不存在\"}
    Map<String, String> errCommodity = JSON.parseObject(msg.getMsg(), Map.class);
    if (errCommodity == null || errCommodity.isEmpty()) {
      return;
    }

    List<PurchaseOrderDetailDO> commodity = purchaseOrderDetailMapperService.list(
        new LambdaQueryWrapper<PurchaseOrderDetailDO>()
            .eq(PurchaseOrderDetailDO::getPurchaseOrderNo, msg.getPurchaseOrderNo())
            .in(PurchaseOrderDetailDO::getErpCode, new ArrayList<>(errCommodity.keySet())));
    // 循环map
    List<PurchaseOrderDetailDO> updates = new ArrayList<>();
    for (PurchaseOrderDetailDO entry : commodity) {
      PurchaseOrderDetailDO updateData = new PurchaseOrderDetailDO();
      updateData.setId(entry.getId());
      updateData.setExMsg(errCommodity.get(entry.getErpCode()));
      updateData.setStatus("-1");
      updates.add(updateData);
    }
    purchaseOrderDetailMapperService.saveOrUpdateBatch(updates);

  }

  /**
   * 将主单状态改为 待确认， settlementStatus 设置为提单失败
   * @param msg
   */
  private void setPurchaseOrderStateToFail(SettlementFailEvent msg)
  {
    purchaseOrderRepository.setPurchaseOrderStateToFail(msg.getPurchaseOrderNo(),msg.getMsg());
  }

}
