package com.yxt.purchase.infrastructure.utils;

import cn.hutool.core.util.BooleanUtil;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

/**
 * Redis自增工具类
 * 实现获取key对应的值，如果key不存在则创建并设置24小时过期时间，每次获取值+1
 */
@Slf4j
@Component
public class RedisIncrementUtil {

    private static final long DEFAULT_EXPIRE_TIME = 24; // 默认过期时间（小时）
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    /**
     * 获取key对应的值，如果key不存在则创建并设置默认24小时过期时间，每次获取值+1
     *
     * @param key Redis键
     * @return 自增后的值
     */
    public Long getAndIncrement(String key) {
        return getAndIncrement(key, DEFAULT_EXPIRE_TIME, TimeUnit.HOURS);
    }
    
    /**
     * 获取key对应的值，如果key不存在则创建并设置指定过期时间，每次获取值+1
     *
     * @param key Redis键
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     * @return 自增后的值
     */
    public Long getAndIncrement(String key, long expireTime, TimeUnit timeUnit) {
        try {
            // 检查key是否存在
            Boolean hasKey = redisTemplate.hasKey(key);
            
            // 自增操作
            Long value = redisTemplate.opsForValue().increment(key);
            
            // 如果key不存在（首次创建），设置过期时间
            if (BooleanUtil.isFalse(hasKey)) {
                redisTemplate.expire(key, expireTime, timeUnit);
                log.info("Created new Redis key: {}, with expiration: {} {}", key, expireTime, timeUnit);
            }
            return value;
        } catch (Exception e) {
            log.error("Error in getAndIncrement for key: {}", key, e);
            // 发生异常时返回-1，调用方需要处理这种情况
            return -1L;
        }
    }

}
