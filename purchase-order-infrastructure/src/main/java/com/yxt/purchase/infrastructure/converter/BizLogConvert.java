package com.yxt.purchase.infrastructure.converter;

import com.yxt.order.atom.sdk.common.order_world.BizLogInfoDTO;
import com.yxt.order.common.order_world_dto.es.BizLogInfo;
import com.yxt.purchase.domain.log.BizAction;
import com.yxt.purchase.domain.log.BizScene;

public class BizLogConvert {

  public static BizLogInfo convertBizLog(BizLogInfoDTO temp) {
    BizLogInfo bizLogInfo = BizLogInfo.builder().build();
    bizLogInfo.setOperatorId(temp.getOperatorId());
    bizLogInfo.setOperatorName(temp.getOperatorName());
    bizLogInfo.setOperateTime(temp.getOperateTime());
    bizLogInfo.setOperateService(temp.getOperateService());
    bizLogInfo.setTraceId(temp.getTraceId());
    bizLogInfo.setBizNo(temp.getBizNo());
    bizLogInfo.setBizScene(BizScene.getBizScene(temp.getBizScene()));
    bizLogInfo.setBizAction(BizAction.getBizAction(temp.getBizAction()));
    bizLogInfo.setBizResult(temp.getBizResult());
    bizLogInfo.setBizResultDesc(temp.getBizResultDesc());
    bizLogInfo.setExtensionNum1(temp.getExtensionNum1());
    bizLogInfo.setExtensionNum2(temp.getExtensionNum2());
    bizLogInfo.setExtensionNum3(temp.getExtensionNum3());
    bizLogInfo.setExtJson(temp.getExtJson());
    return bizLogInfo;
  }

}
