package com.yxt.purchase.infrastructure.api;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.middle.baseinfo.api.SysUserOrganizationInfoApi;
import com.yxt.middle.baseinfo.req.org.QueryUserAuthOrgReq;
import com.yxt.middle.baseinfo.res.org.QueryUserAuthOrgRes;
import com.yxt.order.common.utils.CompletableFutureUtils;
import com.yxt.org.read.opensdk.emp.dto.request.EmployeeGetReqDTO;
import com.yxt.org.read.opensdk.emp.dto.request.EmployeeListQueryReqDTO;
import com.yxt.org.read.opensdk.emp.dto.response.EmployeeResDTO;
import com.yxt.org.read.opensdk.emp.service.EmpQueryOpenApi;
import com.yxt.org.read.opensdk.org.dto.request.OrganizationCacheOpenReqDTO;
import com.yxt.org.read.opensdk.org.dto.response.OrgInfoQueryOpenResDTO;
import com.yxt.org.read.opensdk.org.service.OrgQueryOpenApi;
import com.yxt.purchase.domain.pool.ThreadPoolConfig;
import com.yxt.purchase.infrastructure.api.entity.OperatorValueObject;
import com.yxt.purchase.infrastructure.api.entity.QueryUserAuthOrgResModel;
import com.yxt.purchase.types.RedisKeyConstant;
import com.yxt.purchase.utils.ResponseUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MiddleBaseInfoServiceImpl implements MiddleBaseInfoService {

  @Autowired
  private EmpQueryOpenApi empQueryOpenApi;

  @Autowired
  private OrgQueryOpenApi orgQueryOpenApi;

  @Autowired
  private SysUserOrganizationInfoApi sysUserOrganizationInfoApi;

  @Resource(name = ThreadPoolConfig.AUTH_ORG_QUERY_THREAD_POOL)
  private Executor threadPool;

  @Value("${auth-org-query-size:1000}")
  private Integer queryAuthOrgPageSize;

  @Autowired
  private StringRedisTemplate redisTemplate;

  @Autowired
  private RedissonClient redissonClient;

  @Override
  public OperatorValueObject getOperatorInfo(String userId, String merCode) {
    EmployeeGetReqDTO reqDTO = new EmployeeGetReqDTO();
    reqDTO.setUserId(userId);
    reqDTO.setMerCode(merCode);
    ResponseBase<EmployeeResDTO> base = empQueryOpenApi.getEmployee(reqDTO);
    ResponseUtils.checkRespSuccess(base);

    EmployeeResDTO sysEmployeeResDTO = base.getData();
    if (sysEmployeeResDTO == null) {
      return null;
    }
    return OperatorValueObject.build(sysEmployeeResDTO.getId(), sysEmployeeResDTO.getEmpCode(),
        sysEmployeeResDTO.getEmpName(), sysEmployeeResDTO.getMerCode());
  }

  @Override
  public OperatorValueObject getOperatorInfoByCode(String code, String merCode) {
    EmployeeGetReqDTO reqDTO = new EmployeeGetReqDTO();
    reqDTO.setEmpCode(code);
    reqDTO.setMerCode(merCode);
    ResponseBase<EmployeeResDTO> base = empQueryOpenApi.getEmployee(reqDTO);
    ResponseUtils.checkRespSuccess(base);

    EmployeeResDTO sysEmployeeResDTO = base.getData();
    if (sysEmployeeResDTO == null) {
      return null;
    }
    return OperatorValueObject.build(sysEmployeeResDTO.getId(), sysEmployeeResDTO.getEmpCode(),
        sysEmployeeResDTO.getEmpName(), sysEmployeeResDTO.getMerCode());
  }

  @Override
  public Map<String, EmployeeResDTO> getSysEmployeeInfo(List<String> userIds, String merCode) {

    if (CollUtil.isEmpty(userIds)) {
      return new HashMap<>();
    }
    //userIds 排除相同或者为空的userid
    userIds = userIds.stream().distinct().filter(StringUtil::isNotBlank)
        .collect(Collectors.toList());
    if (CollUtil.isEmpty(userIds)) {
      return new HashMap<>();
    }
    EmployeeListQueryReqDTO reqDTOList = new EmployeeListQueryReqDTO();
    reqDTOList.setUserIdList(userIds);
    reqDTOList.setMerCode(merCode);
    ResponseBase<List<EmployeeResDTO>> base = empQueryOpenApi.listQueryEmployee(reqDTOList);
    ResponseUtils.checkRespSuccess(base);
    Map<String, EmployeeResDTO> userIdEmployeeResDTOMap = base.getData().stream()
        .collect(Collectors.toMap(EmployeeResDTO::getId, Function.identity()));
    return userIdEmployeeResDTOMap;
  }

  @Override
  public EmployeeResDTO getSysEmployeeInfo(String userId, String merCode) {

    EmployeeListQueryReqDTO reqDTOList = new EmployeeListQueryReqDTO();
    reqDTOList.setUserIdList(Collections.singletonList(userId));
    reqDTOList.setMerCode(merCode);
    ResponseBase<List<EmployeeResDTO>> base = empQueryOpenApi.listQueryEmployee(reqDTOList);
    ResponseUtils.checkRespSuccess(base);
    if (CollUtil.isNotEmpty(base.getData())) {
      return base.getData().get(0);
    }
    return null;
  }

  @Override
  public List<OrgInfoQueryOpenResDTO> getOrgInfo(List<String> orgCodes, String merCode) {
    OrganizationCacheOpenReqDTO reqDTO = new OrganizationCacheOpenReqDTO();
    reqDTO.setMerCode(merCode);
    reqDTO.setOrgCodeList(orgCodes);
    ResponseBase<List<OrgInfoQueryOpenResDTO>> base = orgQueryOpenApi.listOrgByCache(reqDTO);
    ResponseUtils.checkRespSuccess(base);
    return base.getData();
  }

  @Override
  public List<QueryUserAuthOrgResModel> queryUserAuthOrg(String userId) {
    List<QueryUserAuthOrgResModel> userAuthOrgList = new ArrayList<>();
    String userAuthOrgKey = RedisKeyConstant.getUserAuthOrgKey(userId);
    //先查redis获取一下
    if (redisTemplate.hasKey(userAuthOrgKey)) {
      String redisDataJson = redisTemplate.opsForValue().get(userAuthOrgKey);
      userAuthOrgList = JSON.parseArray(redisDataJson, QueryUserAuthOrgResModel.class);
      return userAuthOrgList;
    }
    String userAuthOrgLock = RedisKeyConstant.getUserAuthOrgLockKey(userId);
    //加个锁
    RLock lock = redissonClient.getLock(userAuthOrgLock);
    boolean lockResult = true;
    try {
      lockResult = lock.tryLock(10, 30, TimeUnit.SECONDS);
    } catch (Exception e) {
      log.warn("加锁失败！", e);
    }
    if (lockResult) {
      try {
        if (redisTemplate.hasKey(userAuthOrgKey)) {
          String redisDataJson = redisTemplate.opsForValue().get(userAuthOrgKey);
          userAuthOrgList = JSON.parseArray(redisDataJson, QueryUserAuthOrgResModel.class);
          return userAuthOrgList;
        }
        //先查第一页，获取总页数，然后并发查剩余页
        ResponseBase<PageDTO<QueryUserAuthOrgRes>> response = callQueryAuthOrgApi(1, userId);
        ResponseUtils.checkRespSuccess(response);

        PageDTO<QueryUserAuthOrgRes> data = response.getData();
        if (ObjectUtil.isNull(data) || CollUtil.isEmpty(data.getData())) {
          return new ArrayList<>(0);
        }
        List<QueryUserAuthOrgResModel> firstPageDataList = convertUserOrgDTO(response);
        if (data.getTotalCount() < queryAuthOrgPageSize) {
          return firstPageDataList;
        }
        //并发查剩余页数据
        List<Integer> pageIndexList = IntStream.rangeClosed(2, data.getTotalPage().intValue())
            .boxed().collect(Collectors.toList());
        Function<Integer, Supplier<List<QueryUserAuthOrgResModel>>> supplierFunction = (pageIndex) -> () -> convertUserOrgDTO(
            callQueryAuthOrgApi(pageIndex, userId));
        List<List<QueryUserAuthOrgResModel>> authOrgList = CompletableFutureUtils.supplyAsync(
            supplierFunction, pageIndexList, 8, threadPool);
        List<QueryUserAuthOrgResModel> authOrgResultList = authOrgList.stream()
            .flatMap(Collection::stream).collect(Collectors.toList());
        authOrgResultList.addAll(firstPageDataList);
        redisTemplate.opsForValue()
            .set(userAuthOrgKey, JSON.toJSONString(authOrgResultList), RandomUtil.randomInt(10, 30),
                TimeUnit.MINUTES);
        return authOrgResultList;
      } finally {
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
          lock.unlock();
        }
      }
    }
    return new ArrayList<>(0);
  }

  private ResponseBase<PageDTO<QueryUserAuthOrgRes>> callQueryAuthOrgApi(Integer pageIndex,
      String userId) {
    QueryUserAuthOrgReq orgReq = new QueryUserAuthOrgReq();
    orgReq.setUserId(userId);
    orgReq.setOrgClassList(ListUtil.toList(3, 6, 7));
    orgReq.setPageSize(Long.valueOf(queryAuthOrgPageSize));
    orgReq.setCurrentPage(Long.valueOf(pageIndex));
    return sysUserOrganizationInfoApi.multipleUserAuthOrg(orgReq);
  }

  @NotNull
  private static List<QueryUserAuthOrgResModel> convertUserOrgDTO(
      ResponseBase<PageDTO<QueryUserAuthOrgRes>> response) {
    PageDTO<QueryUserAuthOrgRes> data = response.getData();
    return data.getData().stream().map(item -> {
      QueryUserAuthOrgResModel result = new QueryUserAuthOrgResModel();
      result.setOrCode(item.getOrCode());
      result.setOrClass(item.getOrClass());
      result.setLayer(item.getLayer());
      return result;
    }).collect(Collectors.toList());
  }
}
