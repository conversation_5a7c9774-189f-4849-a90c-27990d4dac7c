package com.yxt.purchase.infrastructure.handlers.process;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.middle.baseinfo.api.StoreInfoApi;
import com.yxt.middle.baseinfo.res.store.StoreInfoDataResDTO;
import com.yxt.order.common.ExceptionUtil;
import com.yxt.org.read.opensdk.org.dto.response.OrgInfoQueryOpenResDTO;
import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.domain.entity.HandlerContext;
import com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler;
import com.yxt.purchase.infrastructure.api.MiddleBaseInfoService;
import com.yxt.purchase.infrastructure.feign.MiddleIdClient;
import com.yxt.purchase.utils.ResponseUtils;
import java.util.List;

public class InitDataHandler extends
    AbstractPurchaseOrderHandler<HandlerContext> {

  private final MiddleIdClient middleIdClient;
  private final MiddleBaseInfoService middleBaseInfoService;
  private final StoreInfoApi storeInfoApi;

  public InitDataHandler(MiddleIdClient middleIdClient, MiddleBaseInfoService middleBaseInfoService,
      StoreInfoApi storeInfoApi) {
    this.middleIdClient = middleIdClient;
    this.middleBaseInfoService = middleBaseInfoService;
    this.storeInfoApi = storeInfoApi;
  }

  /**
   * 数据清洗 以及必要数据初始化，目前没有太多逻辑需要处理 暂时放这里
   *
   * @param context
   */
  @Override
  public void doHandleReal(HandlerContext context) {
    PurchaseOrderAggregate purchaseOrderAggregate = context.getAggregates().get(0);
    context.getAggregates().get(0)
        .setParentPurchaseOrderNo(middleIdClient.getId(1).get(0).toString());
    // 获取组织机构信息
    List<OrgInfoQueryOpenResDTO> orgInfoQueryOpenResDTOS = middleBaseInfoService.getOrgInfo(
        CollUtil.newArrayList(purchaseOrderAggregate.getOrganizationCode()),
        purchaseOrderAggregate.getMerCode());
    if (CollUtil.isNotEmpty(orgInfoQueryOpenResDTOS)) {
      purchaseOrderAggregate.setOrganizationName(orgInfoQueryOpenResDTOS.get(0).getOrName());
      purchaseOrderAggregate.setCompanyCode(orgInfoQueryOpenResDTOS.get(0).getAffiliationCompany());
    } else {
      throw ExceptionUtil.getWarnException("-1",
          purchaseOrderAggregate.getOrganizationCode() + "门店不存在");
    }
    // 获取门店大仓信息
    initStoreInfo(purchaseOrderAggregate);
  }

  private void initStoreInfo(PurchaseOrderAggregate purchaseOrderAggregate) {
    ResponseBase<StoreInfoDataResDTO> storeInfoDataResDTOResponseBase = storeInfoApi.getStoreInfo(
        purchaseOrderAggregate.getMerCode(), null,
        purchaseOrderAggregate.getOrganizationCode());
    ResponseUtils.checkRespSuccess(storeInfoDataResDTOResponseBase);

    StoreInfoDataResDTO storeInfoDataResDTO = storeInfoDataResDTOResponseBase.getData();
    purchaseOrderAggregate.setWarehouseCode(storeInfoDataResDTO.getWarehouseCode());
    // 设置收货人信息
    purchaseOrderAggregate.setAddress(PurchaseOrderAggregate.buildReceiveInfoModel(
        StrUtil.isBlank(storeInfoDataResDTO.getContacter()) ? storeInfoDataResDTO.getStName()
            : storeInfoDataResDTO.getContacter(), storeInfoDataResDTO.getMobile(),
        storeInfoDataResDTO.getProvince(), storeInfoDataResDTO.getCity(),
        storeInfoDataResDTO.getArea(), storeInfoDataResDTO.getAddress()
    ));
  }

}
