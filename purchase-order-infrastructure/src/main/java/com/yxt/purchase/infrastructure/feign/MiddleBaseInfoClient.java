package com.yxt.purchase.infrastructure.feign;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.org.read.opensdk.emp.dto.response.old.SysEmployeeResDTO;
import com.yxt.purchase.infrastructure.feign.entity.StoreResDTO;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

//@FeignClient(value = "hydee-middle-baseinfo",contextId = "baseinfoApi")
public interface MiddleBaseInfoClient {

  @GetMapping("/1.0/baseinfo/getEmployee/{userId}")
  ResponseBase<SysEmployeeResDTO> getEmployeeByUserId(@PathVariable(value = "userId") String userId);

  @GetMapping("/1.0/store/_queryStoreByMerCodeAndProvence")
  ResponseBase<List<StoreResDTO>> queryAllOrgByMerCode(@RequestParam("merCode") String merCode);
}
