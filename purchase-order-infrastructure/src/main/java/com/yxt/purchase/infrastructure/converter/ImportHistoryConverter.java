package com.yxt.purchase.infrastructure.converter;

import cn.hutool.json.JSONUtil;
import com.yxt.purchase.domain.ImportHistoryAggregate;
import com.yxt.purchase.enums.ImportStatusEnum;
import com.yxt.purchase.infrastructure.dataobject.ImportHistoryDO;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

/**
 * 导入历史转换器
 */
@Component
public class ImportHistoryConverter {
    
    /**
     * 将数据对象转换为聚合根
     */
    public ImportHistoryAggregate toDomain(ImportHistoryDO importHistoryDO) {
        if (importHistoryDO == null) {
            return null;
        }
        
        ImportHistoryAggregate aggregate = new ImportHistoryAggregate();
        aggregate.setImportId(importHistoryDO.getImportId());
        aggregate.setFileName(importHistoryDO.getFileName());
        aggregate.setImportStatus(ImportStatusEnum.getByCode(importHistoryDO.getImportStatus()));
        aggregate.setImportTime(importHistoryDO.getImportTime());
        aggregate.setImportBy(importHistoryDO.getImportBy());
        aggregate.setImportByName(importHistoryDO.getImportByName());
        aggregate.setErrorFilePath(importHistoryDO.getErrorFilePath());
        aggregate.setCreatedTime(importHistoryDO.getCreatedTime());
        aggregate.setUpdatedTime(importHistoryDO.getUpdatedTime());
        
        // 解析采购单号列表
        if (importHistoryDO.getPurchaseOrderNos() != null) {
            try {
                List<String> purchaseOrderNos = JSONUtil.toList(importHistoryDO.getPurchaseOrderNos(), String.class);
                aggregate.setPurchaseOrderNos(purchaseOrderNos);
            } catch (Exception e) {
                // 解析失败时设置为空列表
                aggregate.setPurchaseOrderNos(List.of());
            }
        }
        
        return aggregate;
    }
    
    /**
     * 将数据对象列表转换为聚合根列表
     */
    public List<ImportHistoryAggregate> toDomainList(List<ImportHistoryDO> importHistoryDOs) {
        if (importHistoryDOs == null) {
            return null;
        }
        
        return importHistoryDOs.stream()
            .map(this::toDomain)
            .collect(Collectors.toList());
    }
    
    /**
     * 将聚合根转换为数据对象
     */
    public ImportHistoryDO toDO(ImportHistoryAggregate aggregate) {
        if (aggregate == null) {
            return null;
        }
        
        ImportHistoryDO importHistoryDO = new ImportHistoryDO();
        importHistoryDO.setImportId(aggregate.getImportId());
        importHistoryDO.setFileName(aggregate.getFileName());
        importHistoryDO.setImportStatus(aggregate.getImportStatus() != null ? aggregate.getImportStatus().getCode() : null);
        importHistoryDO.setImportTime(aggregate.getImportTime());
        importHistoryDO.setImportBy(aggregate.getImportBy());
        importHistoryDO.setImportByName(aggregate.getImportByName());
        importHistoryDO.setErrorFilePath(aggregate.getErrorFilePath());
        importHistoryDO.setCreatedTime(aggregate.getCreatedTime());
        importHistoryDO.setUpdatedTime(aggregate.getUpdatedTime());
        
        // 序列化采购单号列表
        if (aggregate.getPurchaseOrderNos() != null) {
            importHistoryDO.setPurchaseOrderNos(JSONUtil.toJsonStr(aggregate.getPurchaseOrderNos()));
        }
        
        return importHistoryDO;
    }
}
