package com.yxt.purchase.infrastructure.feign;

import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "middle-id")
public interface MiddleIdClient {

  @GetMapping("/1.0/id/_get")
  List<Long> getId(@RequestParam(name = "batch", defaultValue = "1") int batch);
}
