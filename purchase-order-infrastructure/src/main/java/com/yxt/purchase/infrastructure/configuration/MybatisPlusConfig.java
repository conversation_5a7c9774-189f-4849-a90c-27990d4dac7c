package com.yxt.purchase.infrastructure.configuration;



import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MybatisPlusConfig  {
  @Bean
  public PaginationInterceptor paginationInterceptor() {

    PaginationInterceptor page = new PaginationInterceptor().setLimit(1000L);
    page.setDialectType("mysql");
    return page;
  }
}
