package com.yxt.purchase.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 采购单Mapper接口
 */
@Mapper
public interface PurchaseOrderMapper extends BaseMapper<PurchaseOrderDO> {

  /**
   * 根据ID查询采购单
   *
   * @param id 采购单ID
   * @return 采购单DO
   */
  PurchaseOrderDO selectById(@Param("id") Long id);

  /**
   * 根据采购单号查询采购单
   *
   * @param purchaseOrderNo 采购单号
   * @return 采购单DO
   */
  PurchaseOrderDO selectByPurchaseOrderNo(@Param("purchaseOrderNo") String purchaseOrderNo);

  /**
   * 根据商户编码查询采购单列表
   *
   * @param merCode 商户编码
   * @return 采购单DO列表
   */
  List<PurchaseOrderDO> selectByMerCode(@Param("merCode") String merCode);

  /**
   * 根据状态查询采购单列表
   *
   * @param state 采购单状态
   * @return 采购单DO列表
   */
  List<PurchaseOrderDO> selectByState(@Param("state") String state);

  /**
   * 根据ID逻辑删除采购单
   *
   * @param id        采购单ID
   * @param updatedBy 更新人
   * @return 影响行数
   */
  int deleteById(@Param("id") Long id, @Param("updatedBy") String updatedBy);

  /**
   * 根据采购单号逻辑删除采购单
   *
   * @param purchaseOrderNo 采购单号
   * @param updatedBy       更新人
   * @return 影响行数
   */
  int deleteByPurchaseOrderNo(@Param("purchaseOrderNo") String purchaseOrderNo,
      @Param("updatedBy") String updatedBy);

  /**
   * 更新采购单
   *
   * @param purchaseOrderDO 采购单DO
   * @return 影响行数
   */
  int update(PurchaseOrderDO purchaseOrderDO);

  /**
   * 将采购单状态设置为取消
   *
   * @param purchaseOrderNos 采购单号
   * @param failReason       失败原因
   * @param updatedBy        更新人
   * @return 影响行数
   */
  int setPurchaseOrderStateToCancel(@Param("purchaseOrderNos") List<String> purchaseOrderNos,
      @Param("failReason") String failReason,
      @Param("updatedBy") String updatedBy);

  /**
   * 将采购单状态错误消息以及状态回滚到待确认
   *
   * @param purchaseOrderNo 采购单号
   * @param failReason       失败原因
   * @param updatedBy        更新人
   * @return 影响行数
   */
  int setPurchaseOrderStatsToWaitConfirm(@Param("purchaseOrderNo") String purchaseOrderNo,
      @Param("failReason") String failReason,
      @Param("updatedBy") String updatedBy);
}
