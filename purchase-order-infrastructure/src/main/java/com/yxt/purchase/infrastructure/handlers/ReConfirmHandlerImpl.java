package com.yxt.purchase.infrastructure.handlers;

import com.yxt.middle.baseinfo.api.StoreInfoApi;
import com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler;
import com.yxt.purchase.domain.handler.CreatePurchaseOrderHandler;
import com.yxt.purchase.domain.handler.ReCreateOrderHandler;
import com.yxt.purchase.domain.repository.PurchaseOrderRepository;
import com.yxt.purchase.infrastructure.api.MerchandiseService;
import com.yxt.purchase.infrastructure.api.MiddleBaseInfoService;
import com.yxt.purchase.infrastructure.feign.MiddleIdClient;
import com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler;
import com.yxt.purchase.infrastructure.handlers.process.InitDataHandler;
import com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler;
import com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler;
import com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler;
import com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler;
import com.yxt.purchase.infrastructure.mapper.PurchaseOrderMapper;
import com.yxt.purchase.infrastructure.mapper.impl.PurchaseOrderMapperService;
import com.yxt.trade.api.OrderSettlementApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/**
 * 重新发起采购确认处理器
 */
@Service
public class ReConfirmHandlerImpl implements ReCreateOrderHandler {

  @Autowired
  private MiddleBaseInfoService middleBaseInfoService;

  @Autowired
  private MiddleIdClient middleIdClient;

  @Autowired
  private OrderSettlementApi orderSettlementApi;

  @Autowired
  private StoreInfoApi storeInfoApi;

  @Autowired
  private PurchaseOrderMapperService purchaseOrderMapperService;
  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Autowired
  private PurchaseOrderMapper purchaseOrderMapper;
  /**
   * 初始化创建采购单处理器责任链
   *
   * @return
   */
  @Override
  public AbstractPurchaseOrderHandler<?> initHandler() {
    InitDataHandler initDataHandler = new InitDataHandler(middleIdClient, middleBaseInfoService,
        storeInfoApi);
    initDataHandler
        .setNextHandler(
            new SubmitPayOrderHandler(orderSettlementApi, applicationEventPublisher))// 提交交易单
        .setNextHandler(new UpdatePurchaseOrderStatusHandler(purchaseOrderMapper));//更新采购单状态
    return initDataHandler;
  }
}
