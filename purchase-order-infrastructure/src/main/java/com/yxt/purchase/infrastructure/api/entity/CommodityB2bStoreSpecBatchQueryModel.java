package com.yxt.purchase.infrastructure.api.entity;


import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class CommodityB2bStoreSpecBatchQueryModel {

  /**
   * 门店编码
   */
  private String storeCode;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 主图
   */
  private String mainPic;

  /**
   * 品牌名称
   */
  private String brandName;

  /**
   * 商品名称
   */
  private String name;

  /**
   * 规格值
   */
  private String specValue;

  /**
   * 状态 0-下架 1-上架
   */
  private Integer status;

  /**
   * 生产厂商
   */
  private String manufacture;

  /**
   * 购进价
   */
  private BigDecimal purchasePrice;

  /**
   * 仓库库存
   */
  private Integer wareStock;

  /**
   * 助记码
   */
  private String memoryCode;

  /**
   * 药品类型(0：甲类OTC，1:处方药，2：乙类OTC，3：非处方药)
   */
  private Integer drugType;

  /**
   * 分类编码集合(1-5级分类)
   */
  private List<String> typeCodes;

  /**
   * 采购数量上限
   */
  private Integer purchaseUpperLimit;

  /**
   * 采购数量基本单位
   */
  private Integer purchaseBundleSize;

  /**
   * 是否
   */
  private Boolean isColdChain;



}
