package com.yxt.purchase.infrastructure.repository;

import cn.hutool.core.collection.CollUtil;
import com.yxt.org.read.opensdk.emp.dto.response.EmployeeResDTO;
import com.yxt.purchase.domain.entity.EmployeeInfo;
import com.yxt.purchase.domain.repository.EmployeeRepository;
import com.yxt.purchase.infrastructure.api.MiddleBaseInfoService;
import com.yxt.purchase.types.PurchaseOrderCommonTypes;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class EmployeeRepositoryImpl implements EmployeeRepository {

  @Autowired
  private MiddleBaseInfoService middleBaseInfoService;

  @Override
  public List<EmployeeInfo> getEmployeeInfo(List<String> userIds) {
    if (CollUtil.isEmpty(userIds)) {
      return new ArrayList<>();
    }
    Map<String, EmployeeResDTO> employeeResDTOMap = middleBaseInfoService.getSysEmployeeInfo(
        userIds, PurchaseOrderCommonTypes.MER_CODE);
    if (CollUtil.isEmpty(employeeResDTOMap)) {
      return new ArrayList<>();
    }
    List<EmployeeInfo> employeeInfoList = new ArrayList<>();

    for (Map.Entry<String, EmployeeResDTO> entry : employeeResDTOMap.entrySet()) {
      EmployeeInfo employeeInfo = new EmployeeInfo();
      employeeInfo.setUserId(entry.getKey());
      employeeInfo.setUserName(entry.getValue().getEmpName());
      employeeInfo.setUserCode(entry.getValue().getEmpCode());
      employeeInfoList.add(employeeInfo);
    }
    return employeeInfoList;
  }

  @Override
  public EmployeeInfo getEmployeeInfo(String userId) {
    EmployeeResDTO employeeResDTOMap = middleBaseInfoService.getSysEmployeeInfo(
        userId, PurchaseOrderCommonTypes.MER_CODE);
    if (employeeResDTOMap == null) {
      return new EmployeeInfo();
    }

    EmployeeInfo employeeInfo = new EmployeeInfo();
    employeeInfo.setUserId(userId);
    employeeInfo.setUserName(employeeResDTOMap.getEmpName());
    employeeInfo.setUserCode(employeeResDTOMap.getEmpCode());
    return employeeInfo;
  }
}
