package com.yxt.purchase.infrastructure.api.entity;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class CommodityStockAndPriceQueryModel {
  @ApiModelProperty("门店编码")
  private String storeCode;
  @ApiModelProperty("商品编码")
  private String erpCode;
  @ApiModelProperty("门店库存")
  private Integer stock;
  @ApiModelProperty("仓库库存")
  private Integer wareStock;
  @ApiModelProperty("购进价")
  private BigDecimal purchasePrice;
  @ApiModelProperty("零售价")
  private BigDecimal retailPrice;
}
