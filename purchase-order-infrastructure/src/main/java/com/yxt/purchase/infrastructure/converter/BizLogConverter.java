package com.yxt.purchase.infrastructure.converter;

import com.yxt.purchase.domain.entity.BizLog;
import com.yxt.purchase.infrastructure.dataobject.BizLogDO;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

/**
 * 业务日志转换器
 */
@Component
public class BizLogConverter {
    
    /**
     * 将数据对象转换为领域实体
     */
    public BizLog toDomain(BizLogDO bizLogDO) {
        if (bizLogDO == null) {
            return null;
        }
        
        BizLog bizLog = new BizLog();
        bizLog.setId(bizLogDO.getId());
        bizLog.setBizId(bizLogDO.getBizId());
        bizLog.setBizType(bizLogDO.getBizType());
        bizLog.setOperationType(bizLogDO.getOperationType());
        bizLog.setOperationContent(bizLogDO.getOperationContent());
        bizLog.setOperatorId(bizLogDO.getOperatorId());
        bizLog.setOperatorName(bizLogDO.getOperatorName());
        bizLog.setOperationTime(bizLogDO.getOperationTime());
        
        return bizLog;
    }
    
    /**
     * 将数据对象列表转换为领域实体列表
     */
    public List<BizLog> toDomainList(List<BizLogDO> bizLogDOs) {
        if (bizLogDOs == null) {
            return null;
        }
        
        return bizLogDOs.stream()
            .map(this::toDomain)
            .collect(Collectors.toList());
    }
    
    /**
     * 将领域实体转换为数据对象
     */
    public BizLogDO toDO(BizLog bizLog) {
        if (bizLog == null) {
            return null;
        }
        
        BizLogDO bizLogDO = new BizLogDO();
        bizLogDO.setId(bizLog.getId());
        bizLogDO.setBizId(bizLog.getBizId());
        bizLogDO.setBizType(bizLog.getBizType());
        bizLogDO.setOperationType(bizLog.getOperationType());
        bizLogDO.setOperationContent(bizLog.getOperationContent());
        bizLogDO.setOperatorId(bizLog.getOperatorId());
        bizLogDO.setOperatorName(bizLog.getOperatorName());
        bizLogDO.setOperationTime(bizLog.getOperationTime());
        
        return bizLogDO;
    }
}
