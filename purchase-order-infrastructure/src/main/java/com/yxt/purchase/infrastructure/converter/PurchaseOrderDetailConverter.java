package com.yxt.purchase.infrastructure.converter;

import com.yxt.purchase.domain.PurchaseOrderDetailAggregate;
import com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDetailDO;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

/**
 * 采购商品明细转换器
 */
@Component
public class PurchaseOrderDetailConverter {

  /**
   * 将数据对象转换为聚合根
   */
  public PurchaseOrderDetailAggregate toAggregate(PurchaseOrderDetailDO dataObject) {
    if (dataObject == null) {
      return null;
    }

    PurchaseOrderDetailAggregate aggregate = new PurchaseOrderDetailAggregate();
    aggregate.setId(dataObject.getId());
    aggregate.setPurchaseOrderNo(dataObject.getPurchaseOrderNo());
    aggregate.setErpCode(dataObject.getErpCode());
    aggregate.setErpName(dataObject.getErpName());
    aggregate.setCommoditySpec(dataObject.getCommoditySpec());
    aggregate.setManufacture(dataObject.getManufacture());
    aggregate.setCommodityCount(dataObject.getCommodityCount());
    aggregate.setStatus(dataObject.getStatus());
    aggregate.setExMsg(dataObject.getExMsg());
    aggregate.setSysCreateTime(dataObject.getSysCreateTime());
    aggregate.setSysUpdateTime(dataObject.getSysUpdateTime());

    return aggregate;
  }

  /**
   * 将数据对象列表转换为聚合根列表
   */
  public List<PurchaseOrderDetailAggregate> toAggregateList(List<PurchaseOrderDetailDO> dataObjects) {
    if (dataObjects == null) {
      return null;
    }

    return dataObjects.stream()
        .map(this::toAggregate)
        .collect(Collectors.toList());
  }

  /**
   * 将聚合根转换为数据对象
   */
  public PurchaseOrderDetailDO toDO(PurchaseOrderDetailAggregate aggregate) {
    if (aggregate == null) {
      return null;
    }

    PurchaseOrderDetailDO dataObject = new PurchaseOrderDetailDO();
    dataObject.setId(aggregate.getId());
    dataObject.setPurchaseOrderNo(aggregate.getPurchaseOrderNo());
    dataObject.setErpCode(aggregate.getErpCode());
    dataObject.setErpName(aggregate.getErpName());
    dataObject.setCommoditySpec(aggregate.getCommoditySpec());
    dataObject.setManufacture(aggregate.getManufacture());
    dataObject.setCommodityCount(aggregate.getCommodityCount());
    dataObject.setStatus(aggregate.getStatus());
    dataObject.setExMsg(aggregate.getExMsg());
    dataObject.setSysCreateTime(aggregate.getSysCreateTime());
    dataObject.setSysUpdateTime(aggregate.getSysUpdateTime());

    return dataObject;
  }


}