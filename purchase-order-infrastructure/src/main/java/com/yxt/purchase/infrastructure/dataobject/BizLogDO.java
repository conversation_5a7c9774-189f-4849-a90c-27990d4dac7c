package com.yxt.purchase.infrastructure.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 业务日志数据对象
 */
@Data
@TableName("biz_log")
public class BizLogDO {
    
    /**
     * 日志ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 业务ID
     */
    private String bizId;
    
    /**
     * 业务类型
     */
    private String bizType;
    
    /**
     * 操作类型
     */
    private String operationType;
    
    /**
     * 操作内容
     */
    private String operationContent;
    
    /**
     * 操作人ID
     */
    private String operatorId;
    
    /**
     * 操作人姓名
     */
    private String operatorName;
    
    /**
     * 操作时间
     */
    private LocalDateTime operationTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 是否删除 0-未删除 1-已删除
     */
    private Integer deleted;
}
