package com.yxt.purchase.infrastructure.api.entity;

import com.yxt.common.ddd.domain.model.ValueObject;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
public class OperatorValueObject implements ValueObject {

  public OperatorValueObject() {
  }
  /**
   * 操作人工号
   */
  private String operatorId;
  /**
   * 操作人登录名
   */
  private String operatorLogin;
  /**
   * 操作人姓名
   */
  private String operatorName;

  /**
   * 商户编号
   */
  private String merCode;


  private OperatorValueObject(String operatorId, String operatorLogin,
      String operatorName, String merCode) {
    this.operatorId = operatorId;
    this.operatorLogin = operatorLogin;
    this.operatorName = operatorName;
    this.merCode = merCode;
  }

  public static OperatorValueObject build(String operatorId, String operatorLogin,
      String operatorName, String merCode) {
    return new OperatorValueObject(operatorId, operatorLogin,
        operatorName, merCode);

  }



}
