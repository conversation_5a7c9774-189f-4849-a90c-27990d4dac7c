package com.yxt.purchase.infrastructure.handlers.process;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yxt.order.common.ExceptionUtil;
import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.domain.entity.HandlerContext;
import com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler;
import com.yxt.purchase.enums.PurchaseOrderStatusEnum;
import com.yxt.purchase.infrastructure.api.MiddleBaseInfoService;
import com.yxt.purchase.infrastructure.api.entity.OperatorValueObject;
import com.yxt.purchase.infrastructure.api.entity.QueryUserAuthOrgResModel;
import com.yxt.purchase.types.OperationUser;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class UserPermissionCheckHandler extends
    AbstractPurchaseOrderHandler<HandlerContext> {

  private final MiddleBaseInfoService middleBaseInfoService;

  public UserPermissionCheckHandler(MiddleBaseInfoService middleBaseInfoService) {
    this.middleBaseInfoService = middleBaseInfoService;
  }

  /**
   * 用户权限校验
   *
   * @param context 采购订单信息
   */
  @Override
  public void doHandleReal(HandlerContext context) {
    // 获取创建人信息
    PurchaseOrderAggregate purchaseOrderAggregate = context.getAggregates().get(0);
    OperatorValueObject operatorValueObject = middleBaseInfoService.getOperatorInfo(
        purchaseOrderAggregate.getCreatedBy().getUserId(), purchaseOrderAggregate.getMerCode());
    if (ObjectUtil.isNull(operatorValueObject)) {
      throw ExceptionUtil.getWarnException("-1", "创建人信息获取失败");
    }
    purchaseOrderAggregate.setCreatedBy(new OperationUser(operatorValueObject.getOperatorId(),
        operatorValueObject.getOperatorLogin(), operatorValueObject.getOperatorName()));
    purchaseOrderAggregate.setModifiedBy(purchaseOrderAggregate.getCreatedBy());


    List<QueryUserAuthOrgResModel> queryUserAuthOrg = middleBaseInfoService.queryUserAuthOrg(
        purchaseOrderAggregate.getCreatedBy().getUserId());
    if (CollUtil.isEmpty(queryUserAuthOrg) || queryUserAuthOrg.stream().noneMatch(item -> {
      return item.getOrCode().equals(purchaseOrderAggregate.getOrganizationCode());
    })) {
      throw ExceptionUtil.getWarnException("-1", String.format("你没有权限操作%s门店的采购单",
          purchaseOrderAggregate.getOrganizationCode()));
    }

    // 确认人 和创建人一致
    if (purchaseOrderAggregate.getState() == PurchaseOrderStatusEnum.CONFIRMED) {
      purchaseOrderAggregate.setConfirmBy(purchaseOrderAggregate.getCreatedBy());
    }
  }
}
