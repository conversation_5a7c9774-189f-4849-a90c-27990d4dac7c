package com.yxt.purchase.infrastructure.converter;

import cn.hutool.core.collection.CollUtil;
import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.domain.entity.PurchaseOrder;
import com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDO;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 采购单转换器
 */
public class PurchaseOrderConverter {

  /**
   * 将领域实体转换为数据对象
   *
   * @param purchaseOrder 采购单领域实体
   * @return 采购单数据对象
   */
  public static PurchaseOrderDO toDataObject(PurchaseOrder purchaseOrder) {
    if (purchaseOrder == null) {
      return null;
    }

    PurchaseOrderDO purchaseOrderDO = new PurchaseOrderDO();
    purchaseOrderDO.setId(purchaseOrder.getId());
    purchaseOrderDO.setMerCode(purchaseOrder.getMerCode());
    purchaseOrderDO.setPurchaseOrderNo(purchaseOrder.getPurchaseOrderNo());
    purchaseOrderDO.setState(purchaseOrder.getState());
    purchaseOrderDO.setRelatedOrderNo(purchaseOrder.getRelatedOrderNo());
    purchaseOrderDO.setParentPurchaseOrderNo(purchaseOrder.getParentPurchaseOrderNo());
    purchaseOrderDO.setAutoPayment(purchaseOrder.getAutoPayment());
    purchaseOrderDO.setOverduePaymentTime(purchaseOrder.getOverduePaymentTime());
    purchaseOrderDO.setCompanyCode(purchaseOrder.getCompanyCode());
    purchaseOrderDO.setOrganizationCode(purchaseOrder.getOrganizationCode());
    purchaseOrderDO.setOrganizationName(purchaseOrder.getOrganizationName());
    purchaseOrderDO.setPurchaseOrderLabel(purchaseOrder.getPurchaseOrderLabel());
    purchaseOrderDO.setConfirmBy(purchaseOrder.getConfirmBy());
    purchaseOrderDO.setConfirmTime(purchaseOrder.getConfirmTime());
    purchaseOrderDO.setCreatedBy(purchaseOrder.getCreatedBy());
    purchaseOrderDO.setUpdatedBy(purchaseOrder.getUpdatedBy());
    purchaseOrderDO.setCreatedTime(purchaseOrder.getCreatedTime());
    purchaseOrderDO.setUpdatedTime(purchaseOrder.getUpdatedTime());
    purchaseOrderDO.setDeleted(purchaseOrder.getDeleted());
    purchaseOrderDO.setVersion(purchaseOrder.getVersion());

    return purchaseOrderDO;
  }

  /**
   * 将数据对象转换为领域实体
   *
   * @param purchaseOrderDO 采购单数据对象
   * @return 采购单领域实体
   */
  public static PurchaseOrder toDomainEntity(PurchaseOrderDO purchaseOrderDO) {
    if (purchaseOrderDO == null) {
      return null;
    }
    PurchaseOrder purchaseOrder = new PurchaseOrder();
    purchaseOrder.setId(purchaseOrderDO.getId());
    purchaseOrder.setMerCode(purchaseOrderDO.getMerCode());
    purchaseOrder.setPurchaseOrderNo(purchaseOrderDO.getPurchaseOrderNo());
    purchaseOrder.setState(purchaseOrderDO.getState());
    purchaseOrder.setRelatedOrderNo(purchaseOrderDO.getRelatedOrderNo());
    purchaseOrder.setParentPurchaseOrderNo(purchaseOrderDO.getParentPurchaseOrderNo());
    purchaseOrder.setAutoPayment(purchaseOrderDO.getAutoPayment());
    purchaseOrder.setOverduePaymentTime(purchaseOrderDO.getOverduePaymentTime());
    purchaseOrder.setCompanyCode(purchaseOrderDO.getCompanyCode());
    purchaseOrder.setOrganizationCode(purchaseOrderDO.getOrganizationCode());
    purchaseOrder.setOrganizationName(purchaseOrderDO.getOrganizationName());
    purchaseOrder.setPurchaseOrderLabel(purchaseOrderDO.getPurchaseOrderLabel());
    purchaseOrder.setConfirmBy(purchaseOrderDO.getConfirmBy());
    purchaseOrder.setConfirmTime(purchaseOrderDO.getConfirmTime());
    purchaseOrder.setCreatedBy(purchaseOrderDO.getCreatedBy());
    purchaseOrder.setUpdatedBy(purchaseOrderDO.getUpdatedBy());
    purchaseOrder.setCreatedTime(purchaseOrderDO.getCreatedTime());
    purchaseOrder.setUpdatedTime(purchaseOrderDO.getUpdatedTime());
    purchaseOrder.setDeleted(purchaseOrderDO.getDeleted());
    purchaseOrder.setVersion(purchaseOrderDO.getVersion());

    return purchaseOrder;
  }

  /**
   * 将数据对象列表转换为领域实体列表
   *
   * @param purchaseOrderDOList 采购单数据对象列表
   * @return 采购单领域实体列表
   */
  public static List<PurchaseOrder> toDomainEntityList(List<PurchaseOrderDO> purchaseOrderDOList) {
    if (purchaseOrderDOList == null) {
      return null;
    }

    return purchaseOrderDOList.stream()
        .map(PurchaseOrderConverter::toDomainEntity)
        .collect(Collectors.toList());
  }

  /**
   * 将采购单聚合根转换为数据对象
   *
   * @param purchaseOrderAggregate 采购单聚合根
   * @return 采购单数据对象
   */
  public static PurchaseOrderDO toBOEntity(PurchaseOrderAggregate purchaseOrderAggregate) {
    if (purchaseOrderAggregate == null) {
      return null;
    }
    // 创建数据对象
    PurchaseOrderDO purchaseOrderDO = new PurchaseOrderDO();
    // 设置基本信息
    purchaseOrderDO.setMerCode(purchaseOrderAggregate.getMerCode());
    purchaseOrderDO.setPurchaseOrderNo(purchaseOrderAggregate.getPurchaseOrderNo() != null ?
        purchaseOrderAggregate.getPurchaseOrderNo().getPurchaseOrderNo() : null);
    purchaseOrderDO.setState(purchaseOrderAggregate.getState() != null ?
        purchaseOrderAggregate.getState().name() : null);
    purchaseOrderDO.setRelatedOrderNo(purchaseOrderAggregate.getRelatedOrderNo());
    purchaseOrderDO.setMsg(purchaseOrderAggregate.getMsg());
    purchaseOrderDO.setSettlementStatus(purchaseOrderAggregate.getSettlementStatus() != null ?
        purchaseOrderAggregate.getSettlementStatus().getCode() : null);
    purchaseOrderDO.setParentPurchaseOrderNo(purchaseOrderAggregate.getParentPurchaseOrderNo());
    purchaseOrderDO.setAutoPayment(purchaseOrderAggregate.getAutoPayment() != null ?
        (purchaseOrderAggregate.getAutoPayment() ? 1 : 0) : null);
    purchaseOrderDO.setOverduePaymentTime(purchaseOrderAggregate.getOverduePaymentTime());
    purchaseOrderDO.setCompanyCode(purchaseOrderAggregate.getCompanyCode());
    purchaseOrderDO.setOrganizationCode(purchaseOrderAggregate.getOrganizationCode());
    purchaseOrderDO.setOrganizationName(purchaseOrderAggregate.getOrganizationName());
    purchaseOrderDO.setPurchaseOrderLabel(purchaseOrderAggregate.getPurchaseOrderLabel() != null ?
        purchaseOrderAggregate.getPurchaseOrderLabel().name() : null);
    // 设置确认信息
    if (purchaseOrderAggregate.getConfirmBy() != null) {
      purchaseOrderDO.setConfirmBy(purchaseOrderAggregate.getConfirmBy().getUserId());
    }
    purchaseOrderDO.setConfirmTime(purchaseOrderAggregate.getConfirmTime());
    // 设置创建和更新信息
    if (purchaseOrderAggregate.getCreatedBy() != null) {
      purchaseOrderDO.setCreatedBy(purchaseOrderAggregate.getCreatedBy().getUserId());
    }
    if (purchaseOrderAggregate.getModifiedBy() != null) {
      purchaseOrderDO.setUpdatedBy(purchaseOrderAggregate.getModifiedBy().getUserId());
    }
    // 设置时间信息
    purchaseOrderDO.setCreatedTime(purchaseOrderAggregate.getCreatedTime());
    purchaseOrderDO.setUpdatedTime(purchaseOrderAggregate.getModifiedTime());
    // 设置默认值
    purchaseOrderDO.setDeleted(0);
    purchaseOrderDO.setVersion(1L);
    return purchaseOrderDO;
  }

  /**
   * 将采购单聚合根列表转换为数据对象列表
   *
   * @param purchaseOrderAggregates 采购单聚合根列表
   * @return 采购单数据对象列表
   */
  public static List<PurchaseOrderDO> toBOEntityList(
      List<PurchaseOrderAggregate> purchaseOrderAggregates) {
    if (purchaseOrderAggregates == null) {
      return null;
    }

    return purchaseOrderAggregates.stream()
        .map(PurchaseOrderConverter::toBOEntity)
        .collect(Collectors.toList());
  }
}
