package com.yxt.purchase.infrastructure.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 导入历史数据对象
 */
@Data
@TableName("import_history")
public class ImportHistoryDO {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 导入记录ID
     */
    private String importId;
    
    /**
     * 导入文件名
     */
    private String fileName;
    
    /**
     * 导入状态 0:处理中 1:全部处理成功 -1:处理部分异常 -2:全部失败
     */
    private Integer importStatus;
    
    /**
     * 导入时间
     */
    private LocalDateTime importTime;
    
    /**
     * 导入人ID
     */
    private String importBy;
    
    /**
     * 导入人姓名
     */
    private String importByName;

    
    /**
     * 错误文件路径
     */
    private String errorFilePath;
    
    /**
     * 生成的采购单号列表多个用逗号隔开
     */
    private String purchaseOrderNos;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

}
