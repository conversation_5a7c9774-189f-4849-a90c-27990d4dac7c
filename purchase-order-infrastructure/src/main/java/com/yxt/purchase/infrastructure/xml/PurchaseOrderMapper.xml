<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.purchase.infrastructure.mapper.PurchaseOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDO">
        <id column="id" property="id" />
        <result column="mer_code" property="merCode" />
        <result column="purchase_order_no" property="purchaseOrderNo" />
        <result column="state" property="state" />
        <result column="settlement_status" property="settlementStatus" />
        <result column="related_order_no" property="relatedOrderNo" />
        <result column="parent_purchase_order_no" property="parentPurchaseOrderNo" />
        <result column="auto_payment" property="autoPayment" />
        <result column="overdue_payment_time" property="overduePaymentTime" />
        <result column="company_code" property="companyCode" />
        <result column="organization_code" property="organizationCode" />
        <result column="organization_name" property="organizationName" />
        <result column="purchase_order_label" property="purchaseOrderLabel" />
        <result column="confirm_by" property="confirmBy" />
        <result column="confirm_time" property="confirmTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
        <result column="deleted" property="deleted" />
        <result column="version" property="version" />
        <result column="msg" property="msg" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, mer_code, purchase_order_no, state, settlement_status, related_order_no, parent_purchase_order_no, 
        auto_payment, overdue_payment_time, company_code, organization_code,
        organization_name, purchase_order_label, confirm_by, confirm_time, created_by, 
        updated_by, created_time, updated_time, deleted, version, msg
    </sql>

    <!-- 根据ID查询采购单 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM purchase_order
        WHERE id = #{id} AND deleted = 0
    </select>

    <!-- 根据采购单号查询采购单 -->
    <select id="selectByPurchaseOrderNo" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM purchase_order
        WHERE purchase_order_no = #{purchaseOrderNo} AND deleted = 0
    </select>

    <!-- 根据商户编码查询采购单列表 -->
    <select id="selectByMerCode" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM purchase_order
        WHERE mer_code = #{merCode} AND deleted = 0
        ORDER BY created_time DESC
    </select>

    <!-- 根据状态查询采购单列表 -->
    <select id="selectByState" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM purchase_order
        WHERE state = #{state} AND deleted = 0
        ORDER BY created_time DESC
    </select>

    <!-- 插入采购单 -->
    <insert id="insert" parameterType="com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO purchase_order (
            mer_code, purchase_order_no, state, settlement_status, related_order_no, parent_purchase_order_no, 
            auto_payment, overdue_payment_time, company_code, organization_code,
            organization_name, purchase_order_label, confirm_by, confirm_time, created_by, 
            updated_by, created_time, updated_time, deleted, version, msg
        ) VALUES (
            #{merCode}, #{purchaseOrderNo}, #{state}, #{settlementStatus}, #{relatedOrderNo}, #{parentPurchaseOrderNo}, 
            #{autoPayment}, #{overduePaymentTime}, #{companyCode}, #{organizationCode},
            #{organizationName}, #{purchaseOrderLabel}, #{confirmBy}, #{confirmTime}, #{createdBy}, 
            #{updatedBy}, #{createdTime}, #{updatedTime}, #{deleted}, #{version}, #{msg}
        )
    </insert>

    <!-- 根据ID逻辑删除采购单 -->
    <update id="deleteById">
        UPDATE purchase_order
        SET deleted = 1, updated_by = #{updatedBy}, updated_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 根据采购单号逻辑删除采购单 -->
    <update id="deleteByPurchaseOrderNo">
        UPDATE purchase_order
        SET deleted = 1, updated_by = #{updatedBy}, updated_time = NOW()
        WHERE purchase_order_no = #{purchaseOrderNo} AND deleted = 0
    </update>

    <!-- 更新采购单 -->
    <update id="update" parameterType="com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDO">
        UPDATE purchase_order
        <set>
            <if test="state != null">state = #{state},</if>
            <if test="settlementStatus != null">settlement_status = #{settlementStatus},</if>
            <if test="relatedOrderNo != null">related_order_no = #{relatedOrderNo},</if>
            <if test="autoPayment != null">auto_payment = #{autoPayment},</if>
            <if test="overduePaymentTime != null">overdue_payment_time = #{overduePaymentTime},</if>
            <if test="companyCode != null">company_code = #{companyCode},</if>
            <if test="organizationCode != null">organization_code = #{organizationCode},</if>
            <if test="organizationName != null">organization_name = #{organizationName},</if>
            <if test="purchaseOrderLabel != null">purchase_order_label = #{purchaseOrderLabel},</if>
            <if test="confirmBy != null">confirm_by = #{confirmBy},</if>
            <if test="confirmTime != null">confirm_time = #{confirmTime},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="msg != null">msg = #{msg},</if>
            updated_time = NOW(),
            version = version + 1
        </set>
        WHERE id = #{id} AND version = #{version} AND deleted = 0
    </update>
    
    <!-- 将采购单状态设置为失败 -->
    <update id="setPurchaseOrderStateToFail">
        UPDATE purchase_order
        SET 
            state = 'CANCELED',
            msg = #{failReason},
            updated_by = #{updatedBy},
            updated_time = NOW(),
            version = version + 1
        WHERE purchase_order_no = #{purchaseOrderNo} AND deleted = 0
    </update>
</mapper>
