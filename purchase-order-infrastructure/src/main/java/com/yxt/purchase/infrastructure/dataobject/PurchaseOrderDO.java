package com.yxt.purchase.infrastructure.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;


/**
 * 采购单数据对象
 */
@Data
@TableName("purchase_order")
public class PurchaseOrderDO implements Serializable {

  /**
   * 主键ID
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 采购单号 PO+yymmdd000001
   */
  private String purchaseOrderNo;

  /**
   * 采购单状态  待确认:WAIT_CONFIRM  已确认:CONFIRMED  已完成 FINISHED  已取消 CANCELED
   */
  private String state;

  private String settlementStatus;

  /**
   * 关联订单号，采购转订单后的单号
   */
  private String relatedOrderNo;

  /**
   * 父采购单号 关联一次请求或者一次导入
   */
  private String parentPurchaseOrderNo;

  /**
   * 是否超时自动支付 0:不支付  1:超时自动支付
   */
  private Integer autoPayment;

  /**
   * 设置超期自动支付时间 天为单位，0 表示超期不会自动支付
   */
  private Integer overduePaymentTime;


  /**
   * 分公司编码
   */
  private String companyCode;

  /**
   * 分公司名称
   */
  private String companyName;

  /**
   * 所属机构编码
   */
  private String organizationCode;

  /**
   * 所属机构名称
   */
  private String organizationName;

  /**
   * 采购单标签 办公用品:CONSUMABLES 导入铺货: MULTIPLE_STORE
   */
  private String purchaseOrderLabel;

  /**
   * 确认人
   */
  private String confirmBy;

  /**
   * 确认时间
   */
  private LocalDateTime confirmTime;

  /**
   * 创建人 申请人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 创建时间
   */
  private LocalDateTime createdTime;

  /**
   * 更新时间
   */
  private LocalDateTime updatedTime;

  /**
   * 是否删除 0：没有删除 1: 删除
   */
  private Integer deleted;

  /**
   * 数据版本，每次update+1
   */
  private Long version;
}
