package com.yxt.purchase.infrastructure.event;


import org.springframework.context.ApplicationEvent;

/**
 * 提单失败的错误处理
 */

public class SettlementFailEvent extends ApplicationEvent {

  public SettlementFailEvent(Object source) {
    super(source);
  }

  private String purchaseOrderNo;

  private String msg;

  private String userId;

  public String getPurchaseOrderNo() {
    return purchaseOrderNo;
  }

  public String getMsg() {
    return msg;
  }

  public void setPurchaseOrderNo(String purchaseOrderNo) {
    this.purchaseOrderNo = purchaseOrderNo;
  }

  public void setMsg(String msg) {
    this.msg = msg;
  }


}
