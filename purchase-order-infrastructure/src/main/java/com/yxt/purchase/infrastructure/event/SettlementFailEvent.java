package com.yxt.purchase.infrastructure.event;


import org.springframework.context.ApplicationEvent;

/**
 * 提单失败的错误处理
 */

public class SettlementFailEvent extends ApplicationEvent {

  public SettlementFailEvent(Object source) {
    super(source);
  }

  private String purchaseOrderNo;

  /**
   * 提单失败的错误信息
   */
  private String msg;
  /**
   * 提单失败的错误信息 明细信息
   */
  private String subMessage;

  private String userId;

  public String getUserId() {
    return userId;
  }

  public void setUserId(String userId) {
    this.userId = userId;
  }

  public String getPurchaseOrderNo() {
    return purchaseOrderNo;
  }

  public String getMsg() {
    return msg;
  }

  public String getSubMessage() {
    return subMessage;
  }


  public void setPurchaseOrderNo(String purchaseOrderNo) {
    this.purchaseOrderNo = purchaseOrderNo;
  }

  public void setMsg(String msg) {
    this.msg = msg;
  }


  public void setSubMessage(String subMessage) {
    this.subMessage = subMessage;
  }


}
