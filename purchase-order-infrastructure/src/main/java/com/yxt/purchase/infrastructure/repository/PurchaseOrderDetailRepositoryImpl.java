package com.yxt.purchase.infrastructure.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.merchandise.search.common.model.dto.b2b.response.CommodityB2bStoreSpecBatchQueryResp;
import com.yxt.purchase.domain.PurchaseOrderDetailAggregate;
import com.yxt.purchase.domain.repository.PurchaseOrderDetailRepository;
import com.yxt.purchase.infrastructure.api.MerchandiseService;
import com.yxt.purchase.infrastructure.api.entity.CommodityStockAndPriceQueryModel;
import com.yxt.purchase.infrastructure.converter.PurchaseOrderDetailConverter;
import com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDetailDO;
import com.yxt.purchase.infrastructure.mapper.PurchaseOrderDetailMapper;
import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 采购商品明细仓储实现类
 */
@Repository
public class PurchaseOrderDetailRepositoryImpl implements PurchaseOrderDetailRepository {

  @Autowired
  private PurchaseOrderDetailMapper purchaseOrderDetailMapper;

  @Autowired
  private PurchaseOrderDetailConverter purchaseOrderDetailConverter;

  @Autowired
  private MerchandiseService merchandiseService;

  @Override
  public PurchaseOrderDetailAggregate save(PurchaseOrderDetailAggregate detail) {
    PurchaseOrderDetailDO detailDO = purchaseOrderDetailConverter.toDO(detail);
    purchaseOrderDetailMapper.insert(detailDO);
    detail.setId(detailDO.getId());
    return detail;
  }


  @Override
  public Optional<PurchaseOrderDetailAggregate> findById(Long id) {
    PurchaseOrderDetailDO detailDO = purchaseOrderDetailMapper.selectById(id);
    return Optional.ofNullable(detailDO)
        .map(purchaseOrderDetailConverter::toAggregate);
  }

  @Override
  public List<PurchaseOrderDetailAggregate> findByPurchaseOrderNo(String purchaseOrderNo) {
    LambdaQueryWrapper<PurchaseOrderDetailDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(PurchaseOrderDetailDO::getPurchaseOrderNo, purchaseOrderNo);
    List<PurchaseOrderDetailDO> detailDOs = purchaseOrderDetailMapper.selectList(queryWrapper);
    return purchaseOrderDetailConverter.toAggregateList(detailDOs);
  }

  @Override
  public List<PurchaseOrderDetailAggregate> findByPurchaseOrderNos(List<String> purchaseOrderNos) {
    LambdaQueryWrapper<PurchaseOrderDetailDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.in(PurchaseOrderDetailDO::getPurchaseOrderNo, purchaseOrderNos);
    List<PurchaseOrderDetailDO> detailDOs = purchaseOrderDetailMapper.selectList(queryWrapper);

    return purchaseOrderDetailConverter.toAggregateList(detailDOs);
  }

  @Override
  public PurchaseOrderDetailAggregate update(PurchaseOrderDetailAggregate detail) {
    PurchaseOrderDetailDO detailDO = purchaseOrderDetailConverter.toDO(detail);
    purchaseOrderDetailMapper.updateById(detailDO);
    return detail;
  }

  @Override
  public boolean deleteById(Long id) {
    int result = purchaseOrderDetailMapper.deleteById(id);
    return result > 0;
  }

  @Override
  public boolean deleteByPurchaseOrderNo(String purchaseOrderNo) {
    LambdaQueryWrapper<PurchaseOrderDetailDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(PurchaseOrderDetailDO::getPurchaseOrderNo, purchaseOrderNo);
    int result = purchaseOrderDetailMapper.delete(queryWrapper);
    return result > 0;
  }

  @Override
  public List<PurchaseOrderDetailAggregate> findByPurchaseOrderNoAndStatus(String purchaseOrderNo,
      String status, Integer currentPage, Integer pageSize, String erpName,String erpCode) {
    LambdaQueryWrapper<PurchaseOrderDetailDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(PurchaseOrderDetailDO::getPurchaseOrderNo, purchaseOrderNo);
    if (StrUtil.isNotBlank(status)) {
      queryWrapper.eq(PurchaseOrderDetailDO::getStatus, status);
    }
    if (StrUtil.isNotBlank(erpName)) {
      queryWrapper.like(PurchaseOrderDetailDO::getErpName, erpName);
    }
    if (StrUtil.isNotBlank(erpCode)) {
      queryWrapper.eq(PurchaseOrderDetailDO::getErpCode, erpCode);
    }
    // 计算分页参数
    Page<PurchaseOrderDetailDO> page = new Page<>(currentPage, pageSize);

    // 执行分页查询
    IPage<PurchaseOrderDetailDO> detailPage = purchaseOrderDetailMapper.selectPage(page,
        queryWrapper);

    // 转换为聚合根列表
    return purchaseOrderDetailConverter.toAggregateList(detailPage.getRecords());
  }

  @Override
  public long countByPurchaseOrderNoAndStatus(String purchaseOrderNo, String status,
      String erpName) {
    LambdaQueryWrapper<PurchaseOrderDetailDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(PurchaseOrderDetailDO::getPurchaseOrderNo, purchaseOrderNo);
    if (status != null && !status.isEmpty()) {
      queryWrapper.eq(PurchaseOrderDetailDO::getStatus, status);
    }
    if (StrUtil.isNotBlank(erpName)) {
      queryWrapper.like(PurchaseOrderDetailDO::getErpName, erpName);
    }
    return purchaseOrderDetailMapper.selectCount(queryWrapper);
  }

  @Override
  public void checkCommodityIsValid(String storeCode, String erpCode, String qty) {

    List<CommodityB2bStoreSpecBatchQueryResp> commodity = merchandiseService.b2BCommodityBatchQuery(
        storeCode, Collections.singletonList(erpCode));

    if (CollUtil.isEmpty(commodity)) {
      throw new RuntimeException("商品不存在");
    }
    CommodityB2bStoreSpecBatchQueryResp item = commodity.get(0);
    if (item.getPurchaseUpperLimit() != null
        && Integer.compare(item.getPurchaseUpperLimit(), Integer.parseInt(qty)) < 0) {
      throw new RuntimeException("超过商品最大采购数量");
    }
    if (item.getPurchaseBundleSize() != null
        && Integer.parseInt(qty) % item.getPurchaseBundleSize() > 0) {
      throw new RuntimeException(
          "采购数量不符合加购数量，采购数量只能是" + item.getPurchaseBundleSize() + "倍数");
    }
    if (item.getWareStock() == null || item.getWareStock() < Integer.parseInt(qty)) {
      throw new RuntimeException(
          "仓库库存不足，当前库存" + (item.getWareStock() != null ? item.getWareStock() : 0));
    }
  }
}