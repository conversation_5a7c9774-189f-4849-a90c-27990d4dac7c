package com.yxt.purchase.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.purchase.domain.PurchaseOrderDetailAggregate;
import com.yxt.purchase.domain.repository.PurchaseOrderDetailRepository;
import com.yxt.purchase.infrastructure.converter.PurchaseOrderDetailConverter;
import com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDetailDO;
import com.yxt.purchase.infrastructure.mapper.PurchaseOrderDetailMapper;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 采购商品明细仓储实现类
 */
@Repository
public class PurchaseOrderDetailRepositoryImpl implements PurchaseOrderDetailRepository {

  @Autowired
  private PurchaseOrderDetailMapper purchaseOrderDetailMapper;

  @Autowired
  private PurchaseOrderDetailConverter  purchaseOrderDetailConverter;

  @Override
  public PurchaseOrderDetailAggregate save(PurchaseOrderDetailAggregate detail) {
    PurchaseOrderDetailDO detailDO = purchaseOrderDetailConverter.toDO(detail);
    purchaseOrderDetailMapper.insert(detailDO);
    detail.setId(detailDO.getId());
    return detail;
  }

  public  void save(List<PurchaseOrderDetailDO> detailDOList){


  }

  @Override
  public Optional<PurchaseOrderDetailAggregate> findById(Long id) {
    PurchaseOrderDetailDO detailDO = purchaseOrderDetailMapper.selectById(id);
    return Optional.ofNullable(detailDO)
        .map(purchaseOrderDetailConverter::toAggregate);
  }

  @Override
  public List<PurchaseOrderDetailAggregate> findByPurchaseOrderNo(String purchaseOrderNo) {
    LambdaQueryWrapper<PurchaseOrderDetailDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(PurchaseOrderDetailDO::getPurchaseOrderNo, purchaseOrderNo);
    List<PurchaseOrderDetailDO> detailDOs = purchaseOrderDetailMapper.selectList(queryWrapper);
    return purchaseOrderDetailConverter.toAggregateList(detailDOs);
  }

  @Override
  public List<PurchaseOrderDetailAggregate> findByErpCode(String erpCode) {
    LambdaQueryWrapper<PurchaseOrderDetailDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(PurchaseOrderDetailDO::getErpCode, erpCode);
    List<PurchaseOrderDetailDO> detailDOs = purchaseOrderDetailMapper.selectList(queryWrapper);
    return purchaseOrderDetailConverter.toAggregateList(detailDOs);
  }

  @Override
  public PurchaseOrderDetailAggregate update(PurchaseOrderDetailAggregate detail) {
    PurchaseOrderDetailDO detailDO = purchaseOrderDetailConverter.toDO(detail);
    purchaseOrderDetailMapper.updateById(detailDO);
    return detail;
  }

  @Override
  public boolean deleteById(Long id) {
    int result = purchaseOrderDetailMapper.deleteById(id);
    return result > 0;
  }

  @Override
  public boolean deleteByPurchaseOrderNo(String purchaseOrderNo) {
    LambdaQueryWrapper<PurchaseOrderDetailDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(PurchaseOrderDetailDO::getPurchaseOrderNo, purchaseOrderNo);
    int result = purchaseOrderDetailMapper.delete(queryWrapper);
    return result > 0;
  }

  @Override
  public List<PurchaseOrderDetailAggregate> findByPurchaseOrderNoAndStatus(String purchaseOrderNo, String status, Integer currentPage, Integer pageSize) {
    LambdaQueryWrapper<PurchaseOrderDetailDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(PurchaseOrderDetailDO::getPurchaseOrderNo, purchaseOrderNo);
    if (status != null && !status.isEmpty()) {
      queryWrapper.eq(PurchaseOrderDetailDO::getStatus, status);
    }

    // 计算分页参数
    Page<PurchaseOrderDetailDO> page = new Page<>(currentPage, pageSize);

    // 执行分页查询
    IPage<PurchaseOrderDetailDO> detailPage = purchaseOrderDetailMapper.selectPage(page, queryWrapper);

    // 转换为聚合根列表
    return purchaseOrderDetailConverter.toAggregateList(detailPage.getRecords());
  }

  @Override
  public long countByPurchaseOrderNoAndStatus(String purchaseOrderNo, String status) {
    LambdaQueryWrapper<PurchaseOrderDetailDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(PurchaseOrderDetailDO::getPurchaseOrderNo, purchaseOrderNo);
    if (status != null && !status.isEmpty()) {
      queryWrapper.eq(PurchaseOrderDetailDO::getStatus, status);
    }

    return purchaseOrderDetailMapper.selectCount(queryWrapper);
  }
}