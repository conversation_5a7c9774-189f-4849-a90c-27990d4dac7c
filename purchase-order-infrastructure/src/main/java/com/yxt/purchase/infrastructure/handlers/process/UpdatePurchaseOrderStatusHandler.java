package com.yxt.purchase.infrastructure.handlers.process;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.domain.entity.HandlerContext;
import com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler;
import com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDO;
import com.yxt.purchase.infrastructure.mapper.PurchaseOrderMapper;
import com.yxt.purchase.infrastructure.mapper.impl.PurchaseOrderMapperService;
import java.time.LocalDateTime;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class UpdatePurchaseOrderStatusHandler extends
    AbstractPurchaseOrderHandler<HandlerContext> {

  private final PurchaseOrderMapper purchaseOrderMapper;


  public UpdatePurchaseOrderStatusHandler(PurchaseOrderMapper purchaseOrderMapper) {
    this.purchaseOrderMapper = purchaseOrderMapper;
  }

  @Override
  public void doHandleReal(HandlerContext context) {
    // 目前主要处理订单状态的更新 以及商品信息更新
    try {
      for (PurchaseOrderAggregate purchaseOrderAggregate : context.getAggregates()) {
        PurchaseOrderDO purchaseOrderDO = new PurchaseOrderDO();
        purchaseOrderDO.setPurchaseOrderNo(
            purchaseOrderAggregate.getPurchaseOrderNo().getPurchaseOrderNo());
        if (StrUtil.isNotBlank(purchaseOrderAggregate.getRelatedOrderNo())) {
          purchaseOrderDO.setRelatedOrderNo(purchaseOrderAggregate.getRelatedOrderNo());
        }
        purchaseOrderDO.setState(purchaseOrderAggregate.getState().getCode());
        purchaseOrderDO.setUpdatedTime(LocalDateTime.now());
        purchaseOrderDO.setUpdatedBy(purchaseOrderAggregate.getModifiedBy().getUserId());
        purchaseOrderDO.setMsg(purchaseOrderAggregate.getMsg());
        purchaseOrderDO.setSettlementStatus(purchaseOrderAggregate.getSettlementStatus().getCode());
        purchaseOrderMapper.update(purchaseOrderDO);
      }
    } catch (Exception ex) {
      log.error("更新采购单状态异常,request:{}", JSON.toJSONString(context), ex);
    }

  }
}
