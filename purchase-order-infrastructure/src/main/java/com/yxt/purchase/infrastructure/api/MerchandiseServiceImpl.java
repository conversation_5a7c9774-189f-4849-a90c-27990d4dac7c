package com.yxt.purchase.infrastructure.api;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.merchandise.search.common.model.dto.b2b.request.CommodityB2bStoreSpecBatchQueryReq;
import com.yxt.merchandise.search.common.model.dto.b2b.request.StockAndPriceQueryReq;
import com.yxt.merchandise.search.common.model.dto.b2b.response.CommodityB2bStoreSpecBatchQueryResp;
import com.yxt.merchandise.search.common.model.dto.b2b.response.StockAndPriceQueryResp;
import com.yxt.merchandise.search.common.model.dto.request.StoreErpCodesReq;
import com.yxt.merchandise.search.sdk.api.CommodityStoreSpecApi;
import com.yxt.merchandise.search.sdk.api.b2b.CommodityB2bStoreSpecApi;
import com.yxt.purchase.infrastructure.api.entity.CommodityB2bStoreSpecBatchQueryModel;
import com.yxt.purchase.infrastructure.api.entity.CommodityStockAndPriceQueryModel;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.IntStream;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class MerchandiseServiceImpl implements MerchandiseService {

  private final CommodityB2bStoreSpecApi commodityB2bStoreSpecApi;

  private final CommodityStoreSpecApi commodityStoreSpecApi;

  /**
   * 批量查询门店商品
   *
   * @param storeCode 门店编码
   * @param erpCodes  商品编码集合
   * @return 门店商品信息
   */
  @Override
  public List<CommodityB2bStoreSpecBatchQueryResp> b2BCommodityBatchQuery(String storeCode,
      List<String> erpCodes) {
    if(StrUtil.isBlank(storeCode) || CollUtil.isEmpty(erpCodes)){
      return new ArrayList<>();
    }
    int maxCommoditySize = 2000;
    // 如果 erpCodes 超过500个，则分批次查询
    if (erpCodes.size() > maxCommoditySize) {
      List<CommodityB2bStoreSpecBatchQueryResp> result = new ArrayList<>();
      IntStream.range(0, erpCodes.size() / maxCommoditySize + 1).forEach(i -> {
        int fromIndex = i * maxCommoditySize;
        int toIndex = i * maxCommoditySize + maxCommoditySize;
        if(toIndex>erpCodes.size()){
          toIndex=erpCodes.size();
        }
        List<String> subList = erpCodes.subList(fromIndex,
            toIndex);
        result.addAll(getB2bStoreSpecBatchQueryRespList(subList, storeCode));
      });
      return result;
    } else {
      return getB2bStoreSpecBatchQueryRespList(erpCodes, storeCode);
    }
  }

  private List<CommodityB2bStoreSpecBatchQueryResp> getB2bStoreSpecBatchQueryRespList(
      List<String> erpCodes,
      String storeCode
  ) {
    CommodityB2bStoreSpecBatchQueryReq req = new CommodityB2bStoreSpecBatchQueryReq();
    req.setStoreCode(storeCode);
    req.setErpCodeList(erpCodes);
    ResponseBase<List<CommodityB2bStoreSpecBatchQueryResp>> responseBase = commodityB2bStoreSpecApi.batchQuery(
        req);
    if (Objects.isNull(responseBase) || !responseBase.checkSuccess()) {
      throw new YxtBizException(
          "查询门店商品异常，" + (StrUtil.isBlank(responseBase.getMsg()) ? "请稍后再试"
              : responseBase.getMsg()));
    }
    return responseBase.getData();
  }

  /**
   * 查询商品库存和价格
   *
   * @param storeCode 门店编码
   * @param erpCodes  商品编码集合
   * @return 商品库存和价格
   */
  @Override
  public List<CommodityStockAndPriceQueryModel> queryStockAndPrice(String storeCode,
      List<String> erpCodes) {
    if(StrUtil.isBlank(storeCode) || CollUtil.isEmpty(erpCodes)){
      return new ArrayList<>();
    }
    int maxCommoditySize = 2000;
    // 如果 erpCodes 超过500个，则分批次查询
    if (erpCodes.size() > maxCommoditySize) {
      List<CommodityStockAndPriceQueryModel> result = new ArrayList<>();
      IntStream.range(0, erpCodes.size() / maxCommoditySize + 1).forEach(i -> {
        int fromIndex = i * maxCommoditySize;
        int toIndex = i * maxCommoditySize + maxCommoditySize;
        if(toIndex>erpCodes.size()){
          toIndex=erpCodes.size();
        }
        List<String> subList = erpCodes.subList(fromIndex,
            toIndex);
        result.addAll(getStockAndPriceRespList(subList, storeCode));
      });
      return result;
    } else {
      return getStockAndPriceRespList(erpCodes, storeCode);
    }
  }

    private List<CommodityStockAndPriceQueryModel> getStockAndPriceRespList(List<String> erpCodes,
        String storeCode) {
      StockAndPriceQueryReq req = new StockAndPriceQueryReq();
      req.setStoreCode(storeCode);
      req.setErpCodeList(erpCodes);
      ResponseBase<List<StockAndPriceQueryResp>> responseBase = commodityB2bStoreSpecApi.queryStockAndPrice(req);
      if(!responseBase.checkSuccess()){
        throw new YxtBizException("查询商品库存和价格异常，请稍后再试");
      }
      return BeanUtil.copyToList(responseBase.getData(), CommodityStockAndPriceQueryModel.class);
    }

  /**
   * 门店商品经营范围
   *
   * @param erpCodes  商品编码集合
   * @param storeCode 门店id
   * @return 不在经营范围内的商品信息
   */
  @Override
  public List<String> checkCommodityIsOutSide(List<String> erpCodes, String storeCode) {
    if (StrUtil.isBlank(storeCode)) {
      throw new YxtBizException("必须要检查，门店编码不存在");
    }

    if (CollUtil.isEmpty(erpCodes)) {
      return Collections.emptyList();
    }
    // 如果 erpCodes 超过2000个，则分批次查询
    int maxCommoditySize = 2000;
    if (erpCodes.size() > maxCommoditySize) {
      List<String> result = new ArrayList<>();
      IntStream.range(0, erpCodes.size() / maxCommoditySize + 1).forEach(i -> {
        int fromIndex = i * maxCommoditySize;
        int toIndex = i * maxCommoditySize + maxCommoditySize;
        if(toIndex>erpCodes.size()){
          toIndex=erpCodes.size();
        }
        List<String> subList = erpCodes.subList(fromIndex,
            toIndex);
        List<String> errorErpCodes = getOutsideByBusSCope(subList, storeCode);
        if (CollUtil.isNotEmpty(errorErpCodes)) {
          result.addAll(errorErpCodes);
        }
      });
      return result;
    } else {
      return getOutsideByBusSCope(erpCodes, storeCode);
    }
  }


  private List<String> getOutsideByBusSCope(List<String> erpCodes, String storeCode) {
    StoreErpCodesReq req = new StoreErpCodesReq();
    req.setStoreCode(storeCode);
    req.setErpCodes(erpCodes);
    ResponseBase<List<String>> responseBase = commodityStoreSpecApi.getOutsideByBusSCope(req);
    if (!responseBase.checkSuccess()) {
      throw new YxtBizException("查询门店商品异常，请稍后再试。" + responseBase.getMsg());
    }
    return responseBase.getData();
  }

}
