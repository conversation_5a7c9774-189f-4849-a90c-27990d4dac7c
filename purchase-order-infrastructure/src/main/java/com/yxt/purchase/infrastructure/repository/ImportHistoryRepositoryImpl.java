package com.yxt.purchase.infrastructure.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.purchase.domain.ImportHistoryAggregate;
import com.yxt.purchase.domain.command.ImportHistoryQueryCommand;
import com.yxt.purchase.domain.repository.ImportHistoryRepository;
import com.yxt.purchase.infrastructure.converter.ImportHistoryConverter;
import com.yxt.purchase.infrastructure.dataobject.ImportHistoryDO;
import com.yxt.purchase.infrastructure.feign.MiddleIdClient;
import com.yxt.purchase.infrastructure.mapper.ImportHistoryMapper;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 导入历史仓储实现类
 */
@Slf4j
@Repository
public class ImportHistoryRepositoryImpl implements ImportHistoryRepository {

  @Autowired
  private ImportHistoryMapper importHistoryMapper;

  @Autowired
  private ImportHistoryConverter importHistoryConverter;
  @Autowired
  private MiddleIdClient middleIdClient;

  @Override
  public ImportHistoryAggregate save(ImportHistoryAggregate importHistoryAggregate) {

    Long id = middleIdClient.getId(1).get(0);
    importHistoryAggregate.setImportId(String.valueOf(id));
    ImportHistoryDO importHistoryDO = importHistoryConverter.toDO(importHistoryAggregate);
    importHistoryDO.setCreatedTime(LocalDateTime.now());
    importHistoryDO.setUpdatedTime(LocalDateTime.now());

    int rows = importHistoryMapper.insert(importHistoryDO);
    if (rows > 0) {
      return importHistoryAggregate;
    }

    throw new RuntimeException("保存导入历史记录失败");
  }

  @Override
  public void update(ImportHistoryAggregate importHistoryAggregate) {
    ImportHistoryDO importHistoryDO = importHistoryConverter.toDO(importHistoryAggregate);
    importHistoryDO.setUpdatedTime(LocalDateTime.now());

    LambdaQueryWrapper<ImportHistoryDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(ImportHistoryDO::getImportId, importHistoryAggregate.getImportId());

    importHistoryMapper.update(importHistoryDO, queryWrapper);

  }

  @Override
  public ImportHistoryAggregate findByImportId(String importId) {
    LambdaQueryWrapper<ImportHistoryDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(ImportHistoryDO::getImportId, importId);

    ImportHistoryDO importHistoryDO = importHistoryMapper.selectOne(queryWrapper);
    return importHistoryConverter.toDomain(importHistoryDO);
  }


  @Override
  public List<ImportHistoryAggregate> queryImportHistory(ImportHistoryQueryCommand command) {
    LambdaQueryWrapper<ImportHistoryDO> queryWrapper = new LambdaQueryWrapper<>();

    // 添加查询条件
    if (StrUtil.isNotBlank(command.getUserId())) {
      queryWrapper.eq(ImportHistoryDO::getImportBy, command.getUserId());
    }
    // 按导入时间倒序排序
    queryWrapper.orderByDesc(ImportHistoryDO::getImportTime);

    List<ImportHistoryDO> importHistoryDOs = importHistoryMapper.selectList(
        queryWrapper.last("LIMIT 50"));
    return importHistoryConverter.toDomainList(importHistoryDOs);
  }
}
