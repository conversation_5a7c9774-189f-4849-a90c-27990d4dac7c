package com.yxt.purchase.infrastructure.handlers.process;

import cn.hutool.core.collection.CollUtil;
import com.yxt.merchandise.search.common.model.dto.b2b.response.CommodityB2bStoreSpecBatchQueryResp;
import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.domain.entity.CreatePurchaseOrderDetailModel;
import com.yxt.purchase.domain.entity.HandlerContext;
import com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler;
import com.yxt.purchase.enums.CommodityTypeEnum;
import com.yxt.purchase.enums.PurchaseOrderLabelEnum;
import com.yxt.purchase.enums.SettlementStatusEnum;
import com.yxt.purchase.infrastructure.api.MerchandiseService;
import com.yxt.purchase.infrastructure.api.entity.CommodityStockAndPriceQueryModel;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品信息检查处理器，主要检查商品信息是否正确 包括 编码 数量 以及门店商品经营范围
 */

public class CommodityCheckHandler extends
    AbstractPurchaseOrderHandler<HandlerContext> {


  private final MerchandiseService merchandiseService;

  public CommodityCheckHandler(MerchandiseService merchandiseService) {
    this.merchandiseService = merchandiseService;
  }


  @Override
  public void doHandleReal(HandlerContext context) {

    PurchaseOrderAggregate purchaseOrderAggregate = context.getAggregates().get(0);
    List<String> erpCodes = purchaseOrderAggregate.getPurchaseOrderDetailList().stream()
        .map(CreatePurchaseOrderDetailModel::getErpCode).collect(
            Collectors.toList());
    // 先检查是否符合门店商品经营范围 经营范围可以通过当前查询商品详情的接口来检测，因为不属于当前门店的商品不能查到
    // 查询信息是否合法有效
    List<CommodityB2bStoreSpecBatchQueryResp> commodityModels =
        merchandiseService.b2BCommodityBatchQuery(purchaseOrderAggregate.getOrganizationCode(),
            erpCodes);
    // 查询信息用来完善订单商品信息
    if (CollUtil.isEmpty(commodityModels)) {
      // 商品信息都有问题 后续流程直接不走了
      purchaseOrderAggregate.setMsg("商品信息不存在");
      purchaseOrderAggregate.setSettlementStatus(SettlementStatusEnum.CHECK_FAILED);
      return;
    }
    // 将商品信息正确的处理掉
    for (CommodityB2bStoreSpecBatchQueryResp commodityModel : commodityModels) {
      purchaseOrderAggregate.getPurchaseOrderDetailList().stream()
          .filter(item -> item.getErpCode().equals(commodityModel.getErpCode()))
          .forEach(item -> {
            item.setStatus("0");
            item.setEx_msg("");
            // 查询是否满足商品最小加购数量以及最大采购数量
            if (commodityModel.getPurchaseUpperLimit() != null
                && commodityModel.getPurchaseUpperLimit() > 0) {
              if (item.getQty().intValue() > commodityModel.getPurchaseUpperLimit()) {
                item.setStatus("-1");
                item.setEx_msg("采购数量不能大于" + commodityModel.getPurchaseUpperLimit());
              }
            }
            if (commodityModel.getPurchaseBundleSize() != null
                && commodityModel.getPurchaseBundleSize() > 0) {
              if (item.getQty().intValue() % commodityModel.getPurchaseBundleSize() != 0) {
                item.setStatus("-1");
                item.setEx_msg(
                    "采购数量必须是" + commodityModel.getPurchaseBundleSize() + "的整倍数");
              }
            }
            item.setErpName(commodityModel.getName());
            item.setCommoditySpec(commodityModel.getSpecValue());
            item.setIsColdChain(commodityModel.getIsColdChain());
            item.setCommodityType(commodityModel.getIsOfficial() ? CommodityTypeEnum.CONSUMABLES
                : CommodityTypeEnum.COMMODITY);
          });
    }
    // 缩小范围
    erpCodes = commodityModels.stream().map(CommodityB2bStoreSpecBatchQueryResp::getErpCode)
        .collect(Collectors.toList());
    // 检查商品库存是否正常
    List<CommodityStockAndPriceQueryModel> stockAndPriceModels =
        merchandiseService.queryStockAndPrice(purchaseOrderAggregate.getOrganizationCode(),
            erpCodes);
    // 将没有匹配到信息的商品设置为失败
    for (CreatePurchaseOrderDetailModel item : purchaseOrderAggregate.getPurchaseOrderDetailList()) {
      if (!item.getStatus().equals("1")) {
        continue;
      }
      CommodityStockAndPriceQueryModel stockAndPriceQueryModel = stockAndPriceModels.
          stream().
          filter(model -> model.getErpCode().equals(item.getErpCode())).
          findFirst().orElse(null);
      if (stockAndPriceQueryModel == null) {
        item.setStatus("-1");
        item.setEx_msg("库存信息异常");
      } else {
        // 比较商品库存，如果库存小于需求数量，则设置为失败
        if (stockAndPriceQueryModel.getStock() < item.getQty().intValue()) {
          item.setStatus("-1");
          item.setEx_msg("库存不足,仓库库存" + stockAndPriceQueryModel.getStock());
        }
      }
    }

    // 根据商品拆分聚合根
    List<PurchaseOrderAggregate> purchaseOrderAggregates = groupByProductType(
        purchaseOrderAggregate);
    // 后续流程直接使用拆分过后的聚合根信息
    context.setAggregates(purchaseOrderAggregates);
  }


  /**
   * 根据商品分组生成新的采购单聚合根
   *
   * @param originalAggregate 原始采购单聚合根
   * @return 分组后的采购单聚合根列表
   */
  public List<PurchaseOrderAggregate> groupByProductType(PurchaseOrderAggregate originalAggregate) {
    List<PurchaseOrderAggregate> result = new ArrayList<>();

    if (originalAggregate == null || originalAggregate.getPurchaseOrderDetailList() == null
        || originalAggregate.getPurchaseOrderDetailList().isEmpty()) {
      return result;
    }

    // 1. 按商品类型分组 (普通商品 vs 消耗品)
    Map<CommodityTypeEnum, List<CreatePurchaseOrderDetailModel>> groupByType =
        originalAggregate.getPurchaseOrderDetailList().stream()
            .collect(Collectors.groupingBy(CreatePurchaseOrderDetailModel::getCommodityType));

    // 2. 对每种商品类型，再按是否冷链分组
    for (Map.Entry<CommodityTypeEnum, List<CreatePurchaseOrderDetailModel>> typeEntry : groupByType.entrySet()) {
      CommodityTypeEnum commodityType = typeEntry.getKey();
      List<CreatePurchaseOrderDetailModel> itemsOfType = typeEntry.getValue();

      Map<Boolean, List<CreatePurchaseOrderDetailModel>> groupByColdChain =
          itemsOfType.stream()
              .collect(Collectors.groupingBy(item ->
                  item.getIsColdChain() != null ? item.getIsColdChain() : false));
      // 3. 为每个分组创建新的采购单聚合根
      for (Map.Entry<Boolean, List<CreatePurchaseOrderDetailModel>> coldChainEntry : groupByColdChain.entrySet()) {
        Boolean isColdChain = coldChainEntry.getKey();
        List<CreatePurchaseOrderDetailModel> items = coldChainEntry.getValue();

        // 创建新的采购单聚合根
        PurchaseOrderAggregate newAggregate = createNewAggregate(originalAggregate, commodityType,
            isColdChain, items, originalAggregate.getParentPurchaseOrderNo());
        result.add(newAggregate);
      }
    }
    // 拆分后 需要根据信息是否异常处理 主表信息
    for (PurchaseOrderAggregate aggregate : result) {
      if (aggregate.getPurchaseOrderDetailList().stream()
          .anyMatch(item -> item.getStatus().equals("-1"))) {
        aggregate.setMsg("商品校验不通过");
        aggregate.setSettlementStatus(SettlementStatusEnum.CHECK_FAILED);
      }
    }

    return result;
  }

  /**
   * 创建新的采购单聚合根
   *
   * @param originalAggregate     原始采购单聚合根
   * @param commodityType         商品类型
   * @param isColdChain           是否冷链
   * @param items                 商品明细列表
   * @param parentPurchaseOrderNo 父采购单号
   * @return 新的采购单聚合根
   */
  private PurchaseOrderAggregate createNewAggregate(
      PurchaseOrderAggregate originalAggregate,
      CommodityTypeEnum commodityType,
      Boolean isColdChain,
      List<CreatePurchaseOrderDetailModel> items,
      String parentPurchaseOrderNo) {

    PurchaseOrderAggregate newAggregate = new PurchaseOrderAggregate();

    // 复制基本属性
    newAggregate.setMerCode(originalAggregate.getMerCode());
    newAggregate.setState(originalAggregate.getState());
    newAggregate.setRelatedOrderNo(originalAggregate.getRelatedOrderNo());
    newAggregate.setParentPurchaseOrderNo(parentPurchaseOrderNo);
    newAggregate.setAutoPayment(originalAggregate.getAutoPayment());
    newAggregate.setOverduePaymentTime(originalAggregate.getOverduePaymentTime());
    newAggregate.setAutoConfirm(originalAggregate.getAutoConfirm());
    newAggregate.setSettlementStatus(originalAggregate.getSettlementStatus());
    newAggregate.setMsg(originalAggregate.getMsg());
    newAggregate.setCompanyCode(originalAggregate.getCompanyCode());
    newAggregate.setCompanyName(originalAggregate.getCompanyName());
    newAggregate.setOrganizationCode(originalAggregate.getOrganizationCode());
    newAggregate.setOrganizationName(originalAggregate.getOrganizationName());

    // 设置创建时间和修改时间
    LocalDateTime now = LocalDateTime.now();
    newAggregate.setCreatedTime(now);
    newAggregate.setModifiedTime(now);

    // 设置创建人、确认人和修改人
    newAggregate.setCreatedBy(originalAggregate.getCreatedBy());
    newAggregate.setApprovedBy(originalAggregate.getApprovedBy());
    newAggregate.setModifiedBy(originalAggregate.getModifiedBy());

    // 设置确认时间
    newAggregate.setConfirmTime(originalAggregate.getConfirmTime());
    newAggregate.setConfirmBy(originalAggregate.getConfirmBy());

    // 根据商品类型设置采购单标签
    if (commodityType == CommodityTypeEnum.CONSUMABLES) {
      newAggregate.setPurchaseOrderLabel(PurchaseOrderLabelEnum.CONSUMABLES);
    } else {
      // 根据是否冷链设置不同的标签
      newAggregate.setPurchaseOrderLabel(isColdChain ?
          PurchaseOrderLabelEnum.MULTIPLE_STORE : PurchaseOrderLabelEnum.NEW_STORE_STOCK);
    }

    newAggregate.setAddress(originalAggregate.getAddress());
    // 设置商品明细列表
    newAggregate.setPurchaseOrderDetailList(new ArrayList<>(items));

    return newAggregate;
  }
}
