package com.yxt.purchase.infrastructure.api;

import com.yxt.org.read.opensdk.emp.dto.response.EmployeeResDTO;
import com.yxt.org.read.opensdk.org.dto.response.OrgInfoQueryOpenResDTO;
import com.yxt.purchase.infrastructure.api.entity.OperatorValueObject;
import com.yxt.purchase.infrastructure.api.entity.QueryUserAuthOrgResModel;
import java.util.List;
import java.util.Map;

public interface MiddleBaseInfoService {

  /**
   *  获取用户信息
   * @param userId
   * @return
   */
  OperatorValueObject getOperatorInfo(String userId, String merCode);

  /**
   *  获取用户信息
   * @param code
   * @return
   */
  OperatorValueObject getOperatorInfoByCode(String code, String merCode);

  /**
   * 获取用户拥有的门店权限
   * @param userId
   * @return
   */
  List<QueryUserAuthOrgResModel> queryUserAuthOrg(String userId);

  /**
   * 获取组织信息
   * @param orgCodes
   * @param merCode
   * @return
   */
  List<OrgInfoQueryOpenResDTO> getOrgInfo(List<String> orgCodes, String merCode);

  /**
   * 获取员工信息
   * @param userIds
   * @param merCode
   * @return
   */
  Map<String, EmployeeResDTO> getSysEmployeeInfo(List<String> userIds, String merCode);

   EmployeeResDTO getSysEmployeeInfo(String userId, String merCode);

}
