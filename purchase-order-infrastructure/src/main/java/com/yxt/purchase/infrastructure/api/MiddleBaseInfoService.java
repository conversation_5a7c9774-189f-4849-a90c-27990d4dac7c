package com.yxt.purchase.infrastructure.api;

import com.yxt.org.read.opensdk.org.dto.response.OrgInfoQueryOpenResDTO;
import com.yxt.purchase.infrastructure.api.entity.OperatorValueObject;
import com.yxt.purchase.infrastructure.api.entity.QueryUserAuthOrgResModel;
import java.util.List;

public interface MiddleBaseInfoService {

  /**
   *  获取用户信息
   * @param userId
   * @return
   */
  OperatorValueObject getOperatorInfo(String userId, String merCode);

  /**
   * 获取用户拥有的门店权限
   * @param userId
   * @return
   */
  List<QueryUserAuthOrgResModel> queryUserAuthOrg(String userId);

  /**
   * 获取组织信息
   * @param orgCodes
   * @param merCode
   * @return
   */
  List<OrgInfoQueryOpenResDTO> getOrgInfo(List<String> orgCodes, String merCode);
}
