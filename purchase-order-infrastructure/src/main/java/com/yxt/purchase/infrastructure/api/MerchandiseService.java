package com.yxt.purchase.infrastructure.api;

import com.yxt.merchandise.search.common.model.dto.b2b.response.CommodityB2bStoreSpecBatchQueryResp;
import com.yxt.purchase.infrastructure.api.entity.CommodityB2bStoreSpecBatchQueryModel;
import com.yxt.purchase.infrastructure.api.entity.CommodityStockAndPriceQueryModel;
import java.util.List;

public interface MerchandiseService {
  /**
   * 批量查询门店商品
   * @param storeCode
   * @param erpCodes
   * @return
   */
  List<CommodityB2bStoreSpecBatchQueryResp> b2BCommodityBatchQuery(String storeCode, List<String> erpCodes);

  /**
   * 批量查询商品库存
   *
   * @param storeCode 门店编码
   * @param erpCodes 商品编码集合
   * @return List<CommodityStockAndPriceQueryResp>
   */
  List<CommodityStockAndPriceQueryModel> queryStockAndPrice(String storeCode, List<String> erpCodes);

  /**
   * 校验商品是否符合当前门店得经营范围
   * @param erpCodes 商品编码集合
   * @param storeCode 门店id
   * @return 返回不符合门店经营范围得商品集合
   */
  List<String> checkCommodityIsOutSide(List<String> erpCodes,String storeCode);

}
