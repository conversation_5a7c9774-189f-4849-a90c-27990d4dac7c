package com.yxt.purchase.infrastructure.repository;

import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.domain.entity.CreatePurchaseOrderDetailModel;
import com.yxt.purchase.domain.repository.PurchaseOrderRepository;
import com.yxt.purchase.infrastructure.converter.PurchaseOrderConverter;
import com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDO;
import com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDetailDO;
import com.yxt.purchase.infrastructure.mapper.PurchaseOrderMapper;
import com.yxt.purchase.infrastructure.mapper.impl.PurchaseOrderDetailMapperService;
import com.yxt.purchase.infrastructure.utils.RedisIncrementUtil;
import com.yxt.purchase.types.RedisKeyConstant;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * 采购单仓储实现类
 */
@Slf4j
@Repository
public class PurchaseOrderRepositoryImpl implements PurchaseOrderRepository {

  @Autowired
  private PurchaseOrderMapper purchaseOrderMapper;

  @Autowired
  private PurchaseOrderDetailMapperService purchaseOrderDetailMapperService;

  @Autowired
  private RedisIncrementUtil redisIncrementUtil;


  /**
   * 保存采购单主体信息
   *
   * @param purchaseOrderAggregate 采购单聚合根
   */
  @Transactional(rollbackFor = Exception.class)
  public void savePurchaseOrder(PurchaseOrderAggregate purchaseOrderAggregate) {
    if (purchaseOrderAggregate == null) {
      return;
    }
    PurchaseOrderDO purchaseOrderDO = PurchaseOrderConverter.toBOEntity(purchaseOrderAggregate);
    List<PurchaseOrderDetailDO> detailDOList = new ArrayList<>();
    // 保存采购单明细
    if (purchaseOrderAggregate.getPurchaseOrderDetailList() != null
        && !purchaseOrderAggregate.getPurchaseOrderDetailList().isEmpty()) {
      String purchaseOrderNo = purchaseOrderDO.getPurchaseOrderNo();
      log.info("开始保存采购单明细，采购单号：{}，明细数量：{}",
          purchaseOrderNo, purchaseOrderAggregate.getPurchaseOrderDetailList().size());
      for (CreatePurchaseOrderDetailModel detailModel : purchaseOrderAggregate.getPurchaseOrderDetailList()) {
        PurchaseOrderDetailDO detailBO = getPurchaseOrderDetailDO(
            detailModel, purchaseOrderNo, purchaseOrderDO);
        // 保存明细
        detailDOList.add(detailBO);
      }
    } else {
      log.warn("采购单没有明细信息，采购单号：{}", purchaseOrderDO.getPurchaseOrderNo());
    }
    // 保存采购单
    int count = purchaseOrderMapper.insert(purchaseOrderDO);
    if (count <= 0) {
      log.error("保存采购单失败，采购单号：{}", purchaseOrderDO.getPurchaseOrderNo());
      return;
    }
    // 保存明细
    purchaseOrderDetailMapperService.saveBatch(detailDOList);
  }

  @NotNull
  private static PurchaseOrderDetailDO getPurchaseOrderDetailDO(
      CreatePurchaseOrderDetailModel detailModel, String purchaseOrderNo,
      PurchaseOrderDO purchaseOrderDO) {
    PurchaseOrderDetailDO detailBO = new PurchaseOrderDetailDO();
    detailBO.setPurchaseOrderNo(purchaseOrderNo);
    detailBO.setErpCode(detailModel.getErpCode());
    detailBO.setErpName(detailModel.getErpName());
    detailBO.setCommoditySpec(detailModel.getCommoditySpec());
    detailBO.setCommodityCount(detailModel.getQty());
    detailBO.setStatus(detailModel.getStatus() != null ? detailModel.getStatus() : "0");
    detailBO.setExMsg(detailModel.getEx_msg());
    detailBO.setSysCreateTime(purchaseOrderDO.getCreatedTime());
    detailBO.setSysUpdateTime(purchaseOrderDO.getUpdatedTime());
    return detailBO;
  }


  /**
   * 获取采购单号 采购单编号规则为 PO + yyyyMMdd+ storeCode + 当日6位门店序列号
   *
   * @param storeCode 门店编码
   */
  @Override
  public Long getPurchaseOrderNo(String storeCode) {
    // 通过redis 记录当前门店当日生成采购代编号默认为1，每次生成采购单号+1
    String key = RedisKeyConstant.getStorePurchaseOrderNoIndexKey(storeCode);
    return redisIncrementUtil.getAndIncrement(key);
  }

  /**
   * 将采购单状态设置为失败
   *
   * @param purchaseOrderNo 采购单号
   * @param failReason 失败原因
   * @param updatedBy 更新人
   * @return 是否设置成功
   */
  @Override
  public void setPurchaseOrderStateToFail(String purchaseOrderNo, String failReason, String updatedBy) {

      // 设置采购单状态为取消，并设置提单状态为失败
      int rows = purchaseOrderMapper.setPurchaseOrderStateToFail(purchaseOrderNo, failReason, updatedBy);

  }
}
