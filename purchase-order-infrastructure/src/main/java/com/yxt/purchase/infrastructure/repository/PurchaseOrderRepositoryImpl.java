package com.yxt.purchase.infrastructure.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.permission.YxtOrderPermission;
import com.yxt.permission.YxtOrderPermission.ValidType;
import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.domain.command.BaseQueryCommand;
import com.yxt.purchase.domain.command.BatchReConfirmCommand;
import com.yxt.purchase.domain.command.PurchaseOrderQueryCommand;
import com.yxt.purchase.domain.entity.CreatePurchaseOrderDetailModel;
import com.yxt.purchase.domain.repository.PurchaseOrderRepository;
import com.yxt.purchase.domain.entity.PurchaseOrderQueryResult;
import com.yxt.purchase.enums.PurchaseOrderStatusEnum;
import com.yxt.purchase.enums.SettlementStatusEnum;
import com.yxt.purchase.infrastructure.api.MiddleBaseInfoService;
import com.yxt.purchase.infrastructure.api.entity.OperatorValueObject;
import com.yxt.purchase.infrastructure.converter.PurchaseOrderConverter;
import com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDO;
import com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDetailDO;
import com.yxt.purchase.infrastructure.feign.MiddleIdClient;
import com.yxt.purchase.infrastructure.mapper.PurchaseOrderMapper;
import com.yxt.purchase.infrastructure.mapper.impl.PurchaseOrderDetailMapperService;
import com.yxt.purchase.infrastructure.mapper.impl.PurchaseOrderMapperService;
import com.yxt.purchase.infrastructure.utils.RedisIncrementUtil;
import com.yxt.purchase.types.OperationUser;
import com.yxt.purchase.types.PurchaseOrderCommonTypes;
import com.yxt.purchase.types.PurchaseOrderNo;
import com.yxt.purchase.types.RedisKeyConstant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 采购单仓储实现类
 */
@Slf4j
@Repository
public class PurchaseOrderRepositoryImpl implements PurchaseOrderRepository {

  @Autowired
  private PurchaseOrderMapper purchaseOrderMapper;

  @Autowired
  private PurchaseOrderMapperService purchaseOrderMapperService;

  @Autowired
  private PurchaseOrderDetailMapperService purchaseOrderDetailMapperService;

  @Autowired
  private RedisIncrementUtil redisIncrementUtil;

  @Autowired
  private MiddleBaseInfoService middleBaseInfoService;

  @Autowired
  private MiddleIdClient middleIdClient;

  /**
   * 保存采购单主体信息
   *
   * @param purchaseOrderAggregate 采购单聚合根
   */
  public void savePurchaseOrder(PurchaseOrderAggregate purchaseOrderAggregate) {
    if (purchaseOrderAggregate == null) {
      return;
    }
    PurchaseOrderDO purchaseOrderDO = PurchaseOrderConverter.toBOEntity(purchaseOrderAggregate);
    List<PurchaseOrderDetailDO> detailDOList = new ArrayList<>();
    // 保存采购单明细
    if (purchaseOrderAggregate.getPurchaseOrderDetailList() != null
        && !purchaseOrderAggregate.getPurchaseOrderDetailList().isEmpty()) {
      String purchaseOrderNo = purchaseOrderDO.getPurchaseOrderNo();
      log.info("开始保存采购单明细，采购单号：{}，明细数量：{}",
          purchaseOrderNo, purchaseOrderAggregate.getPurchaseOrderDetailList().size());
      for (CreatePurchaseOrderDetailModel detailModel : purchaseOrderAggregate.getPurchaseOrderDetailList()) {
        PurchaseOrderDetailDO detailBO = getPurchaseOrderDetailDO(
            detailModel, purchaseOrderNo, purchaseOrderDO);
        // 保存明细
        detailDOList.add(detailBO);
      }
    } else {
      log.warn("采购单没有明细信息，采购单号：{}", purchaseOrderDO.getPurchaseOrderNo());
    }
    // 保存采购单
    int count = purchaseOrderMapper.insert(purchaseOrderDO);
    if (count <= 0) {
      log.error("保存采购单失败，采购单号：{}", purchaseOrderDO.getPurchaseOrderNo());
      return;
    }
    // 保存明细
    purchaseOrderDetailMapperService.saveBatch(detailDOList);
  }

  @NotNull
  private static PurchaseOrderDetailDO getPurchaseOrderDetailDO(
      CreatePurchaseOrderDetailModel detailModel, String purchaseOrderNo,
      PurchaseOrderDO purchaseOrderDO) {
    PurchaseOrderDetailDO detailBO = new PurchaseOrderDetailDO();
    detailBO.setPurchaseOrderNo(purchaseOrderNo);
    detailBO.setErpCode(detailModel.getErpCode());
    detailBO.setErpName(detailModel.getErpName());
    detailBO.setCommoditySpec(detailModel.getCommoditySpec());
    detailBO.setManufacture(detailModel.getManufacture());
    detailBO.setCommodityCount(detailModel.getQty());
    detailBO.setStatus(detailModel.getStatus() != null ? detailModel.getStatus() : "0");
    detailBO.setExMsg(detailModel.getEx_msg());
    detailBO.setSysCreateTime(purchaseOrderDO.getCreatedTime());
    detailBO.setSysUpdateTime(purchaseOrderDO.getUpdatedTime());
    return detailBO;
  }


  /**
   * 获取采购单号 采购单编号规则为 PO + yyyyMMdd+ storeCode + 当日6位门店序列号
   *
   * @param storeCode 门店编码
   */
  @Override
  public Long getPurchaseOrderNo(String storeCode) {
    // 通过redis 记录当前门店当日生成采购代编号默认为1，每次生成采购单号+1
    String key = RedisKeyConstant.getStorePurchaseOrderNoIndexKey(storeCode);
    return redisIncrementUtil.getAndIncrement(key);
  }

  /**
   * 将采购单状态设置为待确认并且会写原因
   *
   * @param purchaseOrderNo 采购单号
   * @param failReason      失败原因
   * @param updatedBy       更新人
   * @return 是否设置成功
   */
  @Override
  public void setPurchaseOrderState(String purchaseOrderNo, String failReason,
      String updatedBy) {

    // 设置采购单状态为取消，并设置提单状态为失败
    int rows = purchaseOrderMapper.setPurchaseOrderStatsToWaitConfirm(
        purchaseOrderNo, failReason,
        updatedBy);

  }

  /**
   * 查询采购单列表
   *
   * @param command 查询命令
   * @return 采购单聚合根列表和总数
   */
  @Override
  @YxtOrderPermission({
      @YxtOrderPermission.PermissionCheck(validType = ValidType.COMPANY_CODE, field = "#command.companyCodeList"),
      @YxtOrderPermission.PermissionCheck(validType = ValidType.STORE_CODE, field = "#command.storeCodeList")
  })
  public PurchaseOrderQueryResult queryPurchaseOrders(PurchaseOrderQueryCommand command) {
    // 构建查询条件
    LambdaQueryWrapper<PurchaseOrderDO> queryWrapper = new LambdaQueryWrapper<>();
    // 处理权限问题

    initQueryWrapperByPermission(queryWrapper, command);

    // 按创建时间倒序排序
    queryWrapper.orderByDesc(PurchaseOrderDO::getCreatedTime);

    // 执行分页查询
    Page<PurchaseOrderDO> page = new Page<>(command.getCurrentPage(), command.getPageSize());
    IPage<PurchaseOrderDO> purchaseOrderPage = purchaseOrderMapper.selectPage(page, queryWrapper);

    // 转换为聚合根列表
    List<PurchaseOrderAggregate> purchaseOrderAggregates = purchaseOrderPage.getRecords().stream()
        .map(this::convertToAggregate)
        .collect(Collectors.toList());

    // 构建查询结果
    PurchaseOrderQueryResult result = new PurchaseOrderQueryResult();
    result.setPurchaseOrderAggregates(purchaseOrderAggregates);
    result.setTotalCount(purchaseOrderPage.getTotal());
    result.setTotalPage(purchaseOrderPage.getPages());
    result.setCurrentPage(command.getCurrentPage());
    result.setPageSize(command.getPageSize());
    return result;
  }

  /**
   * 检查采购单是否存在
   *
   * @param purchaseOrderId 采购单号
   * @return 是否存在
   */
  @Override
  public boolean checkPurchaseOrderExists(String purchaseOrderId) {
    LambdaQueryWrapper<PurchaseOrderDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(PurchaseOrderDO::getId, purchaseOrderId);
    queryWrapper.eq(PurchaseOrderDO::getDeleted, 0); // 只查询未删除的记录
    return purchaseOrderMapperService.count(queryWrapper) > 0;
  }

  /**
   * 更新采购单自动扣款配置
   *
   * @param purchaseOrderId    采购单号
   * @param isAutoPayment      是否自动支付
   * @param overduePaymentTime 超时支付时间
   * @param updatedBy          更新人
   * @return 是否更新成功
   */
  @Override
  public boolean updateAutoDeductionConfig(String purchaseOrderId, Boolean isAutoPayment,
      Integer overduePaymentTime, String updatedBy) {
    // 查询采购单
    LambdaQueryWrapper<PurchaseOrderDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(PurchaseOrderDO::getId, purchaseOrderId);
    queryWrapper.eq(PurchaseOrderDO::getDeleted, 0); // 只查询未删除的记录
    queryWrapper.eq(PurchaseOrderDO::getState, PurchaseOrderStatusEnum.WAIT_CONFIRM.getCode()); // 只能修改待确认的采购单
    PurchaseOrderDO purchaseOrderDO = new PurchaseOrderDO();
    purchaseOrderDO.setId(Long.parseLong(purchaseOrderId));
    // 更新自动扣款配置
    purchaseOrderDO.setAutoPayment(isAutoPayment ? 1 : 0);
    purchaseOrderDO.setOverduePaymentTime(overduePaymentTime);
    purchaseOrderDO.setUpdatedBy(updatedBy);
    purchaseOrderDO.setUpdatedTime(LocalDateTime.now());

    // 保存更新
    return purchaseOrderMapperService.updateById(purchaseOrderDO);
  }

  /**
   * 根据ID列表批量驳回采购单
   *
   * @param purchaseOrderIds 采购单ID列表
   * @param rejectReason     驳回原因
   * @param updatedBy        更新人
   * @return 驳回成功的采购单号列表
   */
  @Override
  public List<String> batchRejectByIds(List<String> purchaseOrderIds, String rejectReason,
      String updatedBy) {
    if (CollUtil.isEmpty(purchaseOrderIds)) {
      return new ArrayList<>();
    }

    List<String> successList = new ArrayList<>();

    // 驳回采购单
    for (String purchaseOrderId : purchaseOrderIds) {
      try {
        // 查询采购单
        LambdaQueryWrapper<PurchaseOrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PurchaseOrderDO::getId, purchaseOrderId);
        queryWrapper.eq(PurchaseOrderDO::getDeleted, 0); // 只查询未删除的记录

        PurchaseOrderDO purchaseOrderDO = purchaseOrderMapperService.getOne(queryWrapper);
        // 只能处理取消待拒绝的采购单
        if (purchaseOrderDO == null || !purchaseOrderDO.getState().equals(PurchaseOrderStatusEnum.WAIT_CONFIRM.getCode())) {
          continue;
        }

        // 调用Mapper将采购单状态设置为失败
        int rows = purchaseOrderMapper.setPurchaseOrderStateToCancel(
            Collections.singletonList(purchaseOrderDO.getPurchaseOrderNo()),
            rejectReason,
            updatedBy);

        if (rows > 0) {
          successList.add(purchaseOrderDO.getPurchaseOrderNo());
        }
      } catch (Exception e) {
        log.error("驳回采购单失败，ID: {}", purchaseOrderId, e);
      }
    }

    return successList;
  }

  /**
   * 根据查询条件批量驳回采购单
   *
   * @param queryCommand 查询命令
   * @param rejectReason 驳回原因
   * @param updatedBy    更新人
   * @return 驳回成功的采购单号列表
   */
  @Override
  @YxtOrderPermission({
      @YxtOrderPermission.PermissionCheck(validType = ValidType.COMPANY_CODE, field = "#queryCommand.companyCodeList"),
      @YxtOrderPermission.PermissionCheck(validType = ValidType.STORE_CODE, field = "#queryCommand.storeCodeList")
  })
  public List<String> batchRejectByCondition(PurchaseOrderQueryCommand queryCommand,
      String rejectReason, String updatedBy) {
    // 构建查询条件
    LambdaQueryWrapper<PurchaseOrderDO> queryWrapper = new LambdaQueryWrapper<>();
    // 添加查询条件
    initQueryWrapperByPermission(queryWrapper, queryCommand);

    // 查询符合条件的采购单
    List<PurchaseOrderDO> purchaseOrderDOs = purchaseOrderMapperService.list(queryWrapper);
    if (CollUtil.isEmpty(purchaseOrderDOs)) {
      return new ArrayList<>();
    }

    // 驳回采购单
    List<String> purchaseOrderNos = purchaseOrderDOs.stream()
        .map(PurchaseOrderDO::getPurchaseOrderNo)
        .collect(Collectors.toList());

    try {
      // 调用Mapper将采购单状态设置为失败
      purchaseOrderMapper.setPurchaseOrderStateToCancel(
          purchaseOrderNos,
          rejectReason,
          updatedBy);

    } catch (Exception e) {
      log.error("驳回采购单失败，采购单号: {}", JSON.toJSONString(purchaseOrderNos), e);
    }

    return purchaseOrderNos;
  }



  private void initQueryWrapperByPermission(LambdaQueryWrapper<PurchaseOrderDO> queryWrapper,
      BaseQueryCommand queryCommand) {
    queryWrapper.eq(PurchaseOrderDO::getDeleted, 0); // 只查询未删除的记录
    // 如果创建人传了 则需要将创建人工号转为userid
    if (StrUtil.isNotBlank(queryCommand.getCreatedBy())) {
      OperatorValueObject operatorValueObject = middleBaseInfoService.getOperatorInfoByCode(
          queryCommand.getCreatedBy(),
          PurchaseOrderCommonTypes.MER_CODE);
      if (operatorValueObject != null) {
        queryCommand.setCreatedBy(operatorValueObject.getOperatorId());
      }
    }
    // 权限过滤
    if (CollUtil.isNotEmpty(queryCommand.getCompanyCodeList())) {
      // 如果传入了公司编码列表，则从权限范围内过滤出去
      if (StringUtils.isNotBlank(queryCommand.getCompanyCode())
          && !queryCommand.getCompanyCodeList().contains(queryCommand.getCompanyCode())) {
        queryWrapper.eq(PurchaseOrderDO::getId, 0);
        return;
      }
      queryWrapper.in(PurchaseOrderDO::getCompanyCode, queryCommand.getCompanyCodeList());
    } else if (CollUtil.isNotEmpty(queryCommand.getStoreCodeList())) {
      // 如果传入了 门店编码 则过滤
      if (StringUtils.isNotBlank(queryCommand.getOrganizationCode())
          && !queryCommand.getStoreCodeList().contains(queryCommand.getOrganizationCode())) {
        queryWrapper.eq(PurchaseOrderDO::getId, 0);
        return;
      }

      queryWrapper.in(PurchaseOrderDO::getOrganizationCode, queryCommand.getStoreCodeList());
    } else {
      // 没有权限不能查询数据
      queryWrapper.eq(PurchaseOrderDO::getId, 0);
      return;
    }

    // 添加查询条件
    if (StringUtils.isNotBlank(queryCommand.getCompanyCode())) {
      queryWrapper.eq(PurchaseOrderDO::getCompanyCode, queryCommand.getCompanyCode());
    }
    if (StringUtils.isNotBlank(queryCommand.getOrganizationCode())) {
      queryWrapper.eq(PurchaseOrderDO::getOrganizationCode, queryCommand.getOrganizationCode());
    }
    if (StringUtils.isNotBlank(queryCommand.getPurchaseOrderLabel())) {
      queryWrapper.eq(PurchaseOrderDO::getPurchaseOrderLabel, queryCommand.getPurchaseOrderLabel());
    }
    if (StringUtils.isNotBlank(queryCommand.getState())) {
      queryWrapper.eq(PurchaseOrderDO::getState, queryCommand.getState());
    }
    if (StringUtils.isNotBlank(queryCommand.getPurchaseOrderNo())) {
      queryWrapper.eq(PurchaseOrderDO::getPurchaseOrderNo, queryCommand.getPurchaseOrderNo());
    }
    if (StringUtils.isNotBlank(queryCommand.getCreatedBy())) {
      queryWrapper.eq(PurchaseOrderDO::getCreatedBy, queryCommand.getCreatedBy());
    }
    if (StringUtils.isNotBlank(queryCommand.getParentPurchaseOrderNo())) {
      queryWrapper.eq(PurchaseOrderDO::getParentPurchaseOrderNo,
          queryCommand.getParentPurchaseOrderNo());
    }
    if (StringUtils.isNotBlank(queryCommand.getRelatedOrderNo())) {
      queryWrapper.eq(PurchaseOrderDO::getRelatedOrderNo, queryCommand.getRelatedOrderNo());
    }
    if (StringUtils.isNotBlank(queryCommand.getOrderStartTime())) {
      queryWrapper.ge(PurchaseOrderDO::getCreatedTime, queryCommand.getOrderStartTime());
    }
    if (StringUtils.isNotBlank(queryCommand.getOrderEndTime())) {
      queryWrapper.le(PurchaseOrderDO::getCreatedTime, queryCommand.getOrderEndTime());
    }
    if (queryCommand.getIsRelatedOrder() != null) {
      if (queryCommand.getIsRelatedOrder()) {
        queryWrapper.isNotNull(PurchaseOrderDO::getRelatedOrderNo);
        queryWrapper.ne(PurchaseOrderDO::getRelatedOrderNo, "");
      } else {
        queryWrapper.isNull(PurchaseOrderDO::getRelatedOrderNo);
      }
    }
  }


  /**
   * 将数据对象转换为聚合根
   *
   * @param purchaseOrderDO 采购单数据对象
   * @return 采购单聚合根
   */
  private PurchaseOrderAggregate convertToAggregate(PurchaseOrderDO purchaseOrderDO) {
    if (purchaseOrderDO == null) {
      return null;
    }

    // 创建采购单聚合根
    PurchaseOrderAggregate aggregate = new PurchaseOrderAggregate();
    // 设置基本信息
    aggregate.setMerCode(purchaseOrderDO.getMerCode());
    // 设置采购单号
    PurchaseOrderNo purchaseOrderNo = PurchaseOrderNo.create(purchaseOrderDO.getPurchaseOrderNo());
    aggregate.setPurchaseOrderNo(purchaseOrderNo);
    // 设置其他属性
    // 这里需要根据实际情况进行转换，可能需要枚举类型转换等
    // 此处仅作示例，实际实现可能需要更复杂的转换逻辑
    if (purchaseOrderDO.getState() != null) {
      try {
        aggregate.setState(
            com.yxt.purchase.enums.PurchaseOrderStatusEnum.valueOf(purchaseOrderDO.getState()));
      } catch (IllegalArgumentException e) {
        log.warn("无法转换状态: {}", purchaseOrderDO.getState());
      }
    }

    if (purchaseOrderDO.getSettlementStatus() != null) {
      try {
        aggregate.setSettlementStatus(SettlementStatusEnum.getByCode(
            purchaseOrderDO.getSettlementStatus()));
      } catch (IllegalArgumentException e) {
        log.warn("无法转换结算状态: {}", purchaseOrderDO.getSettlementStatus());
      }
    }
    aggregate.setId(purchaseOrderDO.getId());
    aggregate.setMsg(purchaseOrderDO.getMsg());
    aggregate.setRelatedOrderNo(purchaseOrderDO.getRelatedOrderNo());
    aggregate.setParentPurchaseOrderNo(purchaseOrderDO.getParentPurchaseOrderNo());
    aggregate.setAutoPayment(purchaseOrderDO.getAutoPayment() == 1);
    aggregate.setOverduePaymentTime(purchaseOrderDO.getOverduePaymentTime());
    aggregate.setCompanyCode(purchaseOrderDO.getCompanyCode());
    aggregate.setOrganizationCode(purchaseOrderDO.getOrganizationCode());
    aggregate.setOrganizationName(purchaseOrderDO.getOrganizationName());

    if (purchaseOrderDO.getPurchaseOrderLabel() != null) {
      try {
        aggregate.setPurchaseOrderLabel(com.yxt.purchase.enums.PurchaseOrderLabelEnum.valueOf(
            purchaseOrderDO.getPurchaseOrderLabel()));
      } catch (IllegalArgumentException e) {
        log.warn("无法转换采购单标签: {}", purchaseOrderDO.getPurchaseOrderLabel());
      }
    }

    // 设置时间相关属性
    aggregate.setCreatedTime(purchaseOrderDO.getCreatedTime());
    aggregate.setModifiedTime(purchaseOrderDO.getUpdatedTime());
    aggregate.setConfirmTime(purchaseOrderDO.getConfirmTime());

    // 设置用户相关属性
    aggregate.setCreatedBy(new OperationUser(purchaseOrderDO.getCreatedBy(), "", ""));
    aggregate.setConfirmBy(new OperationUser(purchaseOrderDO.getConfirmBy(), "", ""));
    aggregate.setModifiedBy(new OperationUser(purchaseOrderDO.getUpdatedBy(), "", ""));

    return aggregate;
  }

  @Override
  public PurchaseOrderAggregate findByPurchaseOrderNo(String purchaseOrderNo) {
    // 查询采购单
    LambdaQueryWrapper<PurchaseOrderDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(PurchaseOrderDO::getPurchaseOrderNo, purchaseOrderNo);
    queryWrapper.eq(PurchaseOrderDO::getDeleted, 0); // 只查询未删除的记录

    PurchaseOrderDO purchaseOrderDO = purchaseOrderMapperService.getOne(queryWrapper);
    if (purchaseOrderDO == null) {
      return null;
    }

    // 转换为聚合根
    return convertToAggregate(purchaseOrderDO);
  }

  @Override
  public PurchaseOrderAggregate findByPurchaseOrderId(String purchaseOrderId) {
    // 查询采购单
    LambdaQueryWrapper<PurchaseOrderDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(PurchaseOrderDO::getId, purchaseOrderId);
    queryWrapper.eq(PurchaseOrderDO::getDeleted, 0); // 只查询未删除的记录

    PurchaseOrderDO purchaseOrderDO = purchaseOrderMapperService.getOne(queryWrapper);
    if (purchaseOrderDO == null) {
      return null;
    }

    // 转换为聚合根
    return convertToAggregate(purchaseOrderDO);
  }

  @Override
  public String copyPurchaseOrder(PurchaseOrderAggregate sourcePurchaseOrder, String userId
  ) {
    // 查询源采购单的明细信息
    LambdaQueryWrapper<PurchaseOrderDetailDO> detailQueryWrapper = new LambdaQueryWrapper<>();
    detailQueryWrapper.eq(PurchaseOrderDetailDO::getPurchaseOrderNo,
        sourcePurchaseOrder.getPurchaseOrderNo().getPurchaseOrderNo());
    List<PurchaseOrderDetailDO> sourceDetails = purchaseOrderDetailMapperService.list(
        detailQueryWrapper);

    // 创建新的采购单聚合根
    PurchaseOrderAggregate newPurchaseOrder = new PurchaseOrderAggregate();

    // 设置基本信息，从源采购单复制
    newPurchaseOrder.setMerCode(sourcePurchaseOrder.getMerCode());
    newPurchaseOrder.setCompanyCode(sourcePurchaseOrder.getCompanyCode());
    newPurchaseOrder.setCompanyName(sourcePurchaseOrder.getCompanyName());
    newPurchaseOrder.setOrganizationCode(sourcePurchaseOrder.getOrganizationCode());
    newPurchaseOrder.setOrganizationName(sourcePurchaseOrder.getOrganizationName());
    newPurchaseOrder.setPurchaseOrderLabel(sourcePurchaseOrder.getPurchaseOrderLabel());
    List<Long> parentPurchaseOrderNo = middleIdClient.getId(1);
    newPurchaseOrder.setParentPurchaseOrderNo(parentPurchaseOrderNo.get(0).toString());
    // 设置新的配置
    newPurchaseOrder.setAutoPayment(sourcePurchaseOrder.getAutoPayment());
    newPurchaseOrder.setOverduePaymentTime(sourcePurchaseOrder.getOverduePaymentTime());

    // 设置创建人信息
    newPurchaseOrder.setCreatedBy(new OperationUser(userId, null, null));
    newPurchaseOrder.setCreatedTime(LocalDateTime.now());
    newPurchaseOrder.setModifiedBy(new OperationUser(userId, null, null));
    newPurchaseOrder.setModifiedTime(LocalDateTime.now());
    newPurchaseOrder.setConfirmTime(null);
    newPurchaseOrder.setConfirmBy(null);
    // 获取新的采购单号
    Long purchaseOrderNoValue = getPurchaseOrderNo(sourcePurchaseOrder.getOrganizationCode());

    newPurchaseOrder.setPurchaseOrderNo(
        PurchaseOrderNo.create(purchaseOrderNoValue, sourcePurchaseOrder.getOrganizationCode()));

    // 设置初始状态
    newPurchaseOrder.setState(PurchaseOrderStatusEnum.WAIT_CONFIRM);

    // 保存新的采购单
    savePurchaseOrder(newPurchaseOrder);

    // 复制明细信息
    for (PurchaseOrderDetailDO sourceDetail : sourceDetails) {
      PurchaseOrderDetailDO newDetail = new PurchaseOrderDetailDO();
      newDetail.setPurchaseOrderNo(newPurchaseOrder.getPurchaseOrderNo().getPurchaseOrderNo());
      newDetail.setErpCode(sourceDetail.getErpCode());
      newDetail.setErpName(sourceDetail.getErpName());
      newDetail.setCommoditySpec(sourceDetail.getCommoditySpec());
      newDetail.setManufacture(sourceDetail.getManufacture());
      newDetail.setCommodityCount(sourceDetail.getCommodityCount());
      newDetail.setStatus(sourceDetail.getStatus()); // 正常状态
      newDetail.setSysCreateTime(LocalDateTime.now());
      newDetail.setSysUpdateTime(LocalDateTime.now());
      purchaseOrderDetailMapperService.save(newDetail);
    }
    // 返回新的采购单号
    return newPurchaseOrder.getPurchaseOrderNo().getPurchaseOrderNo();
  }

  @Override
  @YxtOrderPermission({
      @YxtOrderPermission.PermissionCheck(validType = ValidType.COMPANY_CODE, field = "#queryCommand.companyCodeList"),
      @YxtOrderPermission.PermissionCheck(validType = ValidType.STORE_CODE, field = "#queryCommand.storeCodeList")
  })
  public List<PurchaseOrderAggregate> getReConfirmByCondition(
      BatchReConfirmCommand queryCommand) {

    // 构建查询条件
    LambdaQueryWrapper<PurchaseOrderDO> queryWrapper = new LambdaQueryWrapper<>();
    // 添加查询条件
    if (CollUtil.isNotEmpty(queryCommand.getPurchaseOrderIds())) {
      queryWrapper.in(PurchaseOrderDO::getId, queryCommand.getPurchaseOrderIds());
    } else {
      initQueryWrapperByPermission(queryWrapper, queryCommand);
    }
    // 查询符合条件的采购单
    List<PurchaseOrderDO> purchaseOrderDOs = purchaseOrderMapperService.list(queryWrapper);
    if (CollUtil.isEmpty(purchaseOrderDOs)) {
      return new ArrayList<>();
    }
    return purchaseOrderDOs.stream().map(this::convertToAggregate).collect(Collectors.toList());
  }

  @Override
  public PurchaseOrderAggregate updatePurchaseOrderCompleted(PurchaseOrderAggregate purchaseOrderAggregate, String userId) {
    // 查询采购单
    LambdaQueryWrapper<PurchaseOrderDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(PurchaseOrderDO::getPurchaseOrderNo, purchaseOrderAggregate.getPurchaseOrderNo().getPurchaseOrderNo());
    queryWrapper.eq(PurchaseOrderDO::getDeleted, 0); // 只查询未删除的记录

    PurchaseOrderDO purchaseOrderDO = purchaseOrderMapperService.getOne(queryWrapper);
    if (purchaseOrderDO == null) {
      throw new IllegalArgumentException("采购单不存在");
    }

    // 更新采购单状态为已完成
    purchaseOrderDO.setState(PurchaseOrderStatusEnum.FINISHED.name());
    purchaseOrderDO.setCompletedBy(userId);
    purchaseOrderDO.setCompletedTime(LocalDateTime.now());
    purchaseOrderDO.setCompletedRemark(purchaseOrderAggregate.getCompletedRemark());
    purchaseOrderDO.setUpdatedBy(userId);
    purchaseOrderDO.setUpdatedTime(LocalDateTime.now());

    // 保存更新
    boolean updated = purchaseOrderMapperService.updateById(purchaseOrderDO);
    if (!updated) {
      throw new RuntimeException("更新采购单状态失败");
    }

    // 转换为聚合根并返回
    return convertToAggregate(purchaseOrderDO);
  }

}

