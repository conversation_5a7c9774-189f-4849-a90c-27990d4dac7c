package com.yxt.purchase.infrastructure.feign.entity;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class StoreMainDTO {

    @ApiModelProperty(value = "省份")
    private String province;
    @ApiModelProperty(value = "城市")
    private String city;
    @ApiModelProperty(value = "区域")
    private String area;
    @ApiModelProperty(value = "区域id")
    private Integer areaId;
    @ApiModelProperty(value = "详细地址")
    private String address;
    @ApiModelProperty(value = "联系电话")
    private String mobile;
    @ApiModelProperty(value = "经度")
    private String longitude;
    @ApiModelProperty(value = "纬度")
    private String latitude;
    @ApiModelProperty(value = "门店图片")
    private String stPath;
    @ApiModelProperty(value = "门店状态")
    private Integer stStatus;
    @ApiModelProperty(value = "营业开始时间")
    private Integer openStartTime;
    @ApiModelProperty(value = "营业结束时间")
    private Integer openEndTime;
    @ApiModelProperty(value = "开始送药时间")
    private Integer deliveryStartTime;
    @ApiModelProperty(value = "结束送药时间")
    private Integer deliveryEndTime;
    @ApiModelProperty(value = "是否支持自提0不支持，1支持")
    private Integer isself;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "是否有效0无效1有效")
    private Integer isvalid;
    @ApiModelProperty(value = "创建人")
    private String createName;
    @ApiModelProperty(value = "修改人")
    private String modifyName;
    @ApiModelProperty(value = "是否支持普通快递0不支持，1支持")
    private Integer isdelivery;
    @ApiModelProperty(value = "是否支持送药上门0不支持，1支持")
    private Integer isdistribution;
    @ApiModelProperty(value = "是否上线门店（0非上线门店，1上线门店）")
    private Integer onlineStatus;
    @ApiModelProperty(value = "o2o服务范围(0,设置距离;1,自定义范围,2：全国范围)")
    private Integer o2oServiceScopeType;
    @ApiModelProperty(value = "服务范围")
    private BigDecimal serviceScope;

    @ApiModelProperty(value = "是否有电商云线上门店 (0:没有电商云线上门店 1：有电商云线上门店)")
    private Integer onlineStoreStatus;
    @ApiModelProperty(value = "电商云线上库存同步比例")
    private BigDecimal syncRatio;

}
