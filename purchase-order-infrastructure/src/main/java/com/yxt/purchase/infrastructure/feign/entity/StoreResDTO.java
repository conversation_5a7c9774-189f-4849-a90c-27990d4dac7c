package com.yxt.purchase.infrastructure.feign.entity;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 员工查询返回DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/7/19 15:32
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StoreResDTO extends StoreMainDTO {

  @ApiModelProperty(value = "id")
  private String id;
  @ApiModelProperty(value = "商家编码")
  private String merCode;
  @ApiModelProperty(value = "门店编码")
  private String stCode;
  @ApiModelProperty(value = "门店名称")
  private String stName;
  @ApiModelProperty(value = "创建时间")
  private Date createTime;
  @ApiModelProperty(value = "末次修改时间")
  private Date modifyTime;
  private String distance;
  private List<CodeResDTO> codeList;
  @ApiModelProperty("营休状态 1：营业 0 打烊")
  private Integer openStatus;

  @ApiModelProperty(value = "同sys_organization表or_class一致)机构分类:1-集团；2-分子公司；3-仓库；4-数据中心；5-片区；6-加盟店；7-直营店")
  private Integer stClass;

  @ApiModelProperty(value = "pos跟oms交互的appid")
  private String romensAppId;
  @ApiModelProperty(value = "pos跟oms交互的appsecret")
  private String romensAppSecret;

  @ApiModelProperty(value = "省份编码")
  private String provinceId;

  @ApiModelProperty(value = "城市编码")
  private String cityId;

  /**
   * 获取城市代码
   *
   * @return 城市代码
   */
  public String getCityId() {
    if (null != this.getAreaId()) {
      return this.getAreaId().toString().substring(0, 4) + "00";
    }
    return null;
  }

  /**
   * 获取省份代码
   *
   * @return 省份代码
   */
  public String getProvinceId() {
    if (null != this.getAreaId()) {
      return this.getAreaId().toString().substring(0, 2) + "0000";
    }
    return null;
  }

  @Override
  public boolean equals(final Object obj) {
    if (obj == null) {
      return false;
    }
    final StoreResDTO o = (StoreResDTO) obj;
    if (this == o) {
      return true;
    } else {
      return (this.merCode.equals(o.merCode) && this.stCode.equals(o.stCode) && this.stClass.equals(
          o.stClass));
    }
  }

  @Override
  public int hashCode() {
    int result = 17;
    result = 31 * result + (merCode == null ? 0 : merCode.hashCode());
    result = 31 * result + (stCode == null ? 0 : stCode.hashCode());
    result = 31 * result + (stClass == null ? 0 : stClass.hashCode());
    return result;
  }

}