package com.yxt.purchase.infrastructure.log;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.biz_log.req.SaveBizLogReq;
import com.yxt.order.atom.sdk.biz_log.req.SearchBizLogBatchReq;
import com.yxt.order.atom.sdk.biz_log.req.SearchBizLogReq;
import com.yxt.order.atom.sdk.common.order_world.BizLogInfoDTO;
import com.yxt.order.common.Utils;
import com.yxt.order.common.order_world_dto.es.BizLogInfo;
import com.yxt.org.read.opensdk.emp.dto.request.EmployeeListQueryReqDTO;
import com.yxt.org.read.opensdk.emp.dto.response.EmployeeResDTO;
import com.yxt.org.read.opensdk.emp.service.EmpQueryOpenApi;
import com.yxt.purchase.domain.log.BizLogRepository;
import com.yxt.purchase.domain.log.command.BizLogBatchSearchCommand;
import com.yxt.purchase.domain.log.command.BizLogSearchCommand;
import com.yxt.purchase.infrastructure.converter.BizLogConvert;
import com.yxt.purchase.infrastructure.feign.BizLogAtomApiFeign;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

@Slf4j
@Repository
public class BizLogRepositoryImpl implements BizLogRepository {

  @Resource
  private BizLogAtomApiFeign bizLogAtomApiFeign;

  @Autowired
  private Environment env;

  @Resource
  private EmpQueryOpenApi empQueryOpenApi;

  @Override
  public void saveLog(List<BizLogInfo> bizLogList) {
    try{
      Set<String> operatorIdSet = bizLogList.stream()
          .map(BizLogInfo::getOperatorId)
          .filter(NumberUtil::isNumber).collect(Collectors.toSet());

      EmployeeListQueryReqDTO employeeQueryReq = new EmployeeListQueryReqDTO();
      employeeQueryReq.setMerCode("500001");
      employeeQueryReq.setUserIdList(new ArrayList<>(operatorIdSet));
      Map<String, String> employeeMap = new HashMap<>();
      try {
        ResponseBase<List<EmployeeResDTO>> response = empQueryOpenApi.listQueryEmployee(
            employeeQueryReq);
        List<EmployeeResDTO> employeeList = response.getData();
        if (CollUtil.isNotEmpty(employeeList)) {
          employeeMap = employeeList.stream()
              .collect(Collectors.toMap(EmployeeResDTO::getId, EmployeeResDTO::getEmpName));
        }
      } catch (Exception e) {
        log.error("查询用户信息失败！", e);
      }

      SaveBizLogReq req = new SaveBizLogReq();
      Map<String, String> finalEmployeeMap = employeeMap;
      List<BizLogInfoDTO> bizLogInfoDTOS = bizLogList.stream().map(bizLogInfo -> {
        BizLogInfoDTO bizLogInfoDTO = new BizLogInfoDTO();
        bizLogInfoDTO.setOperatorId(bizLogInfo.getOperatorId());
        bizLogInfoDTO.setOperatorName(bizLogInfo.getOperatorName());
        if (finalEmployeeMap.containsKey(bizLogInfo.getOperatorId())) {
          bizLogInfoDTO.setOperatorName(finalEmployeeMap.get(bizLogInfo.getOperatorId()));
        }
        bizLogInfoDTO.setOperateTime(bizLogInfo.getOperateTime());
        bizLogInfoDTO.setOperateService(env.getProperty("spring.application.name"));
        bizLogInfoDTO.setTraceId(bizLogInfo.getTraceId());
        bizLogInfoDTO.setBizNo(bizLogInfo.getBizNo());
        bizLogInfoDTO.setBizScene(bizLogInfo.getBizScene().bizScene());
        bizLogInfoDTO.setBizAction(bizLogInfo.getBizAction().bizAction());
        bizLogInfoDTO.setBizResult(bizLogInfo.getBizResult());
        bizLogInfoDTO.setBizResultDesc(bizLogInfo.getBizResultDesc());
        bizLogInfoDTO.setExtensionNum1(bizLogInfo.getExtensionNum1());
        bizLogInfoDTO.setExtensionNum2(bizLogInfo.getExtensionNum2());
        bizLogInfoDTO.setExtensionNum3(bizLogInfo.getExtensionNum3());
        bizLogInfoDTO.setExtJson(bizLogInfo.getExtJson());
        return bizLogInfoDTO;
      }).collect(Collectors.toList());
      req.setBizLogList(bizLogInfoDTOS);
      ResponseBase<Void> response = bizLogAtomApiFeign.saveLog(req);
      Utils.checkRespSuccess(response);
    }
    catch (Exception ignored){
    }

  }

  @Override
  public List<BizLogInfo> logSearch(BizLogSearchCommand command) {
    SearchBizLogReq req = new SearchBizLogReq();
    req.setBizNo(command.getBizNo());
    ResponseBase<List<BizLogInfoDTO>> response = bizLogAtomApiFeign.searchLog(req);
    Utils.checkRespSuccess(response);
    if (CollUtil.isEmpty(response.getData())) {
      return new ArrayList<>(0);
    }
    return response.getData().stream().map(BizLogConvert::convertBizLog)
        .collect(Collectors.toList());
  }


  @Override
  public List<BizLogInfo> logSearchBatch(BizLogBatchSearchCommand command) {
    SearchBizLogBatchReq req = new SearchBizLogBatchReq();
    req.setBizNoList(command.getBizNoList());
    req.setBizAction(command.getBizAction());
    ResponseBase<List<BizLogInfoDTO>> response = bizLogAtomApiFeign.searchLogBatch(req);
    Utils.checkRespSuccess(response);
    if (CollUtil.isEmpty(response.getData())) {
      return new ArrayList<>(0);
    }
    return response.getData().stream().map(BizLogConvert::convertBizLog)
        .collect(Collectors.toList());
  }
}
