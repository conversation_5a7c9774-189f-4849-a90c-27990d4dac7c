package com.yxt.purchase.infrastructure.handlers;

import com.yxt.middle.baseinfo.api.StoreInfoApi;
import com.yxt.purchase.domain.handler.CreatePurchaseOrderHandler;
import com.yxt.purchase.domain.repository.PurchaseOrderRepository;
import com.yxt.purchase.infrastructure.api.MerchandiseService;
import com.yxt.purchase.infrastructure.api.MiddleBaseInfoService;
import com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler;
import com.yxt.purchase.infrastructure.feign.MiddleIdClient;
import com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler;
import com.yxt.purchase.infrastructure.handlers.process.InitDataHandler;
import com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler;
import com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler;
import com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler;
import com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler;
import com.yxt.purchase.infrastructure.mapper.impl.PurchaseOrderMapperService;
import com.yxt.trade.api.OrderSettlementApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/**
 * 创建采购单 处理器责任链加载类 考虑到采购单创建后期后其他类型采购单需要扩展，这里使用责任链模式，当前处理流程较少看起来比较鸡肋
 */
@Service
public class CreatePurchaseOrderHandlerImpl implements CreatePurchaseOrderHandler {

  @Autowired
  private MerchandiseService merchandiseService;

  @Autowired
  private MiddleBaseInfoService middleBaseInfoService;

  @Autowired
  private MiddleIdClient middleIdClient;

  @Autowired
  private PurchaseOrderRepository purchaseOrderRepository;

  @Autowired
  private OrderSettlementApi orderSettlementApi;

  @Autowired
  private StoreInfoApi storeInfoApi;

  @Autowired
  private PurchaseOrderMapperService purchaseOrderMapperService;
  private final ApplicationEventPublisher applicationEventPublisher;
  /**
   * 初始化创建采购单处理器责任链
   *
   * @return
   */
  @Override
  public AbstractPurchaseOrderHandler<?> initHandler() {
    InitDataHandler initDataHandler = new InitDataHandler(middleIdClient,middleBaseInfoService,storeInfoApi);
    initDataHandler.setNextHandler(new UserPermissionCheckHandler(middleBaseInfoService)) // 检测用户权限
        .setNextHandler(new CommodityCheckHandler(merchandiseService))  // 检测商品信息
        .setNextHandler(new SavePurchaseOrderHandler(purchaseOrderRepository))// 提交采购单
        .setNextHandler(new SubmitPayOrderHandler(orderSettlementApi))// 提交交易单
        .setNextHandler(new UpdatePurchaseOrderStatusHandler(purchaseOrderMapperService));//更新采购单状态
    return initDataHandler;
  }
}
