package com.yxt.purchase.infrastructure.handlers.process;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.domain.entity.HandlerContext;
import com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler;
import com.yxt.purchase.enums.PurchaseOrderLabelEnum;
import com.yxt.purchase.enums.PurchaseOrderStatusEnum;
import com.yxt.purchase.enums.SettlementStatusEnum;
import com.yxt.purchase.infrastructure.event.SettlementFailEvent;
import com.yxt.purchase.utils.ResponseUtils;
import com.yxt.trade.api.OrderSettlementApi;
import com.yxt.trade.enums.SettlementType;
import com.yxt.trade.request.SettlementRequest;
import com.yxt.trade.request.SettlementRequest.Commodity;
import com.yxt.trade.request.SettlementRequest.ReceiverInfo;
import com.yxt.trade.request.SubmitOrderRequest;
import com.yxt.trade.response.SettlementInfoResponse;
import com.yxt.trade.response.SubmitOrderResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;

/**
 * 提交交易处理器
 */
@Slf4j
public class SubmitPayOrderHandler extends
    AbstractPurchaseOrderHandler<HandlerContext> {

  private final OrderSettlementApi orderSettlementApi;
  private final ApplicationEventPublisher applicationEventPublisher;

  public SubmitPayOrderHandler(OrderSettlementApi orderSettlementApi,
      ApplicationEventPublisher applicationEventPublisher) {
    this.orderSettlementApi = orderSettlementApi;
    this.applicationEventPublisher = applicationEventPublisher;
  }


  @Override
  public void doHandleReal(HandlerContext context) {
    for (PurchaseOrderAggregate aggregate : context.getAggregates()) {
      // 不需要自动提单 或者 办公用品暂时不提交订单
      if (BooleanUtil.isFalse(aggregate.getAutoConfirm()) || aggregate.getPurchaseOrderLabel()
          .equals(
              PurchaseOrderLabelEnum.CONSUMABLES)) {
        // 不需要自动提单 此步骤省略
        aggregate.setSettlementStatus(SettlementStatusEnum.WAIT_SETTLEMENT);
        aggregate.setState(PurchaseOrderStatusEnum.WAIT_CONFIRM);
        continue;
      }
      if (aggregate.getSettlementStatus() != null && aggregate.getSettlementStatus()
          .equals(SettlementStatusEnum.CHECK_FAILED)) {
        continue;
      }
      try {
        String settlementId = settlement(aggregate, context.getTradeMode(),
            context.getSystemCode());
        aggregate.setRelatedOrderNo(submitOrder(aggregate, settlementId, context.getTradeMode(),
            context.getSystemCode()));
        aggregate.setSettlementStatus(SettlementStatusEnum.SETTLEMENT);
        aggregate.setState(PurchaseOrderStatusEnum.CONFIRMED);
        aggregate.setMsg("订单提交成功");
      } catch (Exception ex) {
        log.error("提单失败，订单号{}", aggregate.getPurchaseOrderNo(), ex);
        aggregate.setSettlementStatus(SettlementStatusEnum.SETTLEMENT_FAILED);
        aggregate.setState(PurchaseOrderStatusEnum.WAIT_CONFIRM);
        aggregate.setMsg("提单失败," + ex.getMessage());
      }

    }
  }

  /**
   * 订单结算
   *
   * @param aggregate
   * @return
   */
  private String settlement(PurchaseOrderAggregate aggregate, String tradeMode, String systemCode) {

    // 提单
    SettlementRequest settlementRequest = new SettlementRequest();
    settlementRequest.setStoreCode(aggregate.getWarehouseCode());// 大仓编码
    settlementRequest.setSystemCode(systemCode);
    settlementRequest.setTradeMode(tradeMode);
    settlementRequest.setUserId(aggregate.getCreatedBy().getUserId());
    settlementRequest.setUserOrgCode(aggregate.getOrganizationCode());
    if (BooleanUtil.isTrue(aggregate.getAutoPayment())) {// 自动扣款
      settlementRequest.setSettlementType(SettlementType.AUTO_PAY);
    } else {
      settlementRequest.setSettlementType(SettlementType.NORMAL);
    }
    List<Commodity> commodityList = aggregate
        .getPurchaseOrderDetailList().stream().map(item -> {
          Commodity commodity = new Commodity();
          commodity.setErpCode(item.getErpCode());
          commodity.setCount(item.getQty().intValue());
          return commodity;
        }).collect(Collectors.toList());
    settlementRequest.setCommodity(commodityList);
    ReceiverInfo receiverInfo = new ReceiverInfo();
    receiverInfo.setReceiverName(aggregate.getAddress().getReceiveName());
    receiverInfo.setReceiverPhone(aggregate.getAddress().getReceiveMobile());
    receiverInfo.setAddress(aggregate.getAddress().getAddress());
    receiverInfo.setProvince(aggregate.getAddress().getProvince());
    receiverInfo.setCity(aggregate.getAddress().getCity());
    receiverInfo.setArea(aggregate.getAddress().getDistrict());
    settlementRequest.setReceiverInfo(receiverInfo);

    ResponseBase<SettlementInfoResponse> responseBase = orderSettlementApi.settlement(
        settlementRequest);
    log.info("调用交易结算接口,request:{},result:{}", JSON.toJSONString(settlementRequest),
        JSON.toJSONString(responseBase));
    checkSettlementResult(responseBase, aggregate);
    ResponseUtils.checkRespSuccess(responseBase);
    // 结算id 下单是要传入
    return responseBase.getData().getSettlementId();

  }

  /**
   * @param result
   * @param aggregate
   */
  private void checkSettlementResult(ResponseBase<SettlementInfoResponse> result,
      PurchaseOrderAggregate aggregate) {
    if (result.checkSuccess()) {
      return;
    }
    // 非商品异常
    if (result.getCode().equals("30002") && !result.getSubCode().equals("30003")) {
      aggregate.setSettlementStatus(SettlementStatusEnum.SETTLEMENT_FAILED);
      aggregate.setMsg(result.getMsg());
      return;
    }

    if (result.getCode().equals("30002") && result.getSubCode().equals("30003")) {
      SettlementFailEvent event = new SettlementFailEvent(applicationEventPublisher);
      event.setMsg(result.getSubMessage());
      event.setPurchaseOrderNo(aggregate.getPurchaseOrderNo().getPurchaseOrderNo());
      event.setUserId(aggregate.getModifiedBy().getUserId());
      applicationEventPublisher.publishEvent(event);
    }
  }


  /**
   * 下单
   *
   * @param aggregate
   * @param settlementId
   * @return
   */
  private String submitOrder(PurchaseOrderAggregate aggregate, String settlementId,
      String tradeMode, String systemCode) {
    SubmitOrderRequest submitOrderRequest = new SubmitOrderRequest();
    submitOrderRequest.setSystemCode(systemCode);
    submitOrderRequest.setTradeMode(tradeMode);
    submitOrderRequest.setMerchantCode(aggregate.getCompanyCode());
    submitOrderRequest.setUserId(aggregate.getCreatedBy().getUserId());
    submitOrderRequest.setUserOrgCode(aggregate.getOrganizationCode());
    submitOrderRequest.setSettlementId(settlementId);
    submitOrderRequest.setBusinessNo(aggregate.getPurchaseOrderNo().getPurchaseOrderNo());
    Map<String, Object> extraParams = new HashMap<>();
    extraParams.put("orderType", "purchaseOrder");
    extraParams.put("orderNo", aggregate.getPurchaseOrderNo());
    submitOrderRequest.setExtend(extraParams);
    // 自动扣款需要传入自动扣款的具体时间点
    if (aggregate.getAutoPayment() && aggregate.getOverduePaymentTime() != null
        && aggregate.getOverduePaymentTime() > 0) {
      submitOrderRequest.setPayDeductionTime(
          DateUtils.addDays(new Date(), aggregate.getOverduePaymentTime()));
    }
    ResponseBase<SubmitOrderResponse> responseBase = orderSettlementApi.submitOrder(
        submitOrderRequest);
    log.info("调用交易提单接口,request:{},result:{}", JSON.toJSONString(submitOrderRequest),
        JSON.toJSONString(responseBase));
    ResponseUtils.checkRespSuccess(responseBase);
    return responseBase.getData().getOrderNo();
  }

}
