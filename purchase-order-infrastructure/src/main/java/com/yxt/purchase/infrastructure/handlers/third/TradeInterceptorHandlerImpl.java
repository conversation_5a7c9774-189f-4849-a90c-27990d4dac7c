package com.yxt.purchase.infrastructure.handlers.third;

import com.yxt.trade.config.TradeInterceptorHandler;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class TradeInterceptorHandlerImpl implements TradeInterceptorHandler {

  @Value("${trade.systemCode:''}")
  private String systemCode;
  @Value("${trade.tradeMode:''}")
  private String tradeMode;

  @Override
  public String getSystemCode() {
    return systemCode;
  }

  @Override
  public String getUserId() {
    return null;
  }

  @Override
  public String getUserOrgCode() {
    return null;
  }

  @Override
  public String getTradeMode() {
    return tradeMode;
  }
}
