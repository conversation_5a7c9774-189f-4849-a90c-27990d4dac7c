package com.yxt.purchase.infrastructure.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 采购商品明细数据对象
 */
@Data
@TableName("purchase_order_detail")
public class PurchaseOrderDetailDO {

  /**
   * 明细ID
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 采购订单编号
   */
  private String purchaseOrderNo;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品名称
   */
  private String erpName;

  /**
   * 商品规格
   */
  private String commoditySpec;

  /**
   * 生产商
   */
  private String manufacture;

  /**
   * 商品数量
   */
  private BigDecimal commodityCount;

  /**
   * 状态：-1 异常 0 正常
   */
  private String status;

  /**
   * 错误信息，如果status为-1这个字段必定有值
   */
  private String exMsg;

  /**
   * 系统创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 系统更新时间
   */
  private LocalDateTime sysUpdateTime;
}