package com.yxt.purchase.infrastructure.handlers.process;

import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.domain.entity.HandlerContext;
import com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler;
import com.yxt.purchase.domain.repository.PurchaseOrderRepository;
import com.yxt.purchase.types.PurchaseOrderNo;
import lombok.extern.slf4j.Slf4j;


/**
 * 保存采购单信息处理器
 */
@Slf4j
public class SavePurchaseOrderHandler extends
    AbstractPurchaseOrderHandler<HandlerContext> {

  private final PurchaseOrderRepository purchaseOrderRepository;


  public SavePurchaseOrderHandler( PurchaseOrderRepository purchaseOrderRepository) {
    this.purchaseOrderRepository = purchaseOrderRepository;
  }

  @Override
  public void doHandleReal(HandlerContext context) {

    for (PurchaseOrderAggregate aggregate : context.getAggregates()) {
      // 生成新的采购单号
      Long serialNumber = purchaseOrderRepository.getPurchaseOrderNo(
          aggregate.getOrganizationCode());
      aggregate.setPurchaseOrderNo(
          PurchaseOrderNo.create(serialNumber, aggregate.getOrganizationCode()));
      // 使用商品分组服务按照商品类型和冷链属性进行分组，生成新的采购单聚合根
      // List<PurchaseOrderAggregate> groupedAggregates = groupByProductType(context);
      // 保存新的采购单聚合根
      purchaseOrderRepository.savePurchaseOrder(aggregate);

      log.info("采购单分组完成，共生成{}个采购单", context.getAggregates().size());
    }


  }


}
