package com.yxt.purchase.open.sdk.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Data;

@ApiModel(value = "创建采购单请求参数")
@Data
public class CreatePurchaseOrderRequest {

  /**
   * 是否自动支付
   */
  @ApiModelProperty(value = "是否超时自动支付")
  @NotNull(message = "isAutoPayment 参数必填")
  private Boolean isAutoPayment;

  /**
   * 超时支付时间
   */
  @ApiModelProperty(value = "overduePaymentTime 必填")
  @NotNull(message = "超时支付时间必填")
  private Integer overduePaymentTime;

  @ApiModelProperty(value = "是否自动过审，true: 表示生成采购单后立即生成订单 false: 表示只生成采购单")
  @NotNull(message = "autoConfirm 必填")
  private Boolean autoConfirm;

  @ApiModelProperty(value = "采购单明细")
  @NotNull(message = "采购商品不能为空")
  @Valid
  private List<PurchaseOrderItemBaseDto> items;

}
