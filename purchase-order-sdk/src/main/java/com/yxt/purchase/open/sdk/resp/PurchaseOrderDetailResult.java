package com.yxt.purchase.open.sdk.resp;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class PurchaseOrderDetailResult {

  /**
   * 明细ID
   */
  @ApiModelProperty(value = "明细ID", example = "1234")
  private Long id;

  /**
   * 采购订单编号
   */
  @ApiModelProperty(value = "采购订单编号", example = "PPO123456")
  private String purchaseOrderNo;

  /**
   * 商品编码
   */
  @ApiModelProperty(value = "商品编码", example = "ert3t3")
  private String erpCode;

  /**
   * 商品名称
   */
  @ApiModelProperty(value = "商品名称", example = "中药")
  private String erpName;

  /**
   * 商品规格
   */
  @ApiModelProperty(value = "商品规格", example = "20ml*10")
  private String commoditySpec;

  /**
   * 生产商
   */
  @ApiModelProperty(value = "生产商", example = "哈药六厂")
  private String manufacture;

  /**
   * 商品数量
   */
  @ApiModelProperty(value = "商品数量", example = "32")
  private BigDecimal commodityCount;

  /**
   * 状态：-1 异常 0 正常
   */
  @ApiModelProperty(value = "状态：-1 异常 0 正常", example = "0")
  private String status;

  /**
   * 错误信息，如果status为-1，这个字段必定有值
   */
  @ApiModelProperty(value = "错误信息，如果status为-1，这个字段必定有值", example = "异常")
  private String exMsg;

  /**
   * 系统创建时间
   */
  @ApiModelProperty(value = "系统创建时间", example = "2015-05-07")
  private LocalDateTime sysCreateTime;

  /**
   * 系统更新时间
   */
  @ApiModelProperty(value = "系统更新时间", example = "2015-05-07")
  private LocalDateTime sysUpdateTime;

}
