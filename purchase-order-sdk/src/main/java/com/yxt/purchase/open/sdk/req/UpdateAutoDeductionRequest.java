package com.yxt.purchase.open.sdk.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel(description = "修改采购单是否自动扣款请求参数")
public class UpdateAutoDeductionRequest {

    @ApiModelProperty(value = "采购单ID", required = true)
    @NotEmpty(message = "采购单ID不能为空")
    private String purchaseOrderId;

    @ApiModelProperty(value = "是否自动扣款", required = true)
    @NotNull(message = "扣款标识必填")
    private Boolean autoDeduction;
}