package com.yxt.purchase.open.sdk.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;


@ApiModel(value = "修改订单为取消")
@Data
public class UpdatePurchaseOrderCancelRequest {

  @ApiModelProperty(value = "采购单号")
  @NotNull
  private String purchaseOrderNo;

  @ApiModelProperty(value = "取消原因")
  private String cancelReason;


}
