package com.yxt.purchase.open.sdk.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BaseQueryRequest {
  @ApiModelProperty(value = "分公司编码")
  private String companyCode;
  @ApiModelProperty(value = "所属机构编码")
  private String organizationCode;
  @ApiModelProperty(value = "采购单标签 办公用品:CONSUMABLES 导入铺货: MULTIPLE_STORE")
  private String purchaseOrderLabel;
  @ApiModelProperty(value = "采购单状态 待确认:WAIT_CONFIRM 已确认:CONFIRMED 已完成:FINISHED 已取消:CANCELED")
  private String state;
  @ApiModelProperty(value = "采购单号")
  private String purchaseOrderNo;
  @ApiModelProperty(value = "父采购单号")
  private String parentPurchaseOrderNo;
  @ApiModelProperty(value = "关联订单号")
  private String relatedOrderNo;
  @ApiModelProperty(value = "创建人id")
  private String createdBy;
  @ApiModelProperty(value = "采购订单创建开始时间")
  private String orderStartTime;
  @ApiModelProperty(value = "采购订单创建结束时间")
  private String orderEndTime;
  @ApiModelProperty(value = "是否生成订单")
  private Boolean isRelatedOrder;

}
