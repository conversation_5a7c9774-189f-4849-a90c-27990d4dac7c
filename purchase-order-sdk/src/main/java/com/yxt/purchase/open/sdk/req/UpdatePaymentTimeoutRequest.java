package com.yxt.purchase.open.sdk.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

@Data
@ApiModel(description = "修改采购单超时支付天数请求参数")
public class UpdatePaymentTimeoutRequest {

    @ApiModelProperty(value = "采购单ID", required = true)
    @NotEmpty(message = "采购单ID不能为空")
    private String purchaseOrderId;

    @ApiModelProperty(value = "超期扣款天数", required = true)
    @NotEmpty(message = "超期扣款天数必填")
    @Min(value = 1, message = "超期扣款天数必须大于等于1")
    private Integer paymentTimeout;
}