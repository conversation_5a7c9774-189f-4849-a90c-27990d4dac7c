package com.yxt.purchase.open.sdk.resp;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class CreatePurchaseOrderResult {

  @ApiModelProperty(value = "创建成功的采购单id集合")
  private List<String> purchaseOrderIds;

  @ApiModelProperty(value = "新增操作是否成功")
  private Boolean isSuccess;

  @ApiModelProperty(value = "日志说明，如果isSuccess 为false则必定有错误说明")
  private String msg;

}
