package com.yxt.purchase.open.sdk.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UpdatePurchaseOrderDetailResult {
  @ApiModelProperty(value = "采购单no", example = "1234")
  private String purchaseOrderNo;
  @ApiModelProperty(value = "明细ID", example = "1234")
  private String purchaseOrderDetailId;
  /**
   * 状态：-1 异常 0 正常
   */
  @ApiModelProperty(value = "状态：-1 异常 0 正常", example = "0")
  private String status;

  /**
   * 错误信息，如果status为-1，这个字段必定有值
   */
  @ApiModelProperty(value = "错误信息，如果status为-1，这个字段必定有值", example = "异常")
  private String exMsg;

}
