package com.yxt.purchase.open.sdk.req;

import com.yxt.lang.dto.dao.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("采购单详情查询请求参数")
public class PurchaseOrderDetailQueryRequest extends PageRequest {

  /**
   * 状态：-1 异常 0 正常
   */
  @ApiModelProperty(value = "状态：-1 异常 0 正常", example = "0")
  private String status;

  @ApiModelProperty(value = "采购单号")
  @NotEmpty(message = "只允许查询指定采购单的商品信息")
  private String purchaseOrderNo;

  @ApiModelProperty(value = "商品名称")
  private String erpName;

  @ApiModelProperty(value = "商品编码")
  private String erpCode;
}

