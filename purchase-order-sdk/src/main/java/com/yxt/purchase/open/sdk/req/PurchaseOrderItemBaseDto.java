package com.yxt.purchase.open.sdk.req;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class PurchaseOrderItemBaseDto {
  /**
   * 组织编码
   */
  @ApiModelProperty(value = "*门店编码（必填）")
  @NotNull(message = "门店编码必填")
  private String organizationCode;
  /**
   * erp编码
   */
  @ApiModelProperty(value = "*商品编码（必填）")
  @NotNull(message = "商品编码必填")
  private String erpCode;

  /**
   * 数量
   */
  @ApiModelProperty(value = "*采购数量（必填），且数量必须大于等于1")
  @NotNull(message = "采购数量必填")
  @Min(value = 1, message = "采购数量必须大于等于1")
  private BigDecimal qty;
}
