package com.yxt.purchase.open.sdk.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

@ApiModel(value = "删除采购单商品api")
@Data
public class RemovePurchaseDetailByIdRequest {
  @ApiModelProperty(value = "采购单no", example = "1234")
  @NotEmpty(message = "采购单no不能为空")
  private String purchaseOrderNo;
  @ApiModelProperty(value = "明细ID", example = "1234")
  @NotEmpty(message = "明细ID不能为空")
  private String purchaseOrderDetailId;
}
