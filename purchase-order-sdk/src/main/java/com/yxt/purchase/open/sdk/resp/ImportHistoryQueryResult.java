package com.yxt.purchase.open.sdk.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(value = "导入历史列表返回值")
public class ImportHistoryQueryResult {
  @ApiModelProperty(value = "上传文件名称")
  private String fileName;
  @ApiModelProperty(value = "上传时间")
  private LocalDateTime operationTime;
  @ApiModelProperty(value = "上传人id")
  private String operationId;
  @ApiModelProperty(value = "上传人姓名")
  private String operationName;
  @ApiModelProperty(value = "处理状态 0:处理中 -1：处理部分异常  1：全部处理成功")
  private Integer status;
  @ApiModelProperty(value = "异常信息返回文件地址，status 为-1时比返回")
  private String errorFilePath;

  @ApiModelProperty(value = "成功上传的采购订单号")
  private List<String> purchaseOrders;
}
