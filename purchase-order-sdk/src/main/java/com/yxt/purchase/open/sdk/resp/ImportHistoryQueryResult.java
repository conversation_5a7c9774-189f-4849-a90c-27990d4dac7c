package com.yxt.purchase.open.sdk.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Data;

@Data
@ApiModel(value = "导入历史列表返回值")
public class ImportHistoryQueryResult {
  @ApiModelProperty(value = "上传文件名称")
  private String fileName;
  @ApiModelProperty(value = "上传时间")
  private LocalDateTime operationTime;
  @ApiModelProperty(value = "上传人id")
  private String operationId;
  @ApiModelProperty(value = "上传人姓名")
  private String operationName;
  @ApiModelProperty(value = "处理状态 true:成功 false：处理部分异常 ")
  private boolean status;
  @ApiModelProperty(value = "异常信息返回文件地址，status 为false时比返回")
  private String errorFilePath;

  
}
