package com.yxt.purchase.open.sdk.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Data;

@ApiModel(value = "更新采购单商品api")
@Data
public class UpdatePurchaseOrderDetailRequest {
  @ApiModelProperty(value = "采购单no", example = "1234")
  @NotNull(message = "采购单no不能为空")
  private String purchaseOrderNo;
  @ApiModelProperty(value = "明细ID", example = "1234")
  @NotNull(message = "明细ID不能为空")
  private String purchaseOrderDetailId;
  @ApiModelProperty(value = "商品数量", example = "1234")
  @NotNull(message = "商品数量不能为空")
  @Min(value = 1 , message = "商品数量必须大于等于1")
  private BigDecimal commodityCount;
}
