package com.yxt.purchase.open.sdk.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BizLogDto {

    @ApiModelProperty(value = "操作ID", example = "OP12345")
    private String operationId;

    @ApiModelProperty(value = "操作类型", example = "CREATE")
    private String operationType;

    @ApiModelProperty(value = "操作描述", example = "创建采购单")
    private String operationDesc;

    @ApiModelProperty(value = "操作时间", example = "2023-05-12T10:30:00Z")
    private String operationTime;

    @ApiModelProperty(value = "操作人ID", example = "USER001")
    private String operatorId;

    @ApiModelProperty(value = "操作人姓名", example = "张三")
    private String operatorName;

    @ApiModelProperty(value = "操作人角色", example = "采购员")
    private String operatorRole;

    @ApiModelProperty(value = "操作详情", example = "创建了采购单PO2305120001")
    private String details;
}
