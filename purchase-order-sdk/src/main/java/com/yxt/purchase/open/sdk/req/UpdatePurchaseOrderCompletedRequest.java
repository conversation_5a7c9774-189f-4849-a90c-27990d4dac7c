package com.yxt.purchase.open.sdk.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;


@ApiModel(value = "修改订单为完成")
@Data
public class UpdatePurchaseOrderCompletedRequest {

  @ApiModelProperty(value = "采购单号")
  @NotNull
  private String purchaseOrderNo;

  @ApiModelProperty(value = "完成备注")
  private String completedRemark;
}
