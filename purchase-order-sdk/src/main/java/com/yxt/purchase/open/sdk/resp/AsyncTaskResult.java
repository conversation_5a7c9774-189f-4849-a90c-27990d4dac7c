package com.yxt.purchase.open.sdk.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "异步任务结果")
public class AsyncTaskResult {
    
    @ApiModelProperty(value = "任务ID")
    private String taskId;
    
    @ApiModelProperty(value = "任务状态")
    private String status;
    
    @ApiModelProperty(value = "任务进度（0-100）")
    private Integer progress;
    
    @ApiModelProperty(value = "任务结果")
    private String result;
    
    @ApiModelProperty(value = "错误信息")
    private String errorMessage;
    
    @ApiModelProperty(value = "创建时间")
    private String createdTime;
    
    @ApiModelProperty(value = "完成时间")
    private String completedTime;
}
