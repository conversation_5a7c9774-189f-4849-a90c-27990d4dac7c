package com.yxt.purchase.open.sdk.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "驳回采购单api")
@Data
public class BatchRejectPurchaseOrderRequest extends BaseQueryRequest {
    @ApiModelProperty(value = "采购单id")
    private List<String> purchaseOrderIds;
}
