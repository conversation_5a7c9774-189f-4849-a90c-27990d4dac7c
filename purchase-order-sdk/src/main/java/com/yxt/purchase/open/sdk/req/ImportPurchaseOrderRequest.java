package com.yxt.purchase.open.sdk.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 *  导入采购订单请求参数
 */
@Data
@ApiModel(value = "导入采购单api")
public class ImportPurchaseOrderRequest {

  /**
   * 是否自动支付
   */
  @ApiModelProperty(value = "是否超时自动支付")
  @NotNull(message = "isAutoPayment 必填")
  private Boolean isAutoPayment;

  /**
   * 超时支付时间
   */
  @ApiModelProperty(value = "超时支付时间")
  @NotNull(message = "overduePaymentTime 必填")
  private Integer overduePaymentTime;

  @ApiModelProperty(value = "是否自动过审，true: 表示生成采购单后立即生成订单 false: 表示只生成采购单")
  @NotNull(message = "autoConfirm 必填")
  private Boolean autoConfirm;
}
