package com.yxt.purchase.open.sdk.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PurchaseOrderResult {
  @ApiModelProperty(value = "采购订单ID", example = "12345")
  private Long id;

  @ApiModelProperty(value = "采购订单号", example = "PO123456")
  private String purchaseOrderNo;

  @ApiModelProperty(value = "订单状态  待确认:WAIT_CONFIRM 已确认:CONFIRMED 已完成:FINISHED 已取消:CANCELED", example = "CANCELED")
  private String state;

  @ApiModelProperty(value = "关联订单号", example = "RO123456")
  private String relatedOrderNo;

  @ApiModelProperty(value = "父采购订单号", example = "PPO123456")
  private String parentPurchaseOrderNo;

  @ApiModelProperty(value = "是否自动支付", example = "1")
  private Boolean autoPayment;

  @ApiModelProperty(value = "逾期支付时间", example = "30")
  private Integer overduePaymentTime;

  @ApiModelProperty(value = "公司代码", example = "COMP001")
  private String companyCode;

  @ApiModelProperty(value = "公司名称", example = "Example Company")
  private String companyName;

  @ApiModelProperty(value = "组织代码", example = "ORG001")
  private String organizationCode;

  @ApiModelProperty(value = "组织名称", example = "Example Organization")
  private String organizationName;

  @ApiModelProperty(value = "采购订单标签 采购单标签 办公用品:CONSUMABLES 导入铺货: MULTIPLE_STORE", example = "MULTIPLE_STORE")
  private String purchaseOrderLabel;

  @ApiModelProperty(value = "确认人", example = "John Doe")
  private String confirmBy;

  @ApiModelProperty(value = "确认人用户ID", example = "UID12345")
  private String confirmUserId;


  @ApiModelProperty(value = "确认时间", example = "2023-10-01T12:00:00Z")
  private String confirmTime;

  @ApiModelProperty(value = "创建人", example = "Jane Doe")
  private String createdBy;

  @ApiModelProperty(value = "创建人用户ID", example = "UID67890")
  private String createdUserId;


  @ApiModelProperty(value = "更新人", example = "Alice Smith")
  private String updatedBy;

  @ApiModelProperty(value = "创建时间", example = "2023-10-01T10:00:00Z")
  private String createdTime;

  @ApiModelProperty(value = "更新时间", example = "2023-10-01T12:00:00Z")
  private String updatedTime;
  @ApiModelProperty(value = "更新人ID", example = "UID67890")
  private String updatedUserId;

  @ApiModelProperty(value = "备注", example = "This is a test order.")
  private String remark;

}
