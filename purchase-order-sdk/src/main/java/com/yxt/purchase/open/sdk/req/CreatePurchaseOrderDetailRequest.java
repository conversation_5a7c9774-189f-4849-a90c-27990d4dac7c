package com.yxt.purchase.open.sdk.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 创建采购商品明细请求
 */
@Data
@ApiModel(description = "创建采购商品明细请求")
public class CreatePurchaseOrderDetailRequest {

  @ApiModelProperty(value = "采购订单编号", required = true, example = "PO202305120001")
  private String purchaseOrderNo;

  @ApiModelProperty(value = "商品编码", required = true, example = "SKU001")
  private String erpCode;

  @ApiModelProperty(value = "商品名称", required = true, example = "测试商品")
  private String erpName;

  @ApiModelProperty(value = "商品规格", example = "规格型号")
  private String commoditySpec;

  @ApiModelProperty(value = "生产商", example = "生产厂家")
  private String manufacture;

  @ApiModelProperty(value = "商品数量", required = true, example = "10.5")
  private BigDecimal commodityCount;
}