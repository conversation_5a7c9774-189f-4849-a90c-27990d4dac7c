package com.yxt.purchase.open.sdk;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.purchase.open.sdk.req.BatchConfirmRequest;
import com.yxt.purchase.open.sdk.req.BatchRejectPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.CopyPurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.CreatePurchaseOrderRequest;
import com.yxt.purchase.open.sdk.req.PurchaseOrderDetailQueryRequest;
import com.yxt.purchase.open.sdk.req.PurchaseOrderInfoRequest;
import com.yxt.purchase.open.sdk.req.PurchaseOrderListQueryRequest;
import com.yxt.purchase.open.sdk.req.RemovePurchaseDetailByIdRequest;
import com.yxt.purchase.open.sdk.req.UpdateAutoDeductionConfigRequest;
import com.yxt.purchase.open.sdk.req.UpdatePurchaseOrderCancelRequest;
import com.yxt.purchase.open.sdk.req.UpdatePurchaseOrderCompletedRequest;
import com.yxt.purchase.open.sdk.req.UpdatePurchaseOrderDetailRequest;
import com.yxt.purchase.open.sdk.resp.CreatePurchaseOrderResult;
import com.yxt.purchase.open.sdk.resp.ImportHistoryQueryResult;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderDetailResult;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderInfoResult;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import javax.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;


@FeignClient(
    value = "purchase-order-center",
    contextId = "purchaseOrderApi"
)
@Api(tags = "采购中台 api")
public interface PurchaseOrderApi {

  String ENDPOINT = "/1.0";

  @ApiOperation(value = "采购订单导入api")
  @PostMapping(ENDPOINT + "/purchase-order/import")
  ResponseBase<CreatePurchaseOrderResult> importPurchaseOrder(@RequestHeader(value = "userId") String userId,@RequestParam("file") MultipartFile file,  @RequestParam Boolean isAutoPayment,
      @RequestParam Integer overduePaymentTime,@RequestParam Boolean autoConfirm);

  @ApiOperation(value = "采购订单新增api")
  @PostMapping(ENDPOINT + "/purchase-order/create")
  ResponseBase<CreatePurchaseOrderResult> createPurchaseOrder(@RequestHeader(value = "userId") String userId,@RequestBody @Valid CreatePurchaseOrderRequest request);

  @ApiOperation(value = "采购订单查询")
  @PostMapping(ENDPOINT + "/purchase-order/search")
  ResponseBase<PageDTO<PurchaseOrderResult>> queryPurchaseOrder(@RequestHeader(value = "userId") String userId, @RequestBody  @Valid PurchaseOrderListQueryRequest request);

  @ApiOperation(value = "修改采购单自动扣款设置")
  @PostMapping(ENDPOINT + "/purchase-order/update-auto-deduction")
  ResponseBase<Boolean> updateAutoDeductionConfig(@RequestHeader(value = "userId") String userId,@RequestBody  @Valid UpdateAutoDeductionConfigRequest request);

  @ApiOperation(value = "采购订单商品信息查询")
  @PostMapping(ENDPOINT + "/purchase-order/detail/search")
  ResponseBase<PageDTO<PurchaseOrderDetailResult>> queryPurchaseOrderDetail(@RequestHeader(value = "userId") String userId, @RequestBody  @Valid PurchaseOrderDetailQueryRequest request);

  @ApiOperation(value = "修改采购单商品信息")
  @PostMapping(ENDPOINT + "/purchase-order/detail/update")
  ResponseBase<PurchaseOrderDetailResult> updatePurchaseOrderDetail(@RequestHeader(value = "userId") String userId, @RequestBody  @Valid UpdatePurchaseOrderDetailRequest request);

  @ApiOperation(value = "删除采购单商品信息")
  @PostMapping(ENDPOINT + "/purchase-order/detail/remove")
  ResponseBase<Boolean> removePurchaseOrderDetail(@RequestHeader(value = "userId") String userId, @RequestBody  @Valid RemovePurchaseDetailByIdRequest request);

  @ApiOperation(value = "采购订单商品信息复制")
  @PostMapping(ENDPOINT + "/purchase-order/copy")
  ResponseBase<String> copyPurchaseOrder(@RequestHeader(value = "userId") String userId, @RequestBody  @Valid CopyPurchaseOrderRequest request);

  @ApiOperation(value = "采购订单商批量确认")
  @PostMapping(ENDPOINT + "/purchase-order/batch-confirm")
  ResponseBase<List<String>> batchConfirmPurchaseOrder(@RequestHeader(value = "userId") String userId, @RequestBody  @Valid BatchConfirmRequest request);

  @ApiOperation(value = "采购订单批量驳回")
  @PostMapping(ENDPOINT + "/purchase-order/batch-reject")
  ResponseBase<List<String>> batchRejectPurchaseOrder(@RequestHeader(value = "userId") String userId, @RequestBody  @Valid BatchRejectPurchaseOrderRequest request);

  @ApiOperation(value = "采购单详情查询")
  @PostMapping(ENDPOINT + "/purchase-order/info/search")
  ResponseBase<PurchaseOrderInfoResult> purchaseOrderInfoQuery(@RequestHeader(value = "userId") String userId, @RequestBody  @Valid PurchaseOrderInfoRequest request);


  @ApiOperation(value = "修改订单状态为完成，主要用于下游系统通知反馈")
  @PostMapping(ENDPOINT + "/purchase-order/update/completed")
  ResponseBase<Boolean> updatePurchaseOrderCompleted(
      @RequestBody @Valid UpdatePurchaseOrderCompletedRequest request);

  @ApiOperation(value = "修改订单状态为取消，主要用于下游系统通知反馈")
  @PostMapping(ENDPOINT + "/purchase-order/update/cancel")
  ResponseBase<Boolean> updatePurchaseOrderCancel(
      @RequestBody @Valid UpdatePurchaseOrderCancelRequest request);

  @ApiOperation(value = "获取上传历史记录")
  @PostMapping(ENDPOINT + "/purchase-order/import/history")
  ResponseBase<List<ImportHistoryQueryResult>> importHistoryQuery(@RequestHeader(value = "userId") String userId);
}