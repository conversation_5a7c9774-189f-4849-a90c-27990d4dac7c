package com.yxt.purchase.open.sdk.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "批量确认采购单api")
@Data
public class BatchConfirmRequest extends BaseQueryRequest {
  @ApiModelProperty(value = "采购单id")
  private List<String> purchaseOrderIds;
}
