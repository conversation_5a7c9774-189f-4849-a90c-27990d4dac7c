<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.yxt</groupId>
    <artifactId>yxt-xframe</artifactId>
    <version>2.16.5</version>
    <relativePath/>
  </parent>

  <groupId>com.yxt.open.purchase</groupId>
  <artifactId>purchase-order-sdk</artifactId>
  <version>1.0-SNAPSHOT</version>
  <name>purchase-order-sdk</name>
  <packaging>jar</packaging>
  <description>采购中台对外接口sdk</description>
  <properties>
    <java.version>1.8</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <spring-boot.version>2.6.13</spring-boot.version>
  </properties>
  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>yxt-core-spring-boot-starter</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.yxt</groupId>
          <artifactId>yxt-sentinel-spring-boot-starter</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>yxt-sentinel-spring-boot-starter</artifactId>
      <version>1.0.0</version>
    </dependency>
  </dependencies>


</project>
