[2025-05-20 14:03:14.056] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 14:03:15.365] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 14:03:15.788] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 14:04:56.211] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 14:04:57.226] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 14:04:57.544] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 14:05:03.660] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 14:05:05.786] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 14:05:05.812] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 14:05:05.850] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 14:05:05.864] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 14:05:07.535] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:05:07.551] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:05:07.561] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:05:07.582] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:05:07.595] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:05:07.678] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 14:05:07.699] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 14:05:07.780] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 14:05:08.391] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 14:05:08.686] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 19.101 seconds (JVM running for 22.071)
[2025-05-20 14:05:08.686] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 14:05:08.686] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 14:05:08.722] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 14:05:08.920] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:05:08.924] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:05:08.927] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:05:08.931] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:05:08.933] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:15:57.317] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 14:15:58.328] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 14:15:58.627] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 14:16:04.805] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 14:16:06.786] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 14:16:06.814] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 14:16:06.850] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 14:16:06.863] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 14:16:08.543] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:16:08.559] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:16:08.568] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:16:08.592] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:16:08.608] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:16:08.704] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 14:16:08.728] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 14:16:08.817] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 14:16:09.393] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 14:16:09.670] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 19.016 seconds (JVM running for 21.803)
[2025-05-20 14:16:09.670] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 14:16:09.670] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 14:16:09.700] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 14:16:10.355] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:16:10.359] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:16:10.363] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:16:10.365] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:16:10.373] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:18:49.687] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 14:18:51.471] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 41][com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder:137] 导入采购单失败 com.yxt.lang.exception.YxtBizException: JG0104门店不存在
	at com.yxt.lang.exception.YxtBizException$YxtBizExceptionBuilder.build(YxtBizException.java:29) ~[yxt-common-lang-4.10.3.jar:4.10.3]
	at com.yxt.order.common.ExceptionUtil.getWarnException(ExceptionUtil.java:23) ~[order-common-1.24.0-RELEASE.jar:1.24.0-RELEASE]
	at com.yxt.purchase.infrastructure.handlers.process.InitDataHandler.doHandleReal(InitDataHandler.java:54) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:26) ~[classes/:?]
	at com.yxt.purchase.domain.service.impl.PurchaseOrderDomainServiceImpl.createPurchaseOrder(PurchaseOrderDomainServiceImpl.java:47) ~[classes/:?]
	at com.yxt.purchase.application.service.impl.PurchaseOrderApplicationServiceImpl.importPurchaseOrder(PurchaseOrderApplicationServiceImpl.java:49) ~[classes/:?]
	at com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder(PurchaseOrderController.java:129) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at com.yxt.starter.filter.TraceIdFilter.doFilter(TraceIdFilter.java:60) ~[yxt-core-spring-boot-starter-4.6.4.jar:4.6.4]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at cn.hydee.starter.grey.springboot.web.GreyWebInterceptor.doFilter(GreyWebInterceptor.java:62) ~[grey-spring-boot-web-starter-2.0.0-20230815.020948-3.jar:2.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:836) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1747) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_202]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]

[2025-05-20 14:20:52.959] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.ctrip.framework.apollo.internals.RemoteConfigRepository.loadApolloConfig:194] Load config failed, will retry in 1 SECONDS. appId: purchase-order-center, cluster: default, namespaces: bizconfig
[2025-05-20 14:20:53.964] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26] Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: purchase-order-center, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/purchase-order-center/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: purchase-order-center, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
[2025-05-20 14:25:15.051] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 14:25:16.079] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 14:25:16.383] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 14:25:22.489] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 14:25:24.444] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 14:25:24.474] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 14:25:24.513] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 14:25:24.527] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 14:25:26.217] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:25:26.233] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:25:26.241] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:25:26.259] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:25:26.274] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:25:26.359] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 14:25:26.382] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 14:25:26.470] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 14:25:27.067] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 14:25:27.343] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 18.996 seconds (JVM running for 21.838)
[2025-05-20 14:25:27.343] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 14:25:27.343] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 14:25:27.376] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 14:25:28.125] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:25:28.127] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:25:28.130] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:25:28.132] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:25:28.134] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 14:28:16.482] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 14:28:45.199] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.zaxxer.hikari.pool.HikariPool.run:766] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=46s469ms940µs900ns).
[2025-05-20 14:28:45.219] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 14:30:27.762] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.zaxxer.hikari.pool.HikariPool.run:766] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m42s560ms144µs600ns).
[2025-05-20 14:30:27.762] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 14:30:27.791] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.RemoteConfigRepository.loadApolloConfig:194] Load config failed, will retry in 1 SECONDS. appId: purchase-order-center, cluster: default, namespaces: bizconfig
[2025-05-20 14:30:30.621] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26] Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: purchase-order-center, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/purchase-order-center/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: purchase-order-center, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
[2025-05-20 14:30:30.630] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 42][com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder:137] 导入采购单失败 java.lang.RuntimeException: JG0104门店不存在,
JM0003门店不存在,
JM0004门店不存在
	at com.yxt.purchase.domain.service.impl.PurchaseOrderDomainServiceImpl.createPurchaseOrder(PurchaseOrderDomainServiceImpl.java:60) ~[classes/:?]
	at com.yxt.purchase.application.service.impl.PurchaseOrderApplicationServiceImpl.importPurchaseOrder(PurchaseOrderApplicationServiceImpl.java:49) ~[classes/:?]
	at com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder(PurchaseOrderController.java:129) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at com.yxt.starter.filter.TraceIdFilter.doFilter(TraceIdFilter.java:60) ~[yxt-core-spring-boot-starter-4.6.4.jar:4.6.4]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at cn.hydee.starter.grey.springboot.web.GreyWebInterceptor.doFilter(GreyWebInterceptor.java:62) ~[grey-spring-boot-web-starter-2.0.0-20230815.020948-3.jar:2.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:836) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1747) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_202]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]

[2025-05-20 14:35:10.647] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.RemoteConfigRepository.loadApolloConfig:194] Load config failed, will retry in 1 SECONDS. appId: purchase-order-center, cluster: default, namespaces: bizconfig
[2025-05-20 14:35:11.661] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26] Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: purchase-order-center, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/purchase-order-center/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: purchase-order-center, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
[2025-05-20 14:40:10.637] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.RemoteConfigRepository.loadApolloConfig:194] Load config failed, will retry in 1 SECONDS. appId: purchase-order-center, cluster: default, namespaces: bizconfig
[2025-05-20 14:40:11.649] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26] Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: purchase-order-center, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/purchase-order-center/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: purchase-order-center, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
[2025-05-20 14:45:10.642] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.RemoteConfigRepository.loadApolloConfig:194] Load config failed, will retry in 1 SECONDS. appId: purchase-order-center, cluster: default, namespaces: bizconfig
[2025-05-20 14:45:11.656] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26] Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: purchase-order-center, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/purchase-order-center/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: purchase-order-center, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
[2025-05-20 14:50:10.648] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.RemoteConfigRepository.loadApolloConfig:194] Load config failed, will retry in 1 SECONDS. appId: purchase-order-center, cluster: default, namespaces: bizconfig
[2025-05-20 14:50:11.661] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26] Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: purchase-order-center, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/purchase-order-center/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: purchase-order-center, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
[2025-05-20 14:52:11.094] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 42][org.redisson.cluster.ClusterConnectionManager.operationComplete:378] Can't execute CLUSTER_NODES with **********/**********:9004 org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://**********:9004]
	at org.redisson.client.RedisConnection$2.run(RedisConnection.java:214) ~[redisson-3.9.1.jar:?]
	at io.netty.util.concurrent.PromiseTask$RunnableAdapter.call(PromiseTask.java:38) ~[netty-common-4.1.36.Final.jar:4.1.36.Final]
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:127) ~[netty-common-4.1.36.Final.jar:4.1.36.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:163) ~[netty-common-4.1.36.Final.jar:4.1.36.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.36.Final.jar:4.1.36.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:405) ~[netty-common-4.1.36.Final.jar:4.1.36.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500) ~[netty-transport-4.1.36.Final.jar:4.1.36.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:906) ~[netty-common-4.1.36.Final.jar:4.1.36.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.36.Final.jar:4.1.36.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.36.Final.jar:4.1.36.Final]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]

[2025-05-20 14:52:12.025] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][org.redisson.client.handler.CommandDecoder.completeResponse:449] response has been skipped due to timeout! channel: [id: 0xcecee040, L:/**********:55616 - R:**********/**********:9004], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=5774b505c8d6ca9899b5b17f852e038521457a14, address=redis://**********:9000, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=e4cd54c48d16e05f7b1c20aee4d68a7c3b27d473, address=redis://**********:9003, flags=[SLAVE], slaveOf=87e0c70ebb595aa836b5f20003e1c899d63c257f, slotRanges=[]], ClusterNodeInfo [nodeId=87e0c70ebb595aa836b5f20003e1c899d63c257f, address=redis://**********:9001, flags=[MASTER], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=51031f45d9f24602c95f55b5f2b1a1eb146de948, address=redis://**********:9002, flags=[SLAVE], slaveOf=3ff69a9c512383bbf372be70c02440c3c5d2d0c9, slotRanges=[]], ClusterNodeInfo [nodeId=89b58335b7ae59366ff4788f2897ce34a407f2bd, address=redis://**********:9005, flags=[SLAVE], slaveOf=5774b505c8d6ca9899b5b17f852e038521457a14, slotRanges=[]], ClusterNodeInfo [nodeId=3ff69a9c512383bbf372be70c02440c3c5d2d0c9, address=redis://**********:9004, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[10923-16383]]]]
[2025-05-20 14:55:10.637] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.RemoteConfigRepository.loadApolloConfig:194] Load config failed, will retry in 1 SECONDS. appId: purchase-order-center, cluster: default, namespaces: bizconfig
[2025-05-20 14:55:11.653] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26] Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: purchase-order-center, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/purchase-order-center/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: purchase-order-center, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
[2025-05-20 15:00:10.645] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.RemoteConfigRepository.loadApolloConfig:194] Load config failed, will retry in 1 SECONDS. appId: purchase-order-center, cluster: default, namespaces: bizconfig
[2025-05-20 15:00:11.661] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26] Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: purchase-order-center, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/purchase-order-center/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: purchase-order-center, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
[2025-05-20 15:05:10.638] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.RemoteConfigRepository.loadApolloConfig:194] Load config failed, will retry in 1 SECONDS. appId: purchase-order-center, cluster: default, namespaces: bizconfig
[2025-05-20 15:05:11.652] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26] Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: purchase-order-center, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/purchase-order-center/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: purchase-order-center, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
[2025-05-20 15:10:10.634] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.RemoteConfigRepository.loadApolloConfig:194] Load config failed, will retry in 1 SECONDS. appId: purchase-order-center, cluster: default, namespaces: bizconfig
[2025-05-20 15:10:11.639] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26] Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: purchase-order-center, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/purchase-order-center/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: purchase-order-center, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
[2025-05-20 15:11:22.363] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 15:11:23.467] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 15:11:23.780] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 15:11:29.889] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 15:11:31.548] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 15:11:31.573] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 15:11:31.608] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 15:11:31.619] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 15:11:33.193] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:11:33.212] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:11:33.222] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:11:33.244] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:11:33.260] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:11:33.343] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 15:11:33.366] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 15:11:33.450] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 15:11:34.045] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 15:11:34.372] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 18.756 seconds (JVM running for 21.695)
[2025-05-20 15:11:34.372] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 15:11:34.372] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 15:11:34.409] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 15:11:35.452] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:11:35.456] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:11:35.459] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:11:35.461] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:11:35.463] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:13:57.888] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 15:14:22.118] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.zaxxer.hikari.pool.HikariPool.run:766] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=46s48ms762µs200ns).
[2025-05-20 15:14:25.711] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 15:14:25.712] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 15:14:27.618] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 15:14:27.618] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 15:14:27.622] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["850774","142881","120200"],"storeCode":"JG0104"}
[2025-05-20 15:14:27.871] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[],"msg":"操作成功","timestamp":1747725268840}
[2025-05-20 15:14:27.871] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 15:14:27.871] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 15:14:27.883] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.utils.RedisIncrementUtil.getAndIncrement:52] Created new Redis key: purchase-order-index:20250520:JG0104, with expiration: 24 HOURS
[2025-05-20 15:14:43.050] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JG0104000001，明细数量：3
[2025-05-20 15:14:52.876] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler.doHandleReal:39] 采购单分组完成，共生成1个采购单
[2025-05-20 15:14:52.877] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 执行完成... 
[2025-05-20 15:14:52.877] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 开始执行...
[2025-05-20 15:17:05.547] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 15:17:06.592] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 15:17:07.111] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 15:17:13.285] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 15:17:15.288] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 15:17:15.313] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 15:17:15.351] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 15:17:15.362] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 15:17:16.944] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:17:16.958] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:17:16.971] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:17:16.988] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:17:17.004] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:17:17.090] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 15:17:17.112] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 15:17:17.182] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 15:17:17.752] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 15:17:18.026] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 19.157 seconds (JVM running for 21.902)
[2025-05-20 15:17:18.026] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 15:17:18.026] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 15:17:18.057] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 15:17:18.988] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:17:18.991] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:17:18.993] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:17:18.995] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:17:18.998] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:18:41.918] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 15:19:42.830] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.zaxxer.hikari.pool.HikariPool.run:766] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=52s296ms771µs).
[2025-05-20 15:19:42.853] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 15:19:42.854] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 15:20:22.871] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 15:20:23.989] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 15:20:24.328] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 15:20:30.535] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 15:20:32.509] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 15:20:32.535] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 15:20:32.570] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 15:20:32.580] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 15:20:34.142] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:20:34.156] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:20:34.166] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:20:34.184] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:20:34.199] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:20:34.295] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 15:20:34.315] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 15:20:34.379] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 15:20:34.910] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 15:20:35.169] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 19.037 seconds (JVM running for 22.066)
[2025-05-20 15:20:35.169] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 15:20:35.169] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 15:20:35.198] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 15:20:35.927] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:20:35.930] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:20:35.933] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:20:35.934] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:20:35.936] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:34:00.285] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 15:34:01.294] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 15:34:01.628] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 15:34:07.774] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 15:34:09.597] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 15:34:09.622] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 15:34:09.658] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 15:34:09.670] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 15:34:11.291] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:34:11.311] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:34:11.329] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:34:11.353] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:34:11.368] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:34:11.456] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 15:34:11.479] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 15:34:11.559] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 15:34:12.205] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 15:34:12.534] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 18.864 seconds (JVM running for 21.826)
[2025-05-20 15:34:12.535] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 15:34:12.535] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 15:34:12.576] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 15:34:13.076] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:34:13.091] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:34:13.103] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:34:13.109] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:34:13.114] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:35:44.904] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 15:36:06.828] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 15:36:06.828] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 15:36:07.364] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 15:36:07.364] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 15:36:07.426] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["850774","142881","120200"],"storeCode":"JG0104"}
[2025-05-20 15:36:07.563] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[],"msg":"操作成功","timestamp":1747726568601}
[2025-05-20 15:36:07.563] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 15:36:07.563] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 15:36:12.994] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JG0104000002，明细数量：3
[2025-05-20 15:36:13.406] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler.doHandleReal:39] 采购单分组完成，共生成1个采购单
[2025-05-20 15:36:13.406] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 执行完成... 
[2025-05-20 15:36:13.406] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 开始执行...
[2025-05-20 15:38:23.857] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.zaxxer.hikari.pool.HikariPool.run:766] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m8s801ms156µs200ns).
[2025-05-20 15:38:55.062] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 15:38:56.262] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 15:38:56.652] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 15:39:02.939] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 15:39:05.086] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 15:39:05.120] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 15:39:05.165] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 15:39:05.179] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 15:39:06.823] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:39:06.835] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:39:06.844] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:39:06.865] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:39:06.881] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:39:06.964] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 15:39:06.985] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 15:39:07.063] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 15:39:07.602] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 15:39:07.904] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 19.445 seconds (JVM running for 22.339)
[2025-05-20 15:39:07.904] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 15:39:07.904] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 15:39:07.934] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 15:39:08.196] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:39:08.199] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:39:08.201] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:39:08.203] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:39:08.205] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:39:16.511] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 15:39:21.854] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 15:39:21.854] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 15:39:22.423] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 15:39:22.423] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 15:39:22.487] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["850774","142881","120200"],"storeCode":"JG0104"}
[2025-05-20 15:39:22.566] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[],"msg":"操作成功","timestamp":1747726763610}
[2025-05-20 15:39:22.570] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 15:39:22.570] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 15:39:28.673] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JG0104000003，明细数量：3
[2025-05-20 15:39:29.071] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler.doHandleReal:39] 采购单分组完成，共生成1个采购单
[2025-05-20 15:39:29.072] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 执行完成... 
[2025-05-20 15:39:29.072] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 开始执行...
[2025-05-20 15:40:16.092] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.zaxxer.hikari.pool.HikariPool.run:766] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m7s156ms416µs).
[2025-05-20 15:48:01.837] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 15:48:02.863] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 15:48:03.205] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 15:48:09.428] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 15:48:11.101] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 15:48:11.128] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 15:48:11.163] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 15:48:11.175] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 15:48:12.867] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:48:12.888] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:48:12.907] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:48:12.927] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:48:12.945] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:48:13.030] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 15:48:13.053] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 15:48:13.128] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 15:48:13.711] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 15:48:13.984] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 18.877 seconds (JVM running for 22.008)
[2025-05-20 15:48:13.984] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 15:48:13.984] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 15:48:14.015] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 15:48:14.495] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:48:14.498] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:48:14.500] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:48:14.502] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:48:14.505] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:48:42.352] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 15:48:44.142] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 15:48:44.142] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 15:48:45.475] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 15:48:45.476] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 15:48:45.479] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["850774","142881","120200"],"storeCode":"JG0104"}
[2025-05-20 15:48:45.628] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[],"msg":"操作成功","timestamp":1747727326700}
[2025-05-20 15:48:45.628] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 15:48:45.628] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 15:48:45.687] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JG0104000004，明细数量：3
[2025-05-20 15:48:46.163] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler.doHandleReal:39] 采购单分组完成，共生成1个采购单
[2025-05-20 15:48:46.163] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 执行完成... 
[2025-05-20 15:48:46.163] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 开始执行...
[2025-05-20 15:49:19.997] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.settlement:107] 调用交易结算接口,request:{"commodity":[{"count":9,"erpCode":"850774"},{"count":10,"erpCode":"142881"},{"count":11,"erpCode":"120200"}],"receiverInfo":{"address":"山西省忻州市忻府区新建北路开莱国际社区E幢5单元1层010西号房","area":"忻府区","city":"忻州市","province":"山西省","receiverName":"忻州一心堂百姓药业开莱国际社区有限公司","receiverPhone":"13403508079"},"settlementType":"AUTO_PAY","storeCode":"D115","systemCode":"JOIN_B2B","tradeMode":"PURCHASE_ORDER","userId":"4089306587993216930","userOrgCode":"JG0104"},result:{"code":"50001","msg":"业务系统编码错误","timestamp":1747727344512}
[2025-05-20 15:49:22.952] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.doHandleReal:59] 提单失败，订单号PurchaseOrderNo(purchaseOrderNo=PO20250520JG0104000004) com.yxt.lang.exception.YxtBizException: 业务系统编码错误
	at com.yxt.lang.exception.YxtBizException$YxtBizExceptionBuilder.build(YxtBizException.java:29) ~[yxt-common-lang-4.10.3.jar:4.10.3]
	at com.yxt.purchase.utils.ResponseUtils.checkRespSuccess(ResponseUtils.java:9) ~[classes/:?]
	at com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.settlement(SubmitPayOrderHandler.java:109) ~[classes/:?]
	at com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.doHandleReal(SubmitPayOrderHandler.java:53) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:26) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.service.impl.PurchaseOrderDomainServiceImpl.createPurchaseOrder(PurchaseOrderDomainServiceImpl.java:56) ~[classes/:?]
	at com.yxt.purchase.application.service.impl.PurchaseOrderApplicationServiceImpl.importPurchaseOrder(PurchaseOrderApplicationServiceImpl.java:49) ~[classes/:?]
	at com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder(PurchaseOrderController.java:129) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at com.yxt.starter.filter.TraceIdFilter.doFilter(TraceIdFilter.java:60) ~[yxt-core-spring-boot-starter-4.6.4.jar:4.6.4]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at cn.hydee.starter.grey.springboot.web.GreyWebInterceptor.doFilter(GreyWebInterceptor.java:62) ~[grey-spring-boot-web-starter-2.0.0-20230815.020948-3.jar:2.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:836) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1747) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_202]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]

[2025-05-20 15:49:22.953] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 执行完成... 
[2025-05-20 15:49:22.953] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 开始执行...
[2025-05-20 15:49:23.094] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 执行完成... 
[2025-05-20 15:49:23.094] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 15:49:23.149] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 15:49:23.149] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 15:49:23.326] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 15:49:23.327] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 15:49:23.327] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["106632","847162","102443"],"storeCode":"JM0003"}
[2025-05-20 15:49:24.714] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"梓橦宫","drugType":0,"erpCode":"102443","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"昆明梓橦宫全新生物制药有限公司","name":"氨咖黄敏胶囊(速效伤风胶囊)_10粒_昆明梓橦宫全新生物","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10粒","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":88},{"brandName":"可孚","drugType":3,"erpCode":"106632","isColdChain":false,"isOfficial":false,"mainPic":"https://shop-cdn.yxtmart.cn/shop/101-100/106632/106632-2.jpg","manufacture":"可孚医疗科技股份有限公司000","name":"可孚医用棉签(灭菌)_10cm*50支","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10CM*50支","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":987},{"brandName":"楚天舒","drugType":3,"erpCode":"847162","isColdChain":false,"isOfficial":false,"manufacture":"湖北楚天舒药业有限公司","name":"楚天舒含糖金银花露_340ml","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"340ML","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":999958}],"msg":"操作成功","timestamp":1747727365793}
[2025-05-20 15:49:24.716] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["102443","106632","847162"],"storeCode":"JM0003"}
[2025-05-20 15:49:24.811] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"102443","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.700000,"storeCode":"JM0003","wareStock":88},{"erpCode":"106632","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.500000,"stock":0,"storeCode":"JM0003","wareStock":987},{"erpCode":"847162","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":3.500000,"storeCode":"JM0003","wareStock":999958}],"msg":"操作成功","timestamp":1747727365897}
[2025-05-20 15:49:24.818] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 15:49:24.818] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 15:49:24.825] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.utils.RedisIncrementUtil.getAndIncrement:52] Created new Redis key: purchase-order-index:20250520:JM0003, with expiration: 24 HOURS
[2025-05-20 15:49:24.827] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0003000001，明细数量：3
[2025-05-20 15:49:24.840] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler.doHandleReal:39] 采购单分组完成，共生成1个采购单
[2025-05-20 15:49:24.840] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 执行完成... 
[2025-05-20 15:49:24.840] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 开始执行...
[2025-05-20 15:51:06.519] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.zaxxer.hikari.pool.HikariPool.run:766] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m47s120ms784µs400ns).
[2025-05-20 15:51:06.520] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.doHandleReal:59] 提单失败，订单号PurchaseOrderNo(purchaseOrderNo=PO20250520JM0003000001) java.lang.NullPointerException: null
	at com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.settlement(SubmitPayOrderHandler.java:97) ~[classes/:?]
	at com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.doHandleReal(SubmitPayOrderHandler.java:53) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:26) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.service.impl.PurchaseOrderDomainServiceImpl.createPurchaseOrder(PurchaseOrderDomainServiceImpl.java:56) ~[classes/:?]
	at com.yxt.purchase.application.service.impl.PurchaseOrderApplicationServiceImpl.importPurchaseOrder(PurchaseOrderApplicationServiceImpl.java:49) ~[classes/:?]
	at com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder(PurchaseOrderController.java:129) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at com.yxt.starter.filter.TraceIdFilter.doFilter(TraceIdFilter.java:60) ~[yxt-core-spring-boot-starter-4.6.4.jar:4.6.4]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at cn.hydee.starter.grey.springboot.web.GreyWebInterceptor.doFilter(GreyWebInterceptor.java:62) ~[grey-spring-boot-web-starter-2.0.0-20230815.020948-3.jar:2.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:836) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1747) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_202]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]

[2025-05-20 15:51:06.520] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 执行完成... 
[2025-05-20 15:51:06.520] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 开始执行...
[2025-05-20 15:51:06.538] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 执行完成... 
[2025-05-20 15:51:06.538] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 15:51:06.606] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 15:51:06.606] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 15:51:18.409] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 15:51:19.477] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 15:51:19.821] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 15:51:26.026] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 15:51:27.700] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 15:51:27.730] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 15:51:27.774] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 15:51:27.786] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 15:51:29.404] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:51:29.419] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:51:29.429] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:51:29.447] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:51:29.465] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:51:29.553] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 15:51:29.574] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 15:51:29.645] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 15:51:30.207] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 15:51:30.506] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 18.789 seconds (JVM running for 21.682)
[2025-05-20 15:51:30.507] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 15:51:30.507] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 15:51:30.540] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 15:51:31.076] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:51:31.082] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:51:31.086] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:51:31.087] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:51:31.091] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 15:51:50.921] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 15:51:52.658] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 15:51:52.658] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 15:51:53.351] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 15:51:53.351] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 15:51:53.411] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["850774","142881","120200"],"storeCode":"JG0104"}
[2025-05-20 15:51:53.504] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[],"msg":"操作成功","timestamp":1747727514578}
[2025-05-20 15:51:53.504] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 15:51:53.507] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 15:51:53.566] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JG0104000005，明细数量：3
[2025-05-20 15:51:53.937] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler.doHandleReal:39] 采购单分组完成，共生成1个采购单
[2025-05-20 15:51:53.937] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 执行完成... 
[2025-05-20 15:51:53.937] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 开始执行...
[2025-05-20 15:52:32.349] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.settlement:107] 调用交易结算接口,request:{"commodity":[{"count":9,"erpCode":"850774"},{"count":10,"erpCode":"142881"},{"count":11,"erpCode":"120200"}],"receiverInfo":{"address":"山西省忻州市忻府区新建北路开莱国际社区E幢5单元1层010西号房","area":"忻府区","city":"忻州市","province":"山西省","receiverName":"忻州一心堂百姓药业开莱国际社区有限公司","receiverPhone":"13403508079"},"settlementType":"AUTO_PAY","storeCode":"D115","systemCode":"PURCHASE_ORDER","tradeMode":"JOIN_B2B","userId":"4089306587993216930","userOrgCode":"JG0104"},result:{"code":"30002","msg":"结算失败","subCode":"30003","subMessage":"{\"850774\":\"商品无法采购或不存在\",\"142881\":\"商品无法采购或不存在\",\"120200\":\"商品无法采购或不存在\"}","timestamp":1747727548135}
[2025-05-20 15:52:35.017] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.doHandleReal:59] 提单失败，订单号PurchaseOrderNo(purchaseOrderNo=PO20250520JG0104000005) com.yxt.lang.exception.YxtBizException: 结算失败
	at com.yxt.lang.exception.YxtBizException$YxtBizExceptionBuilder.build(YxtBizException.java:29) ~[yxt-common-lang-4.10.3.jar:4.10.3]
	at com.yxt.purchase.utils.ResponseUtils.checkRespSuccess(ResponseUtils.java:9) ~[classes/:?]
	at com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.settlement(SubmitPayOrderHandler.java:109) ~[classes/:?]
	at com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.doHandleReal(SubmitPayOrderHandler.java:53) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:26) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.service.impl.PurchaseOrderDomainServiceImpl.createPurchaseOrder(PurchaseOrderDomainServiceImpl.java:56) ~[classes/:?]
	at com.yxt.purchase.application.service.impl.PurchaseOrderApplicationServiceImpl.importPurchaseOrder(PurchaseOrderApplicationServiceImpl.java:49) ~[classes/:?]
	at com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder(PurchaseOrderController.java:129) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at com.yxt.starter.filter.TraceIdFilter.doFilter(TraceIdFilter.java:60) ~[yxt-core-spring-boot-starter-4.6.4.jar:4.6.4]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at cn.hydee.starter.grey.springboot.web.GreyWebInterceptor.doFilter(GreyWebInterceptor.java:62) ~[grey-spring-boot-web-starter-2.0.0-20230815.020948-3.jar:2.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:836) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1747) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_202]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]

[2025-05-20 15:52:35.019] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 执行完成... 
[2025-05-20 15:52:35.019] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 开始执行...
[2025-05-20 15:52:35.153] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 执行完成... 
[2025-05-20 15:52:35.154] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 15:52:35.202] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 15:52:35.202] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 15:52:35.232] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 15:52:35.232] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 15:52:35.232] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["106632","847162","102443"],"storeCode":"JM0003"}
[2025-05-20 15:52:35.548] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"梓橦宫","drugType":0,"erpCode":"102443","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"昆明梓橦宫全新生物制药有限公司","name":"氨咖黄敏胶囊(速效伤风胶囊)_10粒_昆明梓橦宫全新生物","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10粒","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":88},{"brandName":"可孚","drugType":3,"erpCode":"106632","isColdChain":false,"isOfficial":false,"mainPic":"https://shop-cdn.yxtmart.cn/shop/101-100/106632/106632-2.jpg","manufacture":"可孚医疗科技股份有限公司000","name":"可孚医用棉签(灭菌)_10cm*50支","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10CM*50支","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":987},{"brandName":"楚天舒","drugType":3,"erpCode":"847162","isColdChain":false,"isOfficial":false,"manufacture":"湖北楚天舒药业有限公司","name":"楚天舒含糖金银花露_340ml","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"340ML","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":999958}],"msg":"操作成功","timestamp":1747727556645}
[2025-05-20 15:52:35.550] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["102443","106632","847162"],"storeCode":"JM0003"}
[2025-05-20 15:52:35.584] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"102443","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.700000,"storeCode":"JM0003","wareStock":88},{"erpCode":"106632","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.500000,"stock":0,"storeCode":"JM0003","wareStock":987},{"erpCode":"847162","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":3.500000,"storeCode":"JM0003","wareStock":999958}],"msg":"操作成功","timestamp":1747727556679}
[2025-05-20 15:52:35.591] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 15:52:35.591] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 15:52:35.594] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0003000002，明细数量：3
[2025-05-20 15:52:35.619] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler.doHandleReal:39] 采购单分组完成，共生成1个采购单
[2025-05-20 15:52:35.619] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 执行完成... 
[2025-05-20 15:52:35.619] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 开始执行...
[2025-05-20 15:53:30.687] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.zaxxer.hikari.pool.HikariPool.run:766] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=57s859ms793µs900ns).
[2025-05-20 15:53:30.691] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.doHandleReal:59] 提单失败，订单号PurchaseOrderNo(purchaseOrderNo=PO20250520JM0003000002) java.lang.NullPointerException: null
	at com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.settlement(SubmitPayOrderHandler.java:97) ~[classes/:?]
	at com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.doHandleReal(SubmitPayOrderHandler.java:53) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:26) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.service.impl.PurchaseOrderDomainServiceImpl.createPurchaseOrder(PurchaseOrderDomainServiceImpl.java:56) ~[classes/:?]
	at com.yxt.purchase.application.service.impl.PurchaseOrderApplicationServiceImpl.importPurchaseOrder(PurchaseOrderApplicationServiceImpl.java:49) ~[classes/:?]
	at com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder(PurchaseOrderController.java:129) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at com.yxt.starter.filter.TraceIdFilter.doFilter(TraceIdFilter.java:60) ~[yxt-core-spring-boot-starter-4.6.4.jar:4.6.4]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at cn.hydee.starter.grey.springboot.web.GreyWebInterceptor.doFilter(GreyWebInterceptor.java:62) ~[grey-spring-boot-web-starter-2.0.0-20230815.020948-3.jar:2.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:836) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1747) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_202]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]

[2025-05-20 15:53:30.691] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 执行完成... 
[2025-05-20 15:53:30.691] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 开始执行...
[2025-05-20 15:53:30.699] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 执行完成... 
[2025-05-20 15:53:30.700] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 15:53:30.807] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 15:53:30.808] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 15:53:30.840] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 15:53:30.840] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 15:53:30.840] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["106632","130969","765678"],"storeCode":"JM0004"}
[2025-05-20 15:53:31.153] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"可孚","drugType":3,"erpCode":"106632","isColdChain":false,"isOfficial":false,"mainPic":"https://shop-cdn.yxtmart.cn/shop/101-100/106632/106632-2.jpg","manufacture":"可孚医疗科技股份有限公司000","name":"可孚医用棉签(灭菌)_10cm*50支","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10CM*50支","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":987},{"brandName":"欧意","drugType":0,"erpCode":"130969","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"石药集团欧意药业有限公司","name":"奥美拉唑肠溶胶囊_欧意_20mg*16粒*1瓶_OTC","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"20MG*16粒*1瓶","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100}],"msg":"操作成功","timestamp":1747727612257}
[2025-05-20 15:53:31.153] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["106632","130969"],"storeCode":"JM0004"}
[2025-05-20 15:53:31.182] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"106632","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.500000,"stock":43,"storeCode":"JM0004","wareStock":987},{"erpCode":"130969","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":26.500000,"storeCode":"JM0004","wareStock":100}],"msg":"操作成功","timestamp":1747727612285}
[2025-05-20 15:53:31.184] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 15:53:31.184] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 15:53:31.193] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.utils.RedisIncrementUtil.getAndIncrement:52] Created new Redis key: purchase-order-index:20250520:JM0004, with expiration: 24 HOURS
[2025-05-20 15:53:31.195] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0004000001，明细数量：3
[2025-05-20 15:53:31.212] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler.doHandleReal:39] 采购单分组完成，共生成1个采购单
[2025-05-20 15:53:31.212] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 执行完成... 
[2025-05-20 15:53:31.212] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 开始执行...
[2025-05-20 15:53:32.948] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.doHandleReal:59] 提单失败，订单号PurchaseOrderNo(purchaseOrderNo=PO20250520JM0004000001) java.lang.NullPointerException: null
	at com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.settlement(SubmitPayOrderHandler.java:97) ~[classes/:?]
	at com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.doHandleReal(SubmitPayOrderHandler.java:53) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:26) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.service.impl.PurchaseOrderDomainServiceImpl.createPurchaseOrder(PurchaseOrderDomainServiceImpl.java:56) ~[classes/:?]
	at com.yxt.purchase.application.service.impl.PurchaseOrderApplicationServiceImpl.importPurchaseOrder(PurchaseOrderApplicationServiceImpl.java:49) ~[classes/:?]
	at com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder(PurchaseOrderController.java:129) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at com.yxt.starter.filter.TraceIdFilter.doFilter(TraceIdFilter.java:60) ~[yxt-core-spring-boot-starter-4.6.4.jar:4.6.4]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at cn.hydee.starter.grey.springboot.web.GreyWebInterceptor.doFilter(GreyWebInterceptor.java:62) ~[grey-spring-boot-web-starter-2.0.0-20230815.020948-3.jar:2.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:836) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1747) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_202]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]

[2025-05-20 15:53:32.948] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 执行完成... 
[2025-05-20 15:53:32.948] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 开始执行...
[2025-05-20 15:53:32.954] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 执行完成... 
[2025-05-20 17:57:55.237] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 17:57:56.725] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 17:57:57.143] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 17:58:03.365] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 17:58:04.622] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.baomidou.mybatisplus.core.injector.AbstractMethod.addMappedStatement:306] [com.yxt.purchase.infrastructure.mapper.PurchaseOrderMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-05-20 17:58:04.629] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.baomidou.mybatisplus.core.injector.AbstractMethod.addMappedStatement:306] [com.yxt.purchase.infrastructure.mapper.PurchaseOrderMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
[2025-05-20 17:58:04.643] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.baomidou.mybatisplus.core.injector.AbstractMethod.addMappedStatement:306] [com.yxt.purchase.infrastructure.mapper.PurchaseOrderMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-05-20 17:58:04.653] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.baomidou.mybatisplus.core.injector.AbstractMethod.addMappedStatement:306] [com.yxt.purchase.infrastructure.mapper.PurchaseOrderMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
[2025-05-20 17:58:05.555] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 17:58:05.590] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 17:58:05.630] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 17:58:05.644] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 17:58:07.320] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 17:58:07.339] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 17:58:07.351] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 17:58:07.374] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 17:58:07.388] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 17:58:07.472] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 17:58:07.492] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 17:58:07.566] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 17:58:08.100] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 17:58:08.394] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 20.131 seconds (JVM running for 23.435)
[2025-05-20 17:58:08.394] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 17:58:08.394] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 17:58:08.428] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 17:58:08.974] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 17:58:08.976] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 17:58:08.979] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 17:58:08.981] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 17:58:08.983] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 17:58:28.472] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 17:58:30.275] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 17:58:30.275] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 17:58:31.551] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 17:58:31.551] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 17:58:46.641] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["850774","142881","120200"],"storeCode":"JG0104"}
[2025-05-20 17:58:47.455] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[],"msg":"操作成功","timestamp":1747735127480}
[2025-05-20 17:59:34.898] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.zaxxer.hikari.pool.HikariPool.run:766] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=52s752µs800ns).
[2025-05-20 17:59:34.899] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 17:59:34.902] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 17:59:34.987] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JG0104000006，明细数量：3
[2025-05-20 18:15:33.099] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 18:15:34.434] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 18:15:34.834] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 18:15:41.180] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 18:15:42.249] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.baomidou.mybatisplus.core.injector.AbstractMethod.addMappedStatement:306] [com.yxt.purchase.infrastructure.mapper.PurchaseOrderMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-05-20 18:15:42.255] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.baomidou.mybatisplus.core.injector.AbstractMethod.addMappedStatement:306] [com.yxt.purchase.infrastructure.mapper.PurchaseOrderMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
[2025-05-20 18:15:42.258] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.baomidou.mybatisplus.core.injector.AbstractMethod.addMappedStatement:306] [com.yxt.purchase.infrastructure.mapper.PurchaseOrderMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-05-20 18:15:42.260] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.baomidou.mybatisplus.core.injector.AbstractMethod.addMappedStatement:306] [com.yxt.purchase.infrastructure.mapper.PurchaseOrderMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
[2025-05-20 18:15:43.120] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 18:15:43.147] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 18:15:43.181] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 18:15:43.192] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 18:15:44.970] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:15:44.997] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:15:45.007] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:15:45.024] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:15:45.039] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:15:45.120] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 18:15:45.142] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 18:15:45.213] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 18:15:45.777] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 18:15:46.026] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 19.937 seconds (JVM running for 23.093)
[2025-05-20 18:15:46.026] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 18:15:46.026] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 18:15:46.053] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 18:15:46.614] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:15:46.617] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:15:46.619] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:15:46.622] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:15:46.624] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:19:15.823] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:19:17.586] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:19:17.587] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:19:18.754] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:19:18.755] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:19:32.753] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["131452","100485","146949"],"storeCode":"JM0003"}
[2025-05-20 18:19:33.615] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"999","drugType":0,"erpCode":"100485","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州市九惠制药股份有限公司","memoryCode":"GMLKL","name":"感冒灵颗粒_999_10g*9袋_惠州九惠","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"purchaseUpperLimit":9999,"specValue":"10g*9袋","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041303","A041303003"],"wareStock":92},{"brandName":"惠","drugType":0,"erpCode":"131452","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州大亚制药股份有限公司","name":"清凉喉片_惠_16片","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"16片","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100},{"brandName":"天天乐","drugType":0,"erpCode":"146949","isColdChain":false,"isOfficial":false,"manufacture":"广西天天乐药业股份有限公司(原：广西天天乐药业有限公司）","name":"氨咖黄敏胶囊(速效伤风胶囊)_天天乐_10粒","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10粒","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":99}],"msg":"操作成功","timestamp":1747736373728}
[2025-05-20 18:19:33.618] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["100485","131452","146949"],"storeCode":"JM0003"}
[2025-05-20 18:19:33.650] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"100485","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.000000,"stock":2,"storeCode":"JM0003","wareStock":92},{"erpCode":"131452","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.000000,"stock":16,"storeCode":"JM0003","wareStock":100},{"erpCode":"146949","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.600000,"storeCode":"JM0003","wareStock":99}],"msg":"操作成功","timestamp":1747736373777}
[2025-05-20 18:19:33.656] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:19:33.656] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:19:33.723] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0003000003，明细数量：3
[2025-05-20 18:19:33.966] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:19:34.007] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:19:34.007] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:19:34.112] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:19:34.113] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:19:39.543] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["106632","130969","765678"],"storeCode":"JM0004"}
[2025-05-20 18:19:39.668] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"可孚","drugType":3,"erpCode":"106632","isColdChain":false,"isOfficial":false,"mainPic":"https://shop-cdn.yxtmart.cn/shop/101-100/106632/106632-2.jpg","manufacture":"可孚医疗科技股份有限公司000","name":"可孚医用棉签(灭菌)_10cm*50支","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10CM*50支","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":987},{"brandName":"欧意","drugType":0,"erpCode":"130969","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"石药集团欧意药业有限公司","name":"奥美拉唑肠溶胶囊_欧意_20mg*16粒*1瓶_OTC","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"20MG*16粒*1瓶","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100}],"msg":"操作成功","timestamp":1747736379809}
[2025-05-20 18:19:39.668] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["106632","130969"],"storeCode":"JM0004"}
[2025-05-20 18:19:39.685] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"106632","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.500000,"stock":43,"storeCode":"JM0004","wareStock":987},{"erpCode":"130969","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":26.500000,"storeCode":"JM0004","wareStock":100}],"msg":"操作成功","timestamp":1747736379825}
[2025-05-20 18:19:39.685] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:19:39.685] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:19:39.691] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0004000002，明细数量：3
[2025-05-20 18:19:39.715] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 42][com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder:137] 导入采购单失败 java.lang.RuntimeException: nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'companyName' in 'class com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDO',
nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'companyName' in 'class com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDO'
	at com.yxt.purchase.domain.service.impl.PurchaseOrderDomainServiceImpl.createPurchaseOrder(PurchaseOrderDomainServiceImpl.java:66) ~[classes/:?]
	at com.yxt.purchase.application.service.impl.PurchaseOrderApplicationServiceImpl.importPurchaseOrder(PurchaseOrderApplicationServiceImpl.java:49) ~[classes/:?]
	at com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder(PurchaseOrderController.java:129) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at com.yxt.starter.filter.TraceIdFilter.doFilter(TraceIdFilter.java:60) ~[yxt-core-spring-boot-starter-4.6.4.jar:4.6.4]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at cn.hydee.starter.grey.springboot.web.GreyWebInterceptor.doFilter(GreyWebInterceptor.java:62) ~[grey-spring-boot-web-starter-2.0.0-20230815.020948-3.jar:2.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:836) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1747) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_202]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]

[2025-05-20 18:20:28.458] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.RemoteConfigRepository.loadApolloConfig:194] Load config failed, will retry in 1 SECONDS. appId: purchase-order-center, cluster: default, namespaces: bizconfig
[2025-05-20 18:20:29.464] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26] Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: purchase-order-center, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/purchase-order-center/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: purchase-order-center, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
[2025-05-20 18:21:32.848] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 18:21:33.916] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 18:21:34.237] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 18:21:40.470] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 18:21:41.613] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.baomidou.mybatisplus.core.injector.AbstractMethod.addMappedStatement:306] [com.yxt.purchase.infrastructure.mapper.PurchaseOrderMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-05-20 18:21:41.620] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.baomidou.mybatisplus.core.injector.AbstractMethod.addMappedStatement:306] [com.yxt.purchase.infrastructure.mapper.PurchaseOrderMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
[2025-05-20 18:21:41.624] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.baomidou.mybatisplus.core.injector.AbstractMethod.addMappedStatement:306] [com.yxt.purchase.infrastructure.mapper.PurchaseOrderMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-05-20 18:21:41.627] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.baomidou.mybatisplus.core.injector.AbstractMethod.addMappedStatement:306] [com.yxt.purchase.infrastructure.mapper.PurchaseOrderMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
[2025-05-20 18:21:42.411] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 18:21:42.444] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 18:21:42.486] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 18:21:42.498] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 18:21:44.074] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:21:44.087] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:21:44.098] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:21:44.118] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:21:44.133] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:21:44.214] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 18:21:44.235] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 18:21:44.315] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 18:21:44.847] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 18:21:45.104] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 19.027 seconds (JVM running for 21.827)
[2025-05-20 18:21:45.104] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 18:21:45.104] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 18:21:45.134] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 18:21:45.925] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:21:45.928] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:21:45.931] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:21:45.933] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:21:45.936] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:21:54.630] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:21:56.311] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:21:56.311] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:21:56.853] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:21:56.853] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:21:59.153] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["131452","100485","146949"],"storeCode":"JM0003"}
[2025-05-20 18:21:59.472] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"999","drugType":0,"erpCode":"100485","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州市九惠制药股份有限公司","memoryCode":"GMLKL","name":"感冒灵颗粒_999_10g*9袋_惠州九惠","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"purchaseUpperLimit":9999,"specValue":"10g*9袋","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041303","A041303003"],"wareStock":92},{"brandName":"惠","drugType":0,"erpCode":"131452","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州大亚制药股份有限公司","name":"清凉喉片_惠_16片","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"16片","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100},{"brandName":"天天乐","drugType":0,"erpCode":"146949","isColdChain":false,"isOfficial":false,"manufacture":"广西天天乐药业股份有限公司(原：广西天天乐药业有限公司）","name":"氨咖黄敏胶囊(速效伤风胶囊)_天天乐_10粒","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10粒","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":99}],"msg":"操作成功","timestamp":1747736519567}
[2025-05-20 18:21:59.474] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["100485","131452","146949"],"storeCode":"JM0003"}
[2025-05-20 18:21:59.534] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"100485","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.000000,"stock":2,"storeCode":"JM0003","wareStock":92},{"erpCode":"131452","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.000000,"stock":16,"storeCode":"JM0003","wareStock":100},{"erpCode":"146949","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.600000,"storeCode":"JM0003","wareStock":99}],"msg":"操作成功","timestamp":1747736519670}
[2025-05-20 18:21:59.540] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:21:59.540] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:21:59.599] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0003000004，明细数量：3
[2025-05-20 18:21:59.827] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:21:59.882] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:21:59.882] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:21:59.919] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:21:59.919] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:22:09.996] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["106632","130969","765678"],"storeCode":"JM0004"}
[2025-05-20 18:22:10.040] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"可孚","drugType":3,"erpCode":"106632","isColdChain":false,"isOfficial":false,"mainPic":"https://shop-cdn.yxtmart.cn/shop/101-100/106632/106632-2.jpg","manufacture":"可孚医疗科技股份有限公司000","name":"可孚医用棉签(灭菌)_10cm*50支","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10CM*50支","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":987},{"brandName":"欧意","drugType":0,"erpCode":"130969","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"石药集团欧意药业有限公司","name":"奥美拉唑肠溶胶囊_欧意_20mg*16粒*1瓶_OTC","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"20MG*16粒*1瓶","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100}],"msg":"操作成功","timestamp":1747736530187}
[2025-05-20 18:22:10.041] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["106632","130969"],"storeCode":"JM0004"}
[2025-05-20 18:22:10.057] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"106632","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.500000,"stock":43,"storeCode":"JM0004","wareStock":987},{"erpCode":"130969","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":26.500000,"storeCode":"JM0004","wareStock":100}],"msg":"操作成功","timestamp":1747736530204}
[2025-05-20 18:22:10.058] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:22:10.058] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:22:10.064] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0004000003，明细数量：3
[2025-05-20 18:22:10.086] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 41][com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder:137] 导入采购单失败 java.lang.RuntimeException: nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'companyName' in 'class com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDO',
nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'companyName' in 'class com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDO'
	at com.yxt.purchase.domain.service.impl.PurchaseOrderDomainServiceImpl.createPurchaseOrder(PurchaseOrderDomainServiceImpl.java:66) ~[classes/:?]
	at com.yxt.purchase.application.service.impl.PurchaseOrderApplicationServiceImpl.importPurchaseOrder(PurchaseOrderApplicationServiceImpl.java:49) ~[classes/:?]
	at com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder(PurchaseOrderController.java:129) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at com.yxt.starter.filter.TraceIdFilter.doFilter(TraceIdFilter.java:60) ~[yxt-core-spring-boot-starter-4.6.4.jar:4.6.4]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at cn.hydee.starter.grey.springboot.web.GreyWebInterceptor.doFilter(GreyWebInterceptor.java:62) ~[grey-spring-boot-web-starter-2.0.0-20230815.020948-3.jar:2.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:836) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1747) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_202]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]

[2025-05-20 18:22:22.699] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:22:22.754] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:22:22.755] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:22:22.785] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:22:22.785] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:22:25.694] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["131452","100485","146949"],"storeCode":"JM0003"}
[2025-05-20 18:22:25.736] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"999","drugType":0,"erpCode":"100485","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州市九惠制药股份有限公司","memoryCode":"GMLKL","name":"感冒灵颗粒_999_10g*9袋_惠州九惠","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"purchaseUpperLimit":9999,"specValue":"10g*9袋","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041303","A041303003"],"wareStock":92},{"brandName":"惠","drugType":0,"erpCode":"131452","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州大亚制药股份有限公司","name":"清凉喉片_惠_16片","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"16片","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100},{"brandName":"天天乐","drugType":0,"erpCode":"146949","isColdChain":false,"isOfficial":false,"manufacture":"广西天天乐药业股份有限公司(原：广西天天乐药业有限公司）","name":"氨咖黄敏胶囊(速效伤风胶囊)_天天乐_10粒","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10粒","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":99}],"msg":"操作成功","timestamp":1747736545882}
[2025-05-20 18:22:25.736] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["100485","131452","146949"],"storeCode":"JM0003"}
[2025-05-20 18:22:25.749] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"100485","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.000000,"stock":2,"storeCode":"JM0003","wareStock":92},{"erpCode":"131452","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.000000,"stock":16,"storeCode":"JM0003","wareStock":100},{"erpCode":"146949","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.600000,"storeCode":"JM0003","wareStock":99}],"msg":"操作成功","timestamp":1747736545896}
[2025-05-20 18:22:25.750] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:22:25.750] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:22:41.698] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0003000005，明细数量：3
[2025-05-20 18:24:24.267] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.zaxxer.hikari.pool.HikariPool.run:766] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m7s727ms679µs100ns).
[2025-05-20 18:24:24.276] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:24:24.342] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:24:24.342] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:24:24.400] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:24:24.401] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:24:25.862] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["106632","130969","765678"],"storeCode":"JM0004"}
[2025-05-20 18:24:25.915] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"可孚","drugType":3,"erpCode":"106632","isColdChain":false,"isOfficial":false,"mainPic":"https://shop-cdn.yxtmart.cn/shop/101-100/106632/106632-2.jpg","manufacture":"可孚医疗科技股份有限公司000","name":"可孚医用棉签(灭菌)_10cm*50支","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10CM*50支","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":987},{"brandName":"欧意","drugType":0,"erpCode":"130969","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"石药集团欧意药业有限公司","name":"奥美拉唑肠溶胶囊_欧意_20mg*16粒*1瓶_OTC","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"20MG*16粒*1瓶","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100}],"msg":"操作成功","timestamp":1747736666050}
[2025-05-20 18:24:25.915] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["106632","130969"],"storeCode":"JM0004"}
[2025-05-20 18:24:25.944] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"106632","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.500000,"stock":43,"storeCode":"JM0004","wareStock":987},{"erpCode":"130969","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":26.500000,"storeCode":"JM0004","wareStock":100}],"msg":"操作成功","timestamp":1747736666081}
[2025-05-20 18:24:25.944] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:24:25.944] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:24:25.960] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0004000004，明细数量：3
[2025-05-20 18:24:26.697] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 41][com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder:137] 导入采购单失败 java.lang.RuntimeException: nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'companyName' in 'class com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDO',
nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'companyName' in 'class com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDO'
	at com.yxt.purchase.domain.service.impl.PurchaseOrderDomainServiceImpl.createPurchaseOrder(PurchaseOrderDomainServiceImpl.java:66) ~[classes/:?]
	at com.yxt.purchase.application.service.impl.PurchaseOrderApplicationServiceImpl.importPurchaseOrder(PurchaseOrderApplicationServiceImpl.java:49) ~[classes/:?]
	at com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder(PurchaseOrderController.java:129) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at com.yxt.starter.filter.TraceIdFilter.doFilter(TraceIdFilter.java:60) ~[yxt-core-spring-boot-starter-4.6.4.jar:4.6.4]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at cn.hydee.starter.grey.springboot.web.GreyWebInterceptor.doFilter(GreyWebInterceptor.java:62) ~[grey-spring-boot-web-starter-2.0.0-20230815.020948-3.jar:2.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:836) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1747) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_202]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]

[2025-05-20 18:24:32.409] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:24:32.463] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:24:32.463] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:24:32.490] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:24:32.491] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:24:34.849] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["131452","100485","146949"],"storeCode":"JM0003"}
[2025-05-20 18:24:34.904] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"999","drugType":0,"erpCode":"100485","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州市九惠制药股份有限公司","memoryCode":"GMLKL","name":"感冒灵颗粒_999_10g*9袋_惠州九惠","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"purchaseUpperLimit":9999,"specValue":"10g*9袋","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041303","A041303003"],"wareStock":92},{"brandName":"惠","drugType":0,"erpCode":"131452","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州大亚制药股份有限公司","name":"清凉喉片_惠_16片","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"16片","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100},{"brandName":"天天乐","drugType":0,"erpCode":"146949","isColdChain":false,"isOfficial":false,"manufacture":"广西天天乐药业股份有限公司(原：广西天天乐药业有限公司）","name":"氨咖黄敏胶囊(速效伤风胶囊)_天天乐_10粒","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10粒","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":99}],"msg":"操作成功","timestamp":1747736675057}
[2025-05-20 18:24:34.904] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["100485","131452","146949"],"storeCode":"JM0003"}
[2025-05-20 18:24:34.917] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"100485","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.000000,"stock":2,"storeCode":"JM0003","wareStock":92},{"erpCode":"131452","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.000000,"stock":16,"storeCode":"JM0003","wareStock":100},{"erpCode":"146949","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.600000,"storeCode":"JM0003","wareStock":99}],"msg":"操作成功","timestamp":1747736675070}
[2025-05-20 18:24:34.918] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:24:34.918] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:24:34.921] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0003000006，明细数量：3
[2025-05-20 18:24:36.025] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:24:36.122] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:24:36.122] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:24:36.152] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:24:36.152] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:24:36.152] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["106632","130969","765678"],"storeCode":"JM0004"}
[2025-05-20 18:24:36.212] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"可孚","drugType":3,"erpCode":"106632","isColdChain":false,"isOfficial":false,"mainPic":"https://shop-cdn.yxtmart.cn/shop/101-100/106632/106632-2.jpg","manufacture":"可孚医疗科技股份有限公司000","name":"可孚医用棉签(灭菌)_10cm*50支","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10CM*50支","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":987},{"brandName":"欧意","drugType":0,"erpCode":"130969","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"石药集团欧意药业有限公司","name":"奥美拉唑肠溶胶囊_欧意_20mg*16粒*1瓶_OTC","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"20MG*16粒*1瓶","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100}],"msg":"操作成功","timestamp":1747736676364}
[2025-05-20 18:24:36.213] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["106632","130969"],"storeCode":"JM0004"}
[2025-05-20 18:24:36.229] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"106632","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.500000,"stock":43,"storeCode":"JM0004","wareStock":987},{"erpCode":"130969","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":26.500000,"storeCode":"JM0004","wareStock":100}],"msg":"操作成功","timestamp":1747736676382}
[2025-05-20 18:24:36.229] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:24:36.230] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:24:36.233] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0004000005，明细数量：3
[2025-05-20 18:24:37.639] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 41][com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder:137] 导入采购单失败 java.lang.RuntimeException: nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'companyName' in 'class com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDO',
nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'companyName' in 'class com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDO'
	at com.yxt.purchase.domain.service.impl.PurchaseOrderDomainServiceImpl.createPurchaseOrder(PurchaseOrderDomainServiceImpl.java:66) ~[classes/:?]
	at com.yxt.purchase.application.service.impl.PurchaseOrderApplicationServiceImpl.importPurchaseOrder(PurchaseOrderApplicationServiceImpl.java:49) ~[classes/:?]
	at com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder(PurchaseOrderController.java:129) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at com.yxt.starter.filter.TraceIdFilter.doFilter(TraceIdFilter.java:60) ~[yxt-core-spring-boot-starter-4.6.4.jar:4.6.4]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at cn.hydee.starter.grey.springboot.web.GreyWebInterceptor.doFilter(GreyWebInterceptor.java:62) ~[grey-spring-boot-web-starter-2.0.0-20230815.020948-3.jar:2.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:836) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1747) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_202]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]

[2025-05-20 18:25:15.366] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:25:15.421] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:25:15.422] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:25:15.445] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:25:15.446] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:25:15.446] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["131452","100485","146949"],"storeCode":"JM0003"}
[2025-05-20 18:25:15.521] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"999","drugType":0,"erpCode":"100485","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州市九惠制药股份有限公司","memoryCode":"GMLKL","name":"感冒灵颗粒_999_10g*9袋_惠州九惠","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"purchaseUpperLimit":9999,"specValue":"10g*9袋","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041303","A041303003"],"wareStock":92},{"brandName":"惠","drugType":0,"erpCode":"131452","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州大亚制药股份有限公司","name":"清凉喉片_惠_16片","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"16片","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100},{"brandName":"天天乐","drugType":0,"erpCode":"146949","isColdChain":false,"isOfficial":false,"manufacture":"广西天天乐药业股份有限公司(原：广西天天乐药业有限公司）","name":"氨咖黄敏胶囊(速效伤风胶囊)_天天乐_10粒","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10粒","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":99}],"msg":"操作成功","timestamp":1747736715672}
[2025-05-20 18:25:15.522] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["100485","131452","146949"],"storeCode":"JM0003"}
[2025-05-20 18:25:15.549] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"100485","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.000000,"stock":2,"storeCode":"JM0003","wareStock":92},{"erpCode":"131452","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.000000,"stock":16,"storeCode":"JM0003","wareStock":100},{"erpCode":"146949","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.600000,"storeCode":"JM0003","wareStock":99}],"msg":"操作成功","timestamp":1747736715693}
[2025-05-20 18:25:15.552] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:25:15.552] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:25:15.593] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0003000007，明细数量：3
[2025-05-20 18:25:19.344] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:25:19.385] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:25:19.385] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:25:19.407] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:25:19.407] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:25:19.407] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["106632","130969","765678"],"storeCode":"JM0004"}
[2025-05-20 18:25:19.490] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"可孚","drugType":3,"erpCode":"106632","isColdChain":false,"isOfficial":false,"mainPic":"https://shop-cdn.yxtmart.cn/shop/101-100/106632/106632-2.jpg","manufacture":"可孚医疗科技股份有限公司000","name":"可孚医用棉签(灭菌)_10cm*50支","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10CM*50支","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":987},{"brandName":"欧意","drugType":0,"erpCode":"130969","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"石药集团欧意药业有限公司","name":"奥美拉唑肠溶胶囊_欧意_20mg*16粒*1瓶_OTC","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"20MG*16粒*1瓶","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100}],"msg":"操作成功","timestamp":1747736719644}
[2025-05-20 18:25:19.490] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["106632","130969"],"storeCode":"JM0004"}
[2025-05-20 18:25:19.508] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"106632","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.500000,"stock":43,"storeCode":"JM0004","wareStock":987},{"erpCode":"130969","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":26.500000,"storeCode":"JM0004","wareStock":100}],"msg":"操作成功","timestamp":1747736719663}
[2025-05-20 18:25:19.509] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:25:19.509] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:25:19.528] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0004000006，明细数量：3
[2025-05-20 18:25:20.259] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 41][com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder:137] 导入采购单失败 java.lang.RuntimeException: nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'companyName' in 'class com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDO',
nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'companyName' in 'class com.yxt.purchase.infrastructure.dataobject.PurchaseOrderDO'
	at com.yxt.purchase.domain.service.impl.PurchaseOrderDomainServiceImpl.createPurchaseOrder(PurchaseOrderDomainServiceImpl.java:66) ~[classes/:?]
	at com.yxt.purchase.application.service.impl.PurchaseOrderApplicationServiceImpl.importPurchaseOrder(PurchaseOrderApplicationServiceImpl.java:49) ~[classes/:?]
	at com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder(PurchaseOrderController.java:129) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at com.yxt.starter.filter.TraceIdFilter.doFilter(TraceIdFilter.java:60) ~[yxt-core-spring-boot-starter-4.6.4.jar:4.6.4]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at cn.hydee.starter.grey.springboot.web.GreyWebInterceptor.doFilter(GreyWebInterceptor.java:62) ~[grey-spring-boot-web-starter-2.0.0-20230815.020948-3.jar:2.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:836) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1747) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_202]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]

[2025-05-20 18:27:33.374] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 18:27:34.526] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 18:27:34.894] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 18:27:41.087] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 18:27:42.889] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 18:27:42.916] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 18:27:42.952] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 18:27:42.962] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 18:27:44.513] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:27:44.530] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:27:44.541] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:27:44.557] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:27:44.573] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:27:44.662] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 18:27:44.683] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 18:27:44.768] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 18:27:45.325] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 18:27:45.592] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 19.316 seconds (JVM running for 22.583)
[2025-05-20 18:27:45.593] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 18:27:45.593] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 18:27:45.621] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 18:27:46.323] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:27:46.339] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:27:46.343] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:27:46.346] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:27:46.349] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:29:04.851] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 18:29:05.867] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 18:29:06.191] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 18:29:12.363] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 18:29:13.987] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 18:29:14.010] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 18:29:14.044] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 18:29:14.055] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 18:29:15.549] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:29:15.579] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:29:15.590] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:29:15.612] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:29:15.622] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:29:15.704] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 18:29:15.727] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 18:29:15.804] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 18:29:16.389] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 18:29:16.669] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 18.353 seconds (JVM running for 21.157)
[2025-05-20 18:29:16.670] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 18:29:16.670] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 18:29:16.697] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 18:29:17.304] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:29:17.306] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:29:17.309] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:29:17.313] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:29:17.316] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:29:30.067] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:29:31.806] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:29:31.806] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:29:32.360] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:29:32.360] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:29:32.420] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["131452","100485","146949"],"storeCode":"JM0003"}
[2025-05-20 18:29:32.542] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"999","drugType":0,"erpCode":"100485","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州市九惠制药股份有限公司","memoryCode":"GMLKL","name":"感冒灵颗粒_999_10g*9袋_惠州九惠","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"purchaseUpperLimit":9999,"specValue":"10g*9袋","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041303","A041303003"],"wareStock":92},{"brandName":"惠","drugType":0,"erpCode":"131452","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州大亚制药股份有限公司","name":"清凉喉片_惠_16片","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"16片","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100},{"brandName":"天天乐","drugType":0,"erpCode":"146949","isColdChain":false,"isOfficial":false,"manufacture":"广西天天乐药业股份有限公司(原：广西天天乐药业有限公司）","name":"氨咖黄敏胶囊(速效伤风胶囊)_天天乐_10粒","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10粒","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":99}],"msg":"操作成功","timestamp":1747736972676}
[2025-05-20 18:29:32.545] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["100485","131452","146949"],"storeCode":"JM0003"}
[2025-05-20 18:29:32.584] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"100485","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.000000,"stock":2,"storeCode":"JM0003","wareStock":92},{"erpCode":"131452","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.000000,"stock":16,"storeCode":"JM0003","wareStock":100},{"erpCode":"146949","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.600000,"storeCode":"JM0003","wareStock":99}],"msg":"操作成功","timestamp":1747736972740}
[2025-05-20 18:29:32.590] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:29:32.590] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:29:32.650] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0003000008，明细数量：3
[2025-05-20 18:30:15.304] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.zaxxer.hikari.pool.HikariPool.run:766] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=57s403ms679µs500ns).
[2025-05-20 18:30:15.878] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler.doHandleReal:39] 采购单分组完成，共生成1个采购单
[2025-05-20 18:30:15.878] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 执行完成... 
[2025-05-20 18:30:15.878] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 开始执行...
[2025-05-20 18:30:15.879] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:30:15.880] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 18:30:15.931] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][org.springframework.context.annotation.AnnotationConfigApplicationContext.refresh:557] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'retryableRibbonLoadBalancingHttpClient' defined in org.springframework.cloud.netflix.ribbon.apache.HttpClientRibbonConfiguration: Unsatisfied dependency expressed through method 'retryableRibbonLoadBalancingHttpClient' parameter 2; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'ribbonLoadBalancer' defined in org.springframework.cloud.netflix.ribbon.GreyRibbonClientConfiguration: Unsatisfied dependency expressed through method 'ribbonLoadBalancer' parameter 3; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'ribbonRule': Unsatisfied dependency expressed through field 'smoothService'; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'buildSmoothService': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
[2025-05-20 18:30:15.966] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 40][com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder:137] 导入采购单失败 java.lang.RuntimeException: null,
com.netflix.client.ClientException
	at com.yxt.purchase.domain.service.impl.PurchaseOrderDomainServiceImpl.createPurchaseOrder(PurchaseOrderDomainServiceImpl.java:66) ~[classes/:?]
	at com.yxt.purchase.application.service.impl.PurchaseOrderApplicationServiceImpl.importPurchaseOrder(PurchaseOrderApplicationServiceImpl.java:49) ~[classes/:?]
	at com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder(PurchaseOrderController.java:129) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at com.yxt.starter.filter.TraceIdFilter.doFilter(TraceIdFilter.java:60) ~[yxt-core-spring-boot-starter-4.6.4.jar:4.6.4]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at cn.hydee.starter.grey.springboot.web.GreyWebInterceptor.doFilter(GreyWebInterceptor.java:62) ~[grey-spring-boot-web-starter-2.0.0-20230815.020948-3.jar:2.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:836) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1747) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_202]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]

[2025-05-20 18:30:27.994] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 18:30:29.072] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 18:30:29.443] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 18:30:35.721] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 18:30:38.059] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 18:30:38.087] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 18:30:38.121] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 18:30:38.131] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 18:30:39.751] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:30:39.764] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:30:39.774] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:30:39.805] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:30:39.820] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:30:39.907] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 18:30:39.938] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 18:30:40.013] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 18:30:40.542] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 18:30:40.849] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 19.678 seconds (JVM running for 22.542)
[2025-05-20 18:30:40.849] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 18:30:40.849] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 18:30:40.875] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 18:30:41.266] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:30:41.268] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:30:41.270] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:30:41.272] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:30:41.274] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:30:52.805] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:30:54.585] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:30:54.585] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:30:55.347] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:30:55.347] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:30:55.424] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["131452","100485","146949"],"storeCode":"JM0003"}
[2025-05-20 18:30:55.649] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"999","drugType":0,"erpCode":"100485","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州市九惠制药股份有限公司","memoryCode":"GMLKL","name":"感冒灵颗粒_999_10g*9袋_惠州九惠","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"purchaseUpperLimit":9999,"specValue":"10g*9袋","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041303","A041303003"],"wareStock":92},{"brandName":"惠","drugType":0,"erpCode":"131452","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州大亚制药股份有限公司","name":"清凉喉片_惠_16片","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"16片","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100},{"brandName":"天天乐","drugType":0,"erpCode":"146949","isColdChain":false,"isOfficial":false,"manufacture":"广西天天乐药业股份有限公司(原：广西天天乐药业有限公司）","name":"氨咖黄敏胶囊(速效伤风胶囊)_天天乐_10粒","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10粒","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":99}],"msg":"操作成功","timestamp":1747737055785}
[2025-05-20 18:30:55.650] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["100485","131452","146949"],"storeCode":"JM0003"}
[2025-05-20 18:30:55.687] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"100485","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.000000,"stock":2,"storeCode":"JM0003","wareStock":92},{"erpCode":"131452","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.000000,"stock":16,"storeCode":"JM0003","wareStock":100},{"erpCode":"146949","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.600000,"storeCode":"JM0003","wareStock":99}],"msg":"操作成功","timestamp":1747737055847}
[2025-05-20 18:30:55.694] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:30:55.694] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:30:55.754] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0003000009，明细数量：3
[2025-05-20 18:31:02.850] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler.doHandleReal:39] 采购单分组完成，共生成1个采购单
[2025-05-20 18:31:02.850] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 执行完成... 
[2025-05-20 18:31:02.850] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 开始执行...
[2025-05-20 18:31:49.642] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:31:49.640] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.zaxxer.hikari.pool.HikariPool.run:766] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m7s683ms190µs).
[2025-05-20 18:31:49.700] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:31:49.701] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:31:49.724] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:31:49.724] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:31:49.724] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["106632","130969","765678"],"storeCode":"JM0004"}
[2025-05-20 18:31:49.794] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"可孚","drugType":3,"erpCode":"106632","isColdChain":false,"isOfficial":false,"mainPic":"https://shop-cdn.yxtmart.cn/shop/101-100/106632/106632-2.jpg","manufacture":"可孚医疗科技股份有限公司000","name":"可孚医用棉签(灭菌)_10cm*50支","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10CM*50支","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":987},{"brandName":"欧意","drugType":0,"erpCode":"130969","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"石药集团欧意药业有限公司","name":"奥美拉唑肠溶胶囊_欧意_20mg*16粒*1瓶_OTC","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"20MG*16粒*1瓶","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100}],"msg":"操作成功","timestamp":1747737109942}
[2025-05-20 18:31:49.797] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["106632","130969"],"storeCode":"JM0004"}
[2025-05-20 18:31:49.811] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 18:31:49.850] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"106632","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.500000,"stock":43,"storeCode":"JM0004","wareStock":987},{"erpCode":"130969","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":26.500000,"storeCode":"JM0004","wareStock":100}],"msg":"操作成功","timestamp":1747737110018}
[2025-05-20 18:31:49.854] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:31:49.854] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:31:49.868] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0004000007，明细数量：3
[2025-05-20 18:31:49.883] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler.doHandleReal:39] 采购单分组完成，共生成1个采购单
[2025-05-20 18:31:49.883] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 执行完成... 
[2025-05-20 18:31:49.883] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 开始执行...
[2025-05-20 18:31:49.883] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 执行完成... 
[2025-05-20 18:31:49.883] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 开始执行...
[2025-05-20 18:32:24.946] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 18:32:26.091] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 18:32:26.438] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 18:32:32.562] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 18:32:34.181] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 18:32:34.208] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 18:32:34.243] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 18:32:34.253] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 18:32:35.821] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:32:35.836] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:32:35.846] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:32:35.865] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:32:35.881] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:32:35.965] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 18:32:35.991] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 18:32:36.083] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 18:32:36.648] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 18:32:36.920] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 18.715 seconds (JVM running for 21.441)
[2025-05-20 18:32:36.920] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 18:32:36.920] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 18:32:36.949] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 18:32:37.372] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:32:37.374] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:32:37.378] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:32:37.380] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:32:37.382] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:32:45.810] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:32:47.570] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:32:47.570] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:32:48.258] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:32:48.258] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:32:48.322] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["131452","100485","146949"],"storeCode":"JM0003"}
[2025-05-20 18:32:48.544] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"999","drugType":0,"erpCode":"100485","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州市九惠制药股份有限公司","memoryCode":"GMLKL","name":"感冒灵颗粒_999_10g*9袋_惠州九惠","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"purchaseUpperLimit":9999,"specValue":"10g*9袋","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041303","A041303003"],"wareStock":92},{"brandName":"惠","drugType":0,"erpCode":"131452","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州大亚制药股份有限公司","name":"清凉喉片_惠_16片","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"16片","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100},{"brandName":"天天乐","drugType":0,"erpCode":"146949","isColdChain":false,"isOfficial":false,"manufacture":"广西天天乐药业股份有限公司(原：广西天天乐药业有限公司）","name":"氨咖黄敏胶囊(速效伤风胶囊)_天天乐_10粒","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10粒","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":99}],"msg":"操作成功","timestamp":1747737168688}
[2025-05-20 18:32:48.547] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["100485","131452","146949"],"storeCode":"JM0003"}
[2025-05-20 18:32:48.571] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"100485","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.000000,"stock":2,"storeCode":"JM0003","wareStock":92},{"erpCode":"131452","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.000000,"stock":16,"storeCode":"JM0003","wareStock":100},{"erpCode":"146949","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.600000,"storeCode":"JM0003","wareStock":99}],"msg":"操作成功","timestamp":1747737168736}
[2025-05-20 18:32:48.577] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:32:48.577] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:32:48.633] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0003000010，明细数量：3
[2025-05-20 18:32:51.187] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler.doHandleReal:39] 采购单分组完成，共生成1个采购单
[2025-05-20 18:32:51.187] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 执行完成... 
[2025-05-20 18:32:51.187] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 开始执行...
[2025-05-20 18:33:17.298] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.doHandleReal:68] 提单失败，订单号PurchaseOrderNo(purchaseOrderNo=PO20250520JM0003000010) java.lang.NullPointerException: null
	at com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.settlement(SubmitPayOrderHandler.java:106) ~[classes/:?]
	at com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.doHandleReal(SubmitPayOrderHandler.java:62) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:26) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.service.impl.PurchaseOrderDomainServiceImpl.createPurchaseOrder(PurchaseOrderDomainServiceImpl.java:56) ~[classes/:?]
	at com.yxt.purchase.application.service.impl.PurchaseOrderApplicationServiceImpl.importPurchaseOrder(PurchaseOrderApplicationServiceImpl.java:49) ~[classes/:?]
	at com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder(PurchaseOrderController.java:129) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at com.yxt.starter.filter.TraceIdFilter.doFilter(TraceIdFilter.java:60) ~[yxt-core-spring-boot-starter-4.6.4.jar:4.6.4]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at cn.hydee.starter.grey.springboot.web.GreyWebInterceptor.doFilter(GreyWebInterceptor.java:62) ~[grey-spring-boot-web-starter-2.0.0-20230815.020948-3.jar:2.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:836) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1747) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_202]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]

[2025-05-20 18:33:17.299] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 执行完成... 
[2025-05-20 18:33:17.300] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 开始执行...
[2025-05-20 18:33:17.438] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 执行完成... 
[2025-05-20 18:33:17.439] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:33:17.478] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:33:17.478] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:33:17.503] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:33:17.503] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:33:17.506] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["106632","130969","765678"],"storeCode":"JM0004"}
[2025-05-20 18:33:17.534] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"可孚","drugType":3,"erpCode":"106632","isColdChain":false,"isOfficial":false,"mainPic":"https://shop-cdn.yxtmart.cn/shop/101-100/106632/106632-2.jpg","manufacture":"可孚医疗科技股份有限公司000","name":"可孚医用棉签(灭菌)_10cm*50支","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10CM*50支","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":987},{"brandName":"欧意","drugType":0,"erpCode":"130969","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"石药集团欧意药业有限公司","name":"奥美拉唑肠溶胶囊_欧意_20mg*16粒*1瓶_OTC","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"20MG*16粒*1瓶","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100}],"msg":"操作成功","timestamp":1747737197706}
[2025-05-20 18:33:17.534] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["106632","130969"],"storeCode":"JM0004"}
[2025-05-20 18:33:17.546] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"106632","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.500000,"stock":43,"storeCode":"JM0004","wareStock":987},{"erpCode":"130969","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":26.500000,"storeCode":"JM0004","wareStock":100}],"msg":"操作成功","timestamp":1747737197719}
[2025-05-20 18:33:17.546] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:33:17.546] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:33:17.552] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0004000008，明细数量：3
[2025-05-20 18:33:19.522] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler.doHandleReal:39] 采购单分组完成，共生成1个采购单
[2025-05-20 18:33:19.522] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 执行完成... 
[2025-05-20 18:33:19.522] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 开始执行...
[2025-05-20 18:33:20.434] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 执行完成... 
[2025-05-20 18:33:20.436] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 开始执行...
[2025-05-20 18:33:20.444] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 执行完成... 
[2025-05-20 18:34:50.069] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:34:50.117] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:34:50.117] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:34:50.140] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:34:50.141] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:34:50.141] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["131452","100485","146949"],"storeCode":"JM0003"}
[2025-05-20 18:34:50.539] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"999","drugType":0,"erpCode":"100485","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州市九惠制药股份有限公司","memoryCode":"GMLKL","name":"感冒灵颗粒_999_10g*9袋_惠州九惠","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"purchaseUpperLimit":9999,"specValue":"10g*9袋","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041303","A041303003"],"wareStock":92},{"brandName":"惠","drugType":0,"erpCode":"131452","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州大亚制药股份有限公司","name":"清凉喉片_惠_16片","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"16片","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100},{"brandName":"天天乐","drugType":0,"erpCode":"146949","isColdChain":false,"isOfficial":false,"manufacture":"广西天天乐药业股份有限公司(原：广西天天乐药业有限公司）","name":"氨咖黄敏胶囊(速效伤风胶囊)_天天乐_10粒","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10粒","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":99}],"msg":"操作成功","timestamp":1747737290715}
[2025-05-20 18:34:50.539] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["100485","131452","146949"],"storeCode":"JM0003"}
[2025-05-20 18:34:50.553] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"100485","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.000000,"stock":2,"storeCode":"JM0003","wareStock":92},{"erpCode":"131452","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.000000,"stock":16,"storeCode":"JM0003","wareStock":100},{"erpCode":"146949","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.600000,"storeCode":"JM0003","wareStock":99}],"msg":"操作成功","timestamp":1747737290729}
[2025-05-20 18:34:50.553] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:34:50.553] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:34:50.558] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0003000011，明细数量：3
[2025-05-20 18:34:56.073] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler.doHandleReal:39] 采购单分组完成，共生成1个采购单
[2025-05-20 18:34:56.073] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 执行完成... 
[2025-05-20 18:34:56.073] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 开始执行...
[2025-05-20 18:35:17.354] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.doHandleReal:68] 提单失败，订单号PurchaseOrderNo(purchaseOrderNo=PO20250520JM0003000011) java.lang.NullPointerException: null
	at com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.settlement(SubmitPayOrderHandler.java:106) ~[classes/:?]
	at com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.doHandleReal(SubmitPayOrderHandler.java:62) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:26) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.service.impl.PurchaseOrderDomainServiceImpl.createPurchaseOrder(PurchaseOrderDomainServiceImpl.java:56) ~[classes/:?]
	at com.yxt.purchase.application.service.impl.PurchaseOrderApplicationServiceImpl.importPurchaseOrder(PurchaseOrderApplicationServiceImpl.java:49) ~[classes/:?]
	at com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder(PurchaseOrderController.java:129) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at com.yxt.starter.filter.TraceIdFilter.doFilter(TraceIdFilter.java:60) ~[yxt-core-spring-boot-starter-4.6.4.jar:4.6.4]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at cn.hydee.starter.grey.springboot.web.GreyWebInterceptor.doFilter(GreyWebInterceptor.java:62) ~[grey-spring-boot-web-starter-2.0.0-20230815.020948-3.jar:2.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:836) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1747) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_202]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]

[2025-05-20 18:35:17.354] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 执行完成... 
[2025-05-20 18:35:17.354] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 开始执行...
[2025-05-20 18:35:17.365] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 执行完成... 
[2025-05-20 18:35:17.365] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:35:18.174] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:35:18.174] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:35:18.199] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:35:18.199] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:35:18.199] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["106632","130969","765678"],"storeCode":"JM0004"}
[2025-05-20 18:35:18.293] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"可孚","drugType":3,"erpCode":"106632","isColdChain":false,"isOfficial":false,"mainPic":"https://shop-cdn.yxtmart.cn/shop/101-100/106632/106632-2.jpg","manufacture":"可孚医疗科技股份有限公司000","name":"可孚医用棉签(灭菌)_10cm*50支","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10CM*50支","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":987},{"brandName":"欧意","drugType":0,"erpCode":"130969","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"石药集团欧意药业有限公司","name":"奥美拉唑肠溶胶囊_欧意_20mg*16粒*1瓶_OTC","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"20MG*16粒*1瓶","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100}],"msg":"操作成功","timestamp":1747737318470}
[2025-05-20 18:35:18.293] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["106632","130969"],"storeCode":"JM0004"}
[2025-05-20 18:35:18.308] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"106632","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.500000,"stock":43,"storeCode":"JM0004","wareStock":987},{"erpCode":"130969","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":26.500000,"storeCode":"JM0004","wareStock":100}],"msg":"操作成功","timestamp":1747737318486}
[2025-05-20 18:35:18.308] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:35:18.308] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:35:18.311] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0004000009，明细数量：3
[2025-05-20 18:35:19.053] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler.doHandleReal:39] 采购单分组完成，共生成1个采购单
[2025-05-20 18:35:19.053] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 执行完成... 
[2025-05-20 18:35:19.053] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 开始执行...
[2025-05-20 18:35:19.799] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 执行完成... 
[2025-05-20 18:35:19.799] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 开始执行...
[2025-05-20 18:35:19.805] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 执行完成... 
[2025-05-20 18:35:22.019] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:35:47.042] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:35:47.043] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:35:47.077] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:35:47.077] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:35:47.077] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["131452","100485","146949"],"storeCode":"JM0003"}
[2025-05-20 18:35:47.124] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"999","drugType":0,"erpCode":"100485","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州市九惠制药股份有限公司","memoryCode":"GMLKL","name":"感冒灵颗粒_999_10g*9袋_惠州九惠","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"purchaseUpperLimit":9999,"specValue":"10g*9袋","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041303","A041303003"],"wareStock":92},{"brandName":"惠","drugType":0,"erpCode":"131452","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州大亚制药股份有限公司","name":"清凉喉片_惠_16片","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"16片","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100},{"brandName":"天天乐","drugType":0,"erpCode":"146949","isColdChain":false,"isOfficial":false,"manufacture":"广西天天乐药业股份有限公司(原：广西天天乐药业有限公司）","name":"氨咖黄敏胶囊(速效伤风胶囊)_天天乐_10粒","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10粒","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":99}],"msg":"操作成功","timestamp":1747737347299}
[2025-05-20 18:35:47.126] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["100485","131452","146949"],"storeCode":"JM0003"}
[2025-05-20 18:35:47.169] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"100485","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.000000,"stock":2,"storeCode":"JM0003","wareStock":92},{"erpCode":"131452","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.000000,"stock":16,"storeCode":"JM0003","wareStock":100},{"erpCode":"146949","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.600000,"storeCode":"JM0003","wareStock":99}],"msg":"操作成功","timestamp":1747737347343}
[2025-05-20 18:35:47.174] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:35:47.175] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:35:47.211] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0003000012，明细数量：3
[2025-05-20 18:37:07.596] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 18:37:08.678] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 18:37:08.994] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 18:37:15.124] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 18:37:17.309] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 18:37:17.339] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 18:37:17.375] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 18:37:17.387] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 18:37:19.326] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:37:19.340] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:37:19.349] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:37:19.368] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:37:19.382] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:37:19.471] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 18:37:19.492] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 18:37:19.562] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 18:37:20.131] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 18:37:20.379] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 19.5 seconds (JVM running for 22.117)
[2025-05-20 18:37:20.380] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 18:37:20.380] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 18:37:20.409] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 18:37:21.291] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:37:21.294] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:37:21.296] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:37:21.299] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:37:21.307] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:38:10.731] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:38:15.788] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:38:15.788] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:38:16.324] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:38:16.324] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:38:16.384] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["131452","100485","146949"],"storeCode":"JM0003"}
[2025-05-20 18:38:16.503] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"999","drugType":0,"erpCode":"100485","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州市九惠制药股份有限公司","memoryCode":"GMLKL","name":"感冒灵颗粒_999_10g*9袋_惠州九惠","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"purchaseUpperLimit":9999,"specValue":"10g*9袋","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041303","A041303003"],"wareStock":92},{"brandName":"惠","drugType":0,"erpCode":"131452","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州大亚制药股份有限公司","name":"清凉喉片_惠_16片","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"16片","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100},{"brandName":"天天乐","drugType":0,"erpCode":"146949","isColdChain":false,"isOfficial":false,"manufacture":"广西天天乐药业股份有限公司(原：广西天天乐药业有限公司）","name":"氨咖黄敏胶囊(速效伤风胶囊)_天天乐_10粒","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10粒","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":99}],"msg":"操作成功","timestamp":1747737496660}
[2025-05-20 18:38:16.506] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["100485","131452","146949"],"storeCode":"JM0003"}
[2025-05-20 18:38:16.542] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"100485","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.000000,"stock":2,"storeCode":"JM0003","wareStock":92},{"erpCode":"131452","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.000000,"stock":16,"storeCode":"JM0003","wareStock":100},{"erpCode":"146949","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.600000,"storeCode":"JM0003","wareStock":99}],"msg":"操作成功","timestamp":1747737496718}
[2025-05-20 18:38:16.551] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:38:16.551] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:38:16.616] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0003000013，明细数量：3
[2025-05-20 18:38:20.201] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler.doHandleReal:39] 采购单分组完成，共生成1个采购单
[2025-05-20 18:38:20.201] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 执行完成... 
[2025-05-20 18:38:20.202] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 开始执行...
[2025-05-20 18:38:43.629] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.settlement:116] 调用交易结算接口,request:{"commodity":[{"count":3,"erpCode":"131452"},{"count":8,"erpCode":"100485"},{"count":12,"erpCode":"146949"}],"receiverInfo":{"address":"海南省儋州市那大镇城西片区控规E0401地块#（幢）1层4-103商铺","area":"那大镇","city":"儋州市","province":"海南省","receiverName":"儋州一心堂雅拉路壹号药店（个人独资）","receiverPhone":"13976445102"},"settlementType":"AUTO_PAY","systemCode":"PURCHASE_ORDER","tradeMode":"JOIN_B2B","userId":"4089306587993216930","userOrgCode":"JM0003"},result:{"code":"10000","data":{"commodity":[{"brandName":"惠","cartGroupTags":["COMMON"],"commodityName":"清凉喉片_惠_16片","commoditySpec":"16片","count":3,"drugType":0,"erpCode":"131452","mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州大亚制药股份有限公司","price":5.000000,"stockCount":"100"},{"brandName":"999","cartGroupTags":["COMMON"],"commodityName":"感冒灵颗粒_999_10g*9袋_惠州九惠","commoditySpec":"10g*9袋","count":8,"drugType":0,"erpCode":"100485","mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州市九惠制药股份有限公司","price":5.000000,"stockCount":"92"},{"brandName":"天天乐","cartGroupTags":["COMMON"],"commodityName":"氨咖黄敏胶囊(速效伤风胶囊)_天天乐_10粒","commoditySpec":"10粒","count":12,"drugType":0,"erpCode":"146949","manufacture":"广西天天乐药业股份有限公司(原：广西天天乐药业有限公司）","price":5.000000,"stockCount":"99"}],"settlementAmountInfo":{"activityAmount":0,"actualPayableAmount":115.000000,"commodityTotalAmount":115.000000,"couponAmount":0,"discountAmount":0,"freightAmount":0,"payableAmount":115.000000},"settlementId":"1924776764208447489"},"msg":"操作成功","timestamp":1747737515960}
[2025-05-20 18:39:32.420] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.submitOrder:173] 调用交易提单接口,request:{"extraParams":{"orderType":"purchaseOrder","orderNo":{"purchaseOrderNo":"PO20250520JM0003000013"}},"payDeductionTime":1748169548884,"settlementId":"1924776764208447489"},result:{"code":"20001","msg":"用户所属组织编码","timestamp":1747737551206}
[2025-05-20 18:39:32.429] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.doHandleReal:68] 提单失败，订单号PurchaseOrderNo(purchaseOrderNo=PO20250520JM0003000013) com.yxt.lang.exception.YxtBizException: 用户所属组织编码
	at com.yxt.lang.exception.YxtBizException$YxtBizExceptionBuilder.build(YxtBizException.java:29) ~[yxt-common-lang-4.10.3.jar:4.10.3]
	at com.yxt.purchase.utils.ResponseUtils.checkRespSuccess(ResponseUtils.java:9) ~[classes/:?]
	at com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.submitOrder(SubmitPayOrderHandler.java:175) ~[classes/:?]
	at com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.doHandleReal(SubmitPayOrderHandler.java:64) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:26) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.service.impl.PurchaseOrderDomainServiceImpl.createPurchaseOrder(PurchaseOrderDomainServiceImpl.java:56) ~[classes/:?]
	at com.yxt.purchase.application.service.impl.PurchaseOrderApplicationServiceImpl.importPurchaseOrder(PurchaseOrderApplicationServiceImpl.java:49) ~[classes/:?]
	at com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder(PurchaseOrderController.java:129) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at com.yxt.starter.filter.TraceIdFilter.doFilter(TraceIdFilter.java:60) ~[yxt-core-spring-boot-starter-4.6.4.jar:4.6.4]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at cn.hydee.starter.grey.springboot.web.GreyWebInterceptor.doFilter(GreyWebInterceptor.java:62) ~[grey-spring-boot-web-starter-2.0.0-20230815.020948-3.jar:2.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:836) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1747) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_202]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]

[2025-05-20 18:39:32.429] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 执行完成... 
[2025-05-20 18:39:32.430] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 开始执行...
[2025-05-20 18:41:41.569] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-20 18:41:42.562] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-20 18:41:42.891] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-20 18:41:48.995] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-20 18:41:50.621] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-20 18:41:50.658] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-20 18:41:50.711] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 18:41:50.727] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-20 18:41:52.390] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:41:52.421] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:41:52.435] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:41:52.455] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:41:52.471] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:41:52.559] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-20 18:41:52.580] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-20 18:41:52.665] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-20 18:41:53.221] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-20 18:41:53.501] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 18.482 seconds (JVM running for 21.163)
[2025-05-20 18:41:53.501] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-20 18:41:53.501] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-20 18:41:53.530] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-20 18:41:54.024] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:41:54.026] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:41:54.028] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:41:54.031] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:41:54.033] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-20 18:42:12.911] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:42:14.598] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:42:14.598] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:42:15.324] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:42:15.324] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:42:15.388] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["131452","100485","146949"],"storeCode":"JM0003"}
[2025-05-20 18:42:15.509] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"999","drugType":0,"erpCode":"100485","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州市九惠制药股份有限公司","memoryCode":"GMLKL","name":"感冒灵颗粒_999_10g*9袋_惠州九惠","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"purchaseUpperLimit":9999,"specValue":"10g*9袋","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041303","A041303003"],"wareStock":92},{"brandName":"惠","drugType":0,"erpCode":"131452","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州大亚制药股份有限公司","name":"清凉喉片_惠_16片","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"16片","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100},{"brandName":"天天乐","drugType":0,"erpCode":"146949","isColdChain":false,"isOfficial":false,"manufacture":"广西天天乐药业股份有限公司(原：广西天天乐药业有限公司）","name":"氨咖黄敏胶囊(速效伤风胶囊)_天天乐_10粒","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10粒","status":1,"storeCode":"JM0003","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":99}],"msg":"操作成功","timestamp":1747737735675}
[2025-05-20 18:42:15.511] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["100485","131452","146949"],"storeCode":"JM0003"}
[2025-05-20 18:42:15.554] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"100485","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.000000,"stock":2,"storeCode":"JM0003","wareStock":92},{"erpCode":"131452","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.000000,"stock":16,"storeCode":"JM0003","wareStock":100},{"erpCode":"146949","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":0.600000,"storeCode":"JM0003","wareStock":99}],"msg":"操作成功","timestamp":1747737735739}
[2025-05-20 18:42:15.560] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:42:15.560] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:42:15.621] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0003000014，明细数量：3
[2025-05-20 18:42:16.037] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler.doHandleReal:39] 采购单分组完成，共生成1个采购单
[2025-05-20 18:42:16.037] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 执行完成... 
[2025-05-20 18:42:16.037] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 开始执行...
[2025-05-20 18:42:20.026] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.settlement:118] 调用交易结算接口,request:{"commodity":[{"count":3,"erpCode":"131452"},{"count":8,"erpCode":"100485"},{"count":12,"erpCode":"146949"}],"receiverInfo":{"address":"海南省儋州市那大镇城西片区控规E0401地块#（幢）1层4-103商铺","area":"那大镇","city":"儋州市","province":"海南省","receiverName":"儋州一心堂雅拉路壹号药店（个人独资）","receiverPhone":"13976445102"},"settlementType":"AUTO_PAY","systemCode":"PURCHASE_ORDER","tradeMode":"JOIN_B2B","userId":"4089306587993216930","userOrgCode":"JM0003"},result:{"code":"10000","data":{"commodity":[{"brandName":"惠","cartGroupTags":["COMMON"],"commodityName":"清凉喉片_惠_16片","commoditySpec":"16片","count":3,"drugType":0,"erpCode":"131452","mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州大亚制药股份有限公司","price":5.000000,"stockCount":"100"},{"brandName":"999","cartGroupTags":["COMMON"],"commodityName":"感冒灵颗粒_999_10g*9袋_惠州九惠","commoditySpec":"10g*9袋","count":8,"drugType":0,"erpCode":"100485","mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"惠州市九惠制药股份有限公司","price":5.000000,"stockCount":"92"},{"brandName":"天天乐","cartGroupTags":["COMMON"],"commodityName":"氨咖黄敏胶囊(速效伤风胶囊)_天天乐_10粒","commoditySpec":"10粒","count":12,"drugType":0,"erpCode":"146949","manufacture":"广西天天乐药业股份有限公司(原：广西天天乐药业有限公司）","price":5.000000,"stockCount":"99"}],"settlementAmountInfo":{"activityAmount":0,"actualPayableAmount":115.000000,"commodityTotalAmount":115.000000,"couponAmount":0,"discountAmount":0,"freightAmount":0,"payableAmount":115.000000},"settlementId":"1924777705041166337"},"msg":"操作成功","timestamp":1747737740186}
[2025-05-20 18:42:26.489] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.submitOrder:180] 调用交易提单接口,request:{"extraParams":{"orderType":"purchaseOrder","orderNo":{"purchaseOrderNo":"PO20250520JM0003000014"}},"payDeductionTime":1748169740026,"settlementId":"1924777705041166337","systemCode":"PURCHASE_ORDER","tradeMode":"JOIN_B2B","userId":"4089306587993216930","userOrgCode":"JM0003"},result:{"code":"50001","msg":"不支持的支付业务类型","timestamp":1747737746681}
[2025-05-20 18:42:26.494] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.doHandleReal:70] 提单失败，订单号PurchaseOrderNo(purchaseOrderNo=PO20250520JM0003000014) com.yxt.lang.exception.YxtBizException: 不支持的支付业务类型
	at com.yxt.lang.exception.YxtBizException$YxtBizExceptionBuilder.build(YxtBizException.java:29) ~[yxt-common-lang-4.10.3.jar:4.10.3]
	at com.yxt.purchase.utils.ResponseUtils.checkRespSuccess(ResponseUtils.java:9) ~[classes/:?]
	at com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.submitOrder(SubmitPayOrderHandler.java:182) ~[classes/:?]
	at com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler.doHandleReal(SubmitPayOrderHandler.java:65) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:26) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute(AbstractPurchaseOrderHandler.java:30) ~[classes/:?]
	at com.yxt.purchase.domain.service.impl.PurchaseOrderDomainServiceImpl.createPurchaseOrder(PurchaseOrderDomainServiceImpl.java:56) ~[classes/:?]
	at com.yxt.purchase.application.service.impl.PurchaseOrderApplicationServiceImpl.importPurchaseOrder(PurchaseOrderApplicationServiceImpl.java:49) ~[classes/:?]
	at com.yxt.purchase.interfaces.PurchaseOrderController.importPurchaseOrder(PurchaseOrderController.java:129) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at com.yxt.starter.filter.TraceIdFilter.doFilter(TraceIdFilter.java:60) ~[yxt-core-spring-boot-starter-4.6.4.jar:4.6.4]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at cn.hydee.starter.grey.springboot.web.GreyWebInterceptor.doFilter(GreyWebInterceptor.java:62) ~[grey-spring-boot-web-starter-2.0.0-20230815.020948-3.jar:2.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) ~[spring-boot-actuator-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:836) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1747) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_202]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.19.jar:9.0.19]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]

[2025-05-20 18:42:26.494] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 执行完成... 
[2025-05-20 18:42:26.495] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 开始执行...
[2025-05-20 18:42:26.622] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 执行完成... 
[2025-05-20 18:42:26.622] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 开始执行...
[2025-05-20 18:42:26.664] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.InitDataHandler 执行完成... 
[2025-05-20 18:42:26.664] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 开始执行...
[2025-05-20 18:42:27.560] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UserPermissionCheckHandler 执行完成... 
[2025-05-20 18:42:27.560] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 开始执行...
[2025-05-20 18:42:27.560] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:71] 查询门店商品参数：{"erpCodeList":["106632","130969","765678"],"storeCode":"JM0004"}
[2025-05-20 18:42:27.592] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getB2bStoreSpecBatchQueryRespList:74] 查询门店商品结果：{"code":"10000","data":[{"brandName":"可孚","drugType":3,"erpCode":"106632","isColdChain":false,"isOfficial":false,"mainPic":"https://shop-cdn.yxtmart.cn/shop/101-100/106632/106632-2.jpg","manufacture":"可孚医疗科技股份有限公司000","name":"可孚医用棉签(灭菌)_10cm*50支","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"10CM*50支","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":987},{"brandName":"欧意","drugType":0,"erpCode":"130969","isColdChain":false,"isOfficial":false,"mainPic":"https://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/img/20250312/d5286c6d146a42f882a4c3566dad0099.jpg","manufacture":"石药集团欧意药业有限公司","name":"奥美拉唑肠溶胶囊_欧意_20mg*16粒*1瓶_OTC","purchaseBundleDesc":"分公司级舍入参数对象为空","purchaseBundleSize":1,"purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"specValue":"20MG*16粒*1瓶","status":1,"storeCode":"JM0004","typeCodes":["A","A04","A0413","A041304","A041304002"],"wareStock":100}],"msg":"操作成功","timestamp":1747737747787}
[2025-05-20 18:42:27.592] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:116] 查询门店商品库存及价格参数：{"erpCodeList":["106632","130969"],"storeCode":"JM0004"}
[2025-05-20 18:42:27.734] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.api.MerchandiseServiceImpl.getStockAndPriceRespList:118] 查询门店商品库存及价格返回值：{"code":"10000","data":[{"erpCode":"106632","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":2.500000,"stock":43,"storeCode":"JM0004","wareStock":987},{"erpCode":"130969","purchasePrice":5.000000,"purchasePriceEndTime":4102358400000,"purchasePriceStartTime":1659571200000,"retailPrice":26.500000,"storeCode":"JM0004","wareStock":100}],"msg":"操作成功","timestamp":1747737747929}
[2025-05-20 18:42:27.735] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.CommodityCheckHandler 执行完成... 
[2025-05-20 18:42:27.735] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 开始执行...
[2025-05-20 18:42:27.742] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.repository.PurchaseOrderRepositoryImpl.savePurchaseOrder:54] 开始保存采购单明细，采购单号：PO20250520JM0004000010，明细数量：3
[2025-05-20 18:42:27.753] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler.doHandleReal:39] 采购单分组完成，共生成1个采购单
[2025-05-20 18:42:27.753] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SavePurchaseOrderHandler 执行完成... 
[2025-05-20 18:42:27.753] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 开始执行...
[2025-05-20 18:42:48.198] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.SubmitPayOrderHandler 执行完成... 
[2025-05-20 18:42:48.198] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:25] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 开始执行...
[2025-05-20 18:42:48.205] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.domain.handler.AbstractPurchaseOrderHandler.execute:27] 处理器 com.yxt.purchase.infrastructure.handlers.process.UpdatePurchaseOrderStatusHandler 执行完成... 
