[2025-05-21 13:41:47.161] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-21 13:41:48.252] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-21 13:41:48.623] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-21 13:41:54.781] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-21 13:41:57.191] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-21 13:41:57.217] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-21 13:41:57.252] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-21 13:41:57.263] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-21 13:41:58.827] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-21 13:41:58.842] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-21 13:41:58.853] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-21 13:41:58.873] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-21 13:41:58.892] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-21 13:41:58.976] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-21 13:41:58.997] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-21 13:41:59.062] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-21 13:41:59.595] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-21 13:41:59.871] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 19.471 seconds (JVM running for 23.637)
[2025-05-21 13:41:59.871] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-21 13:41:59.872] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-21 13:41:59.900] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 40][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-21 13:42:00.230] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-21 13:42:00.245] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-21 13:42:00.248] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-21 13:42:00.250] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-21 13:42:00.252] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 40][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
