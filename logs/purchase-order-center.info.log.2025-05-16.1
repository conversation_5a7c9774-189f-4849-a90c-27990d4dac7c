[2025-05-16 11:39:02.797] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-16 11:39:04.257] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-16 11:39:04.688] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-16 11:39:11.047] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 11:39:13.578] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-16 11:39:13.623] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-16 11:39:13.666] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-16 11:39:13.681] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-16 11:39:15.595] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 11:39:15.608] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 11:39:15.617] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 11:39:15.633] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 11:39:15.650] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 11:39:15.739] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-16 11:39:15.763] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-16 11:39:15.855] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-16 11:39:16.568] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-16 11:39:16.987] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 21.394 seconds (JVM running for 25.445)
[2025-05-16 11:39:16.988] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-16 11:39:16.988] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-16 11:39:17.053] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 42][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-16 11:39:17.973] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 11:39:17.979] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 11:39:17.982] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 11:39:17.986] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 11:39:17.989] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 42][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:22:01.030] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-16 14:22:02.229] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-16 14:22:02.695] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-16 14:22:09.302] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:22:11.498] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext.refresh:557] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'documentationPluginsBootstrapper' defined in URL [jar:file:/D:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/DocumentationPluginsBootstrapper.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcRequestHandlerProvider' defined in URL [jar:file:/D:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/WebMvcRequestHandlerProvider.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'purchaseOrderController' method 
public com.yxt.lang.dto.api.ResponseBase<java.util.List<java.lang.String>> com.yxt.purchase.interfaces.PurchaseOrderController.batchConfirmPurchaseOrder(java.lang.String,com.yxt.purchase.open.sdk.req.BatchConfirmRequest)
to {POST /1.0/purchase-order/batch-confirm}: There is already 'purchaseOrderController' bean method
public com.yxt.lang.dto.api.ResponseBase<java.util.List<java.lang.String>> com.yxt.purchase.interfaces.PurchaseOrderController.batchReCreatePurchaseOrder(java.lang.String,com.yxt.purchase.open.sdk.req.BatchConfirmRequest) mapped.
[2025-05-16 14:22:12.154] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.apache.catalina.loader.WebappClassLoaderBase.log:173] The web application [ROOT] appears to have started a thread named [arthas-timer] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.lang.Object.wait(Native Method)
 java.lang.Object.wait(Object.java:502)
 java.util.TimerThread.mainLoop(Timer.java:526)
 java.util.TimerThread.run(Timer.java:505)
[2025-05-16 14:22:12.154] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.apache.catalina.loader.WebappClassLoaderBase.log:173] The web application [ROOT] appears to have started a thread named [arthas-NettyHttpTelnetBootstrap-3-1] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.nio.ch.WindowsSelectorImpl$SubSelector.poll0(Native Method)
 sun.nio.ch.WindowsSelectorImpl$SubSelector.poll(WindowsSelectorImpl.java:296)
 sun.nio.ch.WindowsSelectorImpl$SubSelector.access$400(WindowsSelectorImpl.java:278)
 sun.nio.ch.WindowsSelectorImpl.doSelect(WindowsSelectorImpl.java:159)
 sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:86)
 sun.nio.ch.SelectorImpl.select(SelectorImpl.java:97)
 sun.nio.ch.SelectorImpl.select(SelectorImpl.java:101)
 com.alibaba.arthas.deps.io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 com.alibaba.arthas.deps.io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:803)
 com.alibaba.arthas.deps.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:457)
 com.alibaba.arthas.deps.io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
 com.alibaba.arthas.deps.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 com.alibaba.arthas.deps.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.lang.Thread.run(Thread.java:748)
[2025-05-16 14:22:12.155] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.apache.catalina.loader.WebappClassLoaderBase.log:173] The web application [ROOT] appears to have started a thread named [arthas-NettyWebsocketTtyBootstrap-4-1] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.nio.ch.WindowsSelectorImpl$SubSelector.poll0(Native Method)
 sun.nio.ch.WindowsSelectorImpl$SubSelector.poll(WindowsSelectorImpl.java:296)
 sun.nio.ch.WindowsSelectorImpl$SubSelector.access$400(WindowsSelectorImpl.java:278)
 sun.nio.ch.WindowsSelectorImpl.doSelect(WindowsSelectorImpl.java:159)
 sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:86)
 sun.nio.ch.SelectorImpl.select(SelectorImpl.java:97)
 sun.nio.ch.SelectorImpl.select(SelectorImpl.java:101)
 com.alibaba.arthas.deps.io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
 com.alibaba.arthas.deps.io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:803)
 com.alibaba.arthas.deps.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:457)
 com.alibaba.arthas.deps.io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
 com.alibaba.arthas.deps.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
 com.alibaba.arthas.deps.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.lang.Thread.run(Thread.java:748)
[2025-05-16 14:22:12.155] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.apache.catalina.loader.WebappClassLoaderBase.log:173] The web application [ROOT] appears to have started a thread named [arthas-shell-server] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)
[2025-05-16 14:22:12.156] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.apache.catalina.loader.WebappClassLoaderBase.log:173] The web application [ROOT] appears to have started a thread named [arthas-shell-server] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)
[2025-05-16 14:22:12.156] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.apache.catalina.loader.WebappClassLoaderBase.log:173] The web application [ROOT] appears to have started a thread named [arthas-UserStat] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2039)
 java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)
[2025-05-16 14:22:12.157] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.apache.catalina.loader.WebappClassLoaderBase.log:173] The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.naming.client.listener] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
 java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
 com.alibaba.nacos.client.naming.core.EventDispatcher$Notifier.run(EventDispatcher.java:163)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)
[2025-05-16 14:22:12.157] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.apache.catalina.loader.WebappClassLoaderBase.log:173] The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.client.naming.updater] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2039)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1088)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)
[2025-05-16 14:22:12.157] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.apache.catalina.loader.WebappClassLoaderBase.log:173] The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.client.naming.updater] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)
[2025-05-16 14:22:12.158] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.apache.catalina.loader.WebappClassLoaderBase.log:173] The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.naming.failover] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)
[2025-05-16 14:22:12.158] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.apache.catalina.loader.WebappClassLoaderBase.log:173] The web application [ROOT] appears to have started a thread named [com.alibaba.nacos.naming.push.receiver] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.net.DualStackPlainDatagramSocketImpl.socketReceiveOrPeekData(Native Method)
 java.net.DualStackPlainDatagramSocketImpl.receive0(DualStackPlainDatagramSocketImpl.java:124)
 java.net.AbstractPlainDatagramSocketImpl.receive(AbstractPlainDatagramSocketImpl.java:143)
 java.net.DatagramSocket.receive(DatagramSocket.java:812)
 com.alibaba.nacos.client.naming.core.PushReceiver.run(PushReceiver.java:83)
 java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
 java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
 java.util.concurrent.FutureTask.run(FutureTask.java)
 java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
 java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)
[2025-05-16 14:22:12.208] | [traceId:N/A] |[ERROR]| purchase-order-center | [thread: 41][org.springframework.boot.SpringApplication.reportFailure:858] Application run failed org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'documentationPluginsBootstrapper' defined in URL [jar:file:/D:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/DocumentationPluginsBootstrapper.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcRequestHandlerProvider' defined in URL [jar:file:/D:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/WebMvcRequestHandlerProvider.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'purchaseOrderController' method 
public com.yxt.lang.dto.api.ResponseBase<java.util.List<java.lang.String>> com.yxt.purchase.interfaces.PurchaseOrderController.batchConfirmPurchaseOrder(java.lang.String,com.yxt.purchase.open.sdk.req.BatchConfirmRequest)
to {POST /1.0/purchase-order/batch-confirm}: There is already 'purchaseOrderController' bean method
public com.yxt.lang.dto.api.ResponseBase<java.util.List<java.lang.String>> com.yxt.purchase.interfaces.PurchaseOrderController.batchReCreatePurchaseOrder(java.lang.String,com.yxt.purchase.open.sdk.req.BatchConfirmRequest) mapped.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:769) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:218) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1341) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1187) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:555) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:843) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:877) ~[spring-context-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549) ~[spring-context-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:142) ~[spring-boot-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775) ~[spring-boot-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397) ~[spring-boot-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316) ~[spring-boot-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260) ~[spring-boot-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248) ~[spring-boot-2.1.5.RELEASE.jar:2.1.5.RELEASE]
	at com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main(PurchaseOrderServiceBootstrap.java:30) ~[classes/:?]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcRequestHandlerProvider' defined in URL [jar:file:/D:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/WebMvcRequestHandlerProvider.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'purchaseOrderController' method 
public com.yxt.lang.dto.api.ResponseBase<java.util.List<java.lang.String>> com.yxt.purchase.interfaces.PurchaseOrderController.batchConfirmPurchaseOrder(java.lang.String,com.yxt.purchase.open.sdk.req.BatchConfirmRequest)
to {POST /1.0/purchase-order/batch-confirm}: There is already 'purchaseOrderController' bean method
public com.yxt.lang.dto.api.ResponseBase<java.util.List<java.lang.String>> com.yxt.purchase.interfaces.PurchaseOrderController.batchReCreatePurchaseOrder(java.lang.String,com.yxt.purchase.open.sdk.req.BatchConfirmRequest) mapped.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:769) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:218) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1341) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1187) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:555) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1464) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1428) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1319) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1206) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1168) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:857) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:760) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	... 19 more
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'purchaseOrderController' method 
public com.yxt.lang.dto.api.ResponseBase<java.util.List<java.lang.String>> com.yxt.purchase.interfaces.PurchaseOrderController.batchConfirmPurchaseOrder(java.lang.String,com.yxt.purchase.open.sdk.req.BatchConfirmRequest)
to {POST /1.0/purchase-order/batch-confirm}: There is already 'purchaseOrderController' bean method
public com.yxt.lang.dto.api.ResponseBase<java.util.List<java.lang.String>> com.yxt.purchase.interfaces.PurchaseOrderController.batchReCreatePurchaseOrder(java.lang.String,com.yxt.purchase.open.sdk.req.BatchConfirmRequest) mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1778) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1464) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1428) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1319) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1206) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1168) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:857) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:760) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:218) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1341) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1187) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:555) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1464) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1428) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1319) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1206) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1168) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:857) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:760) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	... 19 more
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'purchaseOrderController' method 
public com.yxt.lang.dto.api.ResponseBase<java.util.List<java.lang.String>> com.yxt.purchase.interfaces.PurchaseOrderController.batchConfirmPurchaseOrder(java.lang.String,com.yxt.purchase.open.sdk.req.BatchConfirmRequest)
to {POST /1.0/purchase-order/batch-confirm}: There is already 'purchaseOrderController' bean method
public com.yxt.lang.dto.api.ResponseBase<java.util.List<java.lang.String>> com.yxt.purchase.interfaces.PurchaseOrderController.batchReCreatePurchaseOrder(java.lang.String,com.yxt.purchase.open.sdk.req.BatchConfirmRequest) mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.assertUniqueMethodMapping(AbstractHandlerMethodMapping.java:618) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:586) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:312) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:282) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:1.8.0_202]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:280) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:252) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:211) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:199) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:164) ~[spring-webmvc-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1837) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1774) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1464) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1428) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1319) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1206) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1168) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:857) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:760) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:218) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1341) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1187) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:555) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:515) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:320) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:318) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:277) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1464) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1428) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1319) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1206) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1168) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:857) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:760) ~[spring-beans-5.1.7.RELEASE.jar:5.1.7.RELEASE]
	... 19 more

[2025-05-16 14:22:14.253] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:22:19.249] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:22:24.254] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:22:29.252] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:22:34.252] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:22:39.255] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:22:44.254] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:22:49.246] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:22:54.246] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:22:59.246] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:23:04.246] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:23:09.258] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:23:14.247] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:23:19.246] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:23:24.246] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:23:29.247] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:23:34.246] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:23:39.246] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:23:44.246] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:23:49.248] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:23:54.253] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:23:59.251] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:24:04.248] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:24:09.261] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:24:14.248] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:24:19.260] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:24:24.255] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:24:59.038] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-16 14:25:00.109] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-16 14:25:00.439] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-16 14:25:06.645] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:25:08.501] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-16 14:25:08.533] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-16 14:25:08.571] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-16 14:25:08.584] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-16 14:25:10.218] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:25:10.237] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:25:10.250] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:25:10.271] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:25:10.291] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:25:10.368] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-16 14:25:10.393] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-16 14:25:10.469] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-16 14:25:11.041] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-16 14:25:11.329] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 19.027 seconds (JVM running for 21.971)
[2025-05-16 14:25:11.329] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-16 14:25:11.329] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-16 14:25:11.369] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-16 14:25:11.582] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:25:11.585] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:25:11.589] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:25:11.592] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:25:11.594] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:47:48.951] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStartupProfileInfo:679] The following profiles are active: test
[2025-05-16 14:47:50.049] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
[2025-05-16 14:47:50.411] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][org.springframework.boot.actuate.endpoint.EndpointId.logWarning:135] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2025-05-16 14:47:56.599] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.yxt.starter.sentinel.transtport.heartbeat.YxtHeartbeatMessage.setYxtAuthConfig:41] #31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null
[2025-05-16 14:47:58.677] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.starter.filter.ApolloConfigValidator.afterPropertiesSet:51] ApolloConfigValidator 耗时:0ms
[2025-05-16 14:47:58.714] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
[2025-05-16 14:47:58.750] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-16 14:47:58.765] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.netflix.config.sources.URLConfigurationSource.<init>:121] No URLs will be polled as dynamic configuration sources.
[2025-05-16 14:48:00.439] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:48:00.456] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:48:00.479] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:48:00.499] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:48:00.520] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:48:00.626] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:160] Context refreshed
[2025-05-16 14:48:00.651] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:163] Found 1 custom documentation plugin(s)
[2025-05-16 14:48:00.753] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:41] Scanning for api listing references
[2025-05-16 14:48:01.310] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:40] Generating unique operation named: queryStoreByOrgCodeUsingPOST_1
[2025-05-16 14:48:01.629] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.logStarted:59] Started PurchaseOrderServiceBootstrap in 19.467 seconds (JVM running for 23.296)
[2025-05-16 14:48:01.629] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:105] grey application started!
[2025-05-16 14:48:01.629] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][cn.hydee.starter.grey.springboot.lib.smooth.SmoothService.onApplicationEvent:107] grey registry success!
[2025-05-16 14:48:01.670] | [traceId:N/A] |[INFO ]| purchase-order-center | [thread: 41][com.yxt.purchase.bootstrap.PurchaseOrderServiceBootstrap.main:31] PurchaseOrderServiceBootstrap 服务启动成功
[2025-05-16 14:48:01.979] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:48:01.982] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:48:01.990] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:48:01.997] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:48:02.000] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.alibaba.cloud.sentinel.datasource.converter.SentinelConverter.convert:80] converter can not convert rules because source is empty
[2025-05-16 14:52:44.516] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.ctrip.framework.apollo.internals.RemoteConfigRepository.loadApolloConfig:194] Load config failed, will retry in 1 SECONDS. appId: purchase-order-center, cluster: default, namespaces: bizconfig
[2025-05-16 14:52:45.524] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26] Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: purchase-order-center, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/purchase-order-center/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: purchase-order-center, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
[2025-05-16 14:57:44.512] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.ctrip.framework.apollo.internals.RemoteConfigRepository.loadApolloConfig:194] Load config failed, will retry in 1 SECONDS. appId: purchase-order-center, cluster: default, namespaces: bizconfig
[2025-05-16 14:57:45.519] | [traceId:N/A] |[WARN ]| purchase-order-center | [thread: 41][com.ctrip.framework.apollo.internals.AbstractConfigRepository.trySync:26] Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Load Apollo Config failed - appId: purchase-order-center, cluster: default, namespace: bizconfig, url: http://**********:9100/configs/purchase-order-center/default/bizconfig?ip=********** [Cause: [status code: 404] Could not find config for namespace - appId: purchase-order-center, cluster: default, namespace: bizconfig, please check whether the configs are released in Apollo!]
