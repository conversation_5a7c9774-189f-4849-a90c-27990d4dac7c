package com.yxt.purchase.application.convert;

import com.yxt.purchase.application.entitys.PurchaseOrderEntity;
import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.open.sdk.resp.PurchaseOrderResult;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

/**
 * 采购单转换器
 */
@Component
public class PurchaseOrderAssembler {

  /**
   * 将聚合根转换为DTO
   */
  public PurchaseOrderEntity toDTO(PurchaseOrderAggregate aggregate) {
    if (aggregate == null) {
      return null;
    }

    PurchaseOrderEntity dto = new PurchaseOrderEntity();
    
    // 设置基本信息
    if (aggregate.getPurchaseOrderNo() != null) {
      dto.setPurchaseOrderNo(aggregate.getPurchaseOrderNo().getPurchaseOrderNo());
    }
    
    if (aggregate.getState() != null) {
      dto.setState(aggregate.getState().name());
    }
    
    dto.setRelatedOrderNo(aggregate.getRelatedOrderNo());
    dto.setParentPurchaseOrderNo(aggregate.getParentPurchaseOrderNo());
    dto.setAutoPayment(aggregate.getAutoPayment());
    dto.setOverduePaymentTime(aggregate.getOverduePaymentTime());
    dto.setCompanyCode(aggregate.getCompanyCode());
    dto.setCompanyName(aggregate.getCompanyName());
    dto.setOrganizationCode(aggregate.getOrganizationCode());
    dto.setOrganizationName(aggregate.getOrganizationName());
    
    if (aggregate.getPurchaseOrderLabel() != null) {
      dto.setPurchaseOrderLabel(aggregate.getPurchaseOrderLabel().name());
    }
    
    // 设置用户相关信息
    if (aggregate.getConfirmBy() != null) {
      dto.setConfirmBy(aggregate.getConfirmBy().getUserId());
      dto.setConfirmUserName(aggregate.getConfirmBy().getUserName());
      dto.setConfirmUserPhone(aggregate.getConfirmBy().getUserPhone());
    }
    
    if (aggregate.getCreatedBy() != null) {
      dto.setCreatedBy(aggregate.getCreatedBy().getUserId());
      dto.setCreatedUserName(aggregate.getCreatedBy().getUserName());
      dto.setCreatedUserPhone(aggregate.getCreatedBy().getUserPhone());
    }
    
    // 设置时间相关信息
    dto.setConfirmTime(aggregate.getConfirmTime());
    dto.setCreatedTime(aggregate.getCreatedTime());
    dto.setUpdatedTime(aggregate.getModifiedTime());
    
    return dto;
  }

  /**
   * 将聚合根列表转换为DTO列表
   */
  public List<PurchaseOrderEntity> toDTOList(List<PurchaseOrderAggregate> aggregates) {
    if (aggregates == null) {
      return null;
    }

    return aggregates.stream()
        .map(this::toDTO)
        .collect(Collectors.toList());
  }

  /**
   * 将DTO转换为响应对象
   */
  public PurchaseOrderResult toResult(PurchaseOrderEntity dto) {
    if (dto == null) {
      return null;
    }

    PurchaseOrderResult result = new PurchaseOrderResult();
    
    // 设置基本信息
    result.setPurchaseOrderNo(dto.getPurchaseOrderNo());
    result.setState(dto.getState());
    result.setRelatedOrderNo(dto.getRelatedOrderNo());
    result.setParentPurchaseOrderNo(dto.getParentPurchaseOrderNo());
    result.setAutoPayment(dto.getAutoPayment());
    result.setOverduePaymentTime(dto.getOverduePaymentTime());
    result.setCompanyCode(dto.getCompanyCode());
    result.setCompanyName(dto.getCompanyName());
    result.setOrganizationCode(dto.getOrganizationCode());
    result.setOrganizationName(dto.getOrganizationName());
    result.setPurchaseOrderLabel(dto.getPurchaseOrderLabel());
    
    // 设置用户相关信息
    result.setConfirmBy(dto.getConfirmUserName());
    result.setConfirmUserId(dto.getConfirmBy());
    result.setConfirmUserPhone(dto.getConfirmUserPhone());
    result.setCreatedBy(dto.getCreatedUserName());
    result.setCreatedUserId(dto.getCreatedBy());
    result.setCreatedUserPhone(dto.getCreatedUserPhone());
    
    // 设置时间相关信息
    if (dto.getConfirmTime() != null) {
      result.setConfirmTime(dto.getConfirmTime().toString());
    }
    if (dto.getCreatedTime() != null) {
      result.setCreatedTime(dto.getCreatedTime().toString());
    }
    if (dto.getUpdatedTime() != null) {
      result.setUpdatedTime(dto.getUpdatedTime().toString());
    }
    
    return result;
  }

  /**
   * 将DTO列表转换为响应对象列表
   */
  public List<PurchaseOrderResult> toResultList(List<PurchaseOrderEntity> dtos) {
    if (dtos == null) {
      return null;
    }

    return dtos.stream()
        .map(this::toResult)
        .collect(Collectors.toList());
  }
}
