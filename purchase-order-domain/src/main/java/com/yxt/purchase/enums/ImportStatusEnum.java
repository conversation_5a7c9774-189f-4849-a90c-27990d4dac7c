package com.yxt.purchase.enums;

/**
 * 导入状态枚举
 */
public enum ImportStatusEnum {
    
    /**
     * 处理中
     */
    PROCESSING(0, "处理中"),
    
    /**
     * 全部处理成功
     */
    SUCCESS(1, "全部处理成功"),
    
    /**
     * 处理部分异常
     */
    PARTIAL_FAILED(-1, "处理部分异常"),
    
    /**
     * 全部失败
     */
    FAILED(-2, "全部失败");
    
    private final Integer code;
    private final String desc;
    
    ImportStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static ImportStatusEnum getByCode(Integer code) {
        for (ImportStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
