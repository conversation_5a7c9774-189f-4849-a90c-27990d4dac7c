package com.yxt.purchase.enums;

/**
 * 异步任务状态枚举
 */
public enum AsyncTaskStatusEnum {
    
    /**
     * 待处理
     */
    PENDING("PENDING", "待处理"),
    
    /**
     * 处理中
     */
    PROCESSING("PROCESSING", "处理中"),
    
    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成"),
    
    /**
     * 失败
     */
    FAILED("FAILED", "失败");
    
    private final String code;
    private final String desc;
    
    AsyncTaskStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
}
