package com.yxt.purchase.domain.entity;

import com.yxt.purchase.domain.PurchaseOrderDetailAggregate;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 采购单明细查询结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrderDetailQueryResult {
    
    /**
     * 采购单明细聚合根列表
     */
    private List<PurchaseOrderDetailAggregate> purchaseOrderDetailAggregates;
    
    /**
     * 总记录数
     */
    private Long totalCount;
    
    /**
     * 总页数
     */
    private Long totalPage;
    
    /**
     * 当前页码
     */
    private Integer currentPage;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
}
