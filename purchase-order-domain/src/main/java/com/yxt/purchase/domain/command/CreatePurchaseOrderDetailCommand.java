package com.yxt.purchase.domain.command;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 创建采购商品明细命令
 */
@Data
public class CreatePurchaseOrderDetailCommand {
  /**
   * 采购订单编号
   */
  private String purchaseOrderNo;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品名称
   */
  private String erpName;

  /**
   * 商品规格
   */
  private String commoditySpec;

  /**
   * 生产商
   */
  private String manufacture;

  /**
   * 商品数量
   */
  private BigDecimal commodityCount;
}

