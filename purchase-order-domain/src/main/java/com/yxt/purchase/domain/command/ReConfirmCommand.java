package com.yxt.purchase.domain.command;

import com.yxt.purchase.domain.entity.PurchaseOrderDetailModel;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * 导入采购订单命令
 */
@Data
public class ReConfirmCommand {

  /**
   * 是否自动支付
   */
  private Boolean isAutoPayment;

  /**
   * 超时支付时间
   */
  private Integer overduePaymentTime;

  /**
   * 是否自动过审，true: 表示生成采购单后立即生成订单 false: 表示只生成采购单
   */
  private Boolean autoConfirm;

  /**
   * 用户ID
   */
  private String userId;

  private List<PurchaseOrderDetailModel> items=new ArrayList<>();

}
