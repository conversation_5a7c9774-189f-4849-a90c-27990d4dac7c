package com.yxt.purchase.domain.service.impl;

import com.yxt.purchase.domain.PurchaseOrderDetailAggregate;
import com.yxt.purchase.domain.command.CreatePurchaseOrderDetailCommand;
import com.yxt.purchase.domain.command.UpdatePurchaseOrderDetailCommand;
import com.yxt.purchase.domain.repository.PurchaseOrderDetailRepository;
import com.yxt.purchase.domain.service.PurchaseOrderDetailDomainService;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 采购商品明细领域服务实现类
 */
@Service
public class PurchaseOrderDetailDomainServiceImpl implements PurchaseOrderDetailDomainService {

  @Autowired
  private PurchaseOrderDetailRepository purchaseOrderDetailRepository;

  @Override
  public PurchaseOrderDetailAggregate createDetail(CreatePurchaseOrderDetailCommand command) {
    // 创建聚合根
    PurchaseOrderDetailAggregate detail = new PurchaseOrderDetailAggregate();
    detail.setPurchaseOrderNo(command.getPurchaseOrderNo());
    detail.setErpCode(command.getErpCode());
    detail.setErpName(command.getErpName());
    detail.setCommoditySpec(command.getCommoditySpec());
    detail.setManufacture(command.getManufacture());
    detail.setCommodityCount(command.getCommodityCount());
    detail.setStatus("0"); // 默认正常状态
    detail.setSysCreateTime(LocalDateTime.now());
    detail.setSysUpdateTime(LocalDateTime.now());

    // 验证聚合根
    if (!detail.isValid()) {
      throw new IllegalArgumentException("采购商品明细信息不完整");
    }

    // 保存聚合根
    return purchaseOrderDetailRepository.save(detail);
  }

  @Override
  public Optional<PurchaseOrderDetailAggregate> getDetailById(Long id) {
    return purchaseOrderDetailRepository.findById(id);
  }

  @Override
  public List<PurchaseOrderDetailAggregate> getDetailsByPurchaseOrderNo(String purchaseOrderNo) {
    return purchaseOrderDetailRepository.findByPurchaseOrderNo(purchaseOrderNo);
  }

  @Override
  public PurchaseOrderDetailAggregate updateDetail(UpdatePurchaseOrderDetailCommand command) {
    // 获取聚合根
    Optional<PurchaseOrderDetailAggregate> optionalDetail = purchaseOrderDetailRepository.findById(command.getId());
    if (!optionalDetail.isPresent()) {
      throw new RuntimeException("采购商品明细不存在");
    }

    PurchaseOrderDetailAggregate detail = optionalDetail.get();

    // 更新商品信息
    detail.updateCommodityCount(command.getCommodityCount());
    // 保存更新后的聚合根
    return purchaseOrderDetailRepository.update(detail);
  }

  @Override
  public PurchaseOrderDetailAggregate markDetailAsException(Long id, String errorMessage) {
    // 获取聚合根
    Optional<PurchaseOrderDetailAggregate> optionalDetail = purchaseOrderDetailRepository.findById(id);
    if (!optionalDetail.isPresent()) {
      throw new RuntimeException("采购商品明细不存在");
    }

    PurchaseOrderDetailAggregate detail = optionalDetail.get();

    // 标记为异常状态
    detail.markAsException(errorMessage);

    // 保存更新后的聚合根
    return purchaseOrderDetailRepository.update(detail);
  }

  @Override
  public PurchaseOrderDetailAggregate markDetailAsNormal(Long id) {
    // 获取聚合根
    Optional<PurchaseOrderDetailAggregate> optionalDetail = purchaseOrderDetailRepository.findById(id);
    if (!optionalDetail.isPresent()) {
      throw new RuntimeException("采购商品明细不存在");
    }

    PurchaseOrderDetailAggregate detail = optionalDetail.get();

    // 标记为正常状态
    detail.markAsNormal();

    // 保存更新后的聚合根
    return purchaseOrderDetailRepository.update(detail);
  }

  @Override
  public boolean deleteDetailById(Long id) {
    return purchaseOrderDetailRepository.deleteById(id);
  }

  @Override
  public boolean deleteDetailsByPurchaseOrderNo(String purchaseOrderNo) {
    return purchaseOrderDetailRepository.deleteByPurchaseOrderNo(purchaseOrderNo);
  }
}