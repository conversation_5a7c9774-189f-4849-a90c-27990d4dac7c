package com.yxt.purchase.domain.service.impl;

import com.yxt.order.common.order_world_dto.es.BizLogInfo;
import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.domain.PurchaseOrderDetailAggregate;
import com.yxt.purchase.domain.command.CreatePurchaseOrderDetailCommand;
import com.yxt.purchase.domain.command.PurchaseOrderDetailQueryCommand;
import com.yxt.purchase.domain.command.RemovePurchaseOrderDetailCommand;
import com.yxt.purchase.domain.command.UpdatePurchaseOrderDetailCommand;
import com.yxt.purchase.domain.entity.PurchaseOrderDetailQueryResult;
import com.yxt.purchase.domain.log.BizAction;
import com.yxt.purchase.domain.log.BizLogService;
import com.yxt.purchase.domain.log.BizScene;
import com.yxt.purchase.domain.repository.PurchaseOrderDetailRepository;
import com.yxt.purchase.domain.repository.PurchaseOrderRepository;
import com.yxt.purchase.domain.service.PurchaseOrderDetailDomainService;
import com.yxt.purchase.enums.PurchaseOrderStatusEnum;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 采购商品明细领域服务实现类
 */
@Service
public class PurchaseOrderDetailDomainServiceImpl implements PurchaseOrderDetailDomainService {

  @Autowired
  private PurchaseOrderDetailRepository purchaseOrderDetailRepository;

  @Autowired
  private PurchaseOrderRepository purchaseOrderRepository;
  @Autowired
  private BizLogService bizLogService;

  @Override
  public PurchaseOrderDetailAggregate createDetail(CreatePurchaseOrderDetailCommand command) {
    // 创建聚合根
    PurchaseOrderDetailAggregate detail = new PurchaseOrderDetailAggregate();
    detail.setPurchaseOrderNo(command.getPurchaseOrderNo());
    detail.setErpCode(command.getErpCode());
    detail.setErpName(command.getErpName());
    detail.setCommoditySpec(command.getCommoditySpec());
    detail.setManufacture(command.getManufacture());
    detail.setCommodityCount(command.getCommodityCount());
    detail.setStatus("0"); // 默认正常状态
    detail.setSysCreateTime(LocalDateTime.now());
    detail.setSysUpdateTime(LocalDateTime.now());

    // 验证聚合根
    if (!detail.isValid()) {
      throw new IllegalArgumentException("采购商品明细信息不完整");
    }

    // 保存聚合根
    return purchaseOrderDetailRepository.save(detail);
  }

  @Override
  public Optional<PurchaseOrderDetailAggregate> getDetailById(Long id) {
    return purchaseOrderDetailRepository.findById(id);
  }

  @Override
  public List<PurchaseOrderDetailAggregate> getDetailsByPurchaseOrderNo(String purchaseOrderNo) {
    return purchaseOrderDetailRepository.findByPurchaseOrderNo(purchaseOrderNo);
  }

  @Override
  public PurchaseOrderDetailAggregate updateDetail(UpdatePurchaseOrderDetailCommand command) {
    // 获取聚合根
    Optional<PurchaseOrderDetailAggregate> optionalDetail = purchaseOrderDetailRepository.findById(
        command.getId());
    if (!optionalDetail.isPresent()) {
      throw new RuntimeException("采购商品明细不存在");
    }
    PurchaseOrderDetailAggregate detail = optionalDetail.get();
    // 验证采购单号是否匹配
    if (command.getPurchaseOrderNo() != null && !command.getPurchaseOrderNo()
        .equals(detail.getPurchaseOrderNo())) {
      throw new RuntimeException("采购单号与明细不匹配");
    }
    PurchaseOrderAggregate order = purchaseOrderRepository.findByPurchaseOrderNo(
        command.getPurchaseOrderNo());
    if (order == null) {
      throw new RuntimeException("采购单信息不存在");
    }

    purchaseOrderDetailRepository.checkCommodityIsValid(order.getOrganizationCode(),
        detail.getErpCode(), String.valueOf(command.getCommodityCount().intValue()));
    // 更新商品信息
    // 保存更新后的聚合根
    addUpdateItemLog(detail.getPurchaseOrderNo(), command.getUserId(), detail.getErpCode(),
        detail.getCommodityCount().intValue(), command.getCommodityCount().intValue());
    detail.updateCommodityCount(command.getCommodityCount());
    return purchaseOrderDetailRepository.update(detail);
  }

  @Override
  public PurchaseOrderDetailAggregate markDetailAsException(Long id, String errorMessage) {
    // 获取聚合根
    Optional<PurchaseOrderDetailAggregate> optionalDetail = purchaseOrderDetailRepository.findById(
        id);
    if (!optionalDetail.isPresent()) {
      throw new RuntimeException("采购商品明细不存在");
    }

    PurchaseOrderDetailAggregate detail = optionalDetail.get();

    // 标记为异常状态
    detail.markAsException(errorMessage);

    // 保存更新后的聚合根
    return purchaseOrderDetailRepository.update(detail);
  }

  @Override
  public PurchaseOrderDetailAggregate markDetailAsNormal(Long id) {
    // 获取聚合根
    Optional<PurchaseOrderDetailAggregate> optionalDetail = purchaseOrderDetailRepository.findById(
        id);
    if (!optionalDetail.isPresent()) {
      throw new RuntimeException("采购商品明细不存在");
    }

    PurchaseOrderDetailAggregate detail = optionalDetail.get();

    // 标记为正常状态
    detail.markAsNormal();

    // 保存更新后的聚合根
    return purchaseOrderDetailRepository.update(detail);
  }

  @Override
  public boolean deleteDetailById(Long id) {
    return purchaseOrderDetailRepository.deleteById(id);
  }

  @Override
  public boolean deleteDetail(RemovePurchaseOrderDetailCommand command) {

    // 获取聚合根
    Optional<PurchaseOrderDetailAggregate> optionalDetail = purchaseOrderDetailRepository.findById(
        command.getId());
    if (!optionalDetail.isPresent()) {
      throw new RuntimeException("采购商品明细不存在");
    }
    PurchaseOrderDetailAggregate detail = optionalDetail.get();
    // 验证采购单号是否匹配
    if (!command.getPurchaseOrderNo().equals(detail.getPurchaseOrderNo())) {
      throw new RuntimeException("采购单号与明细不匹配");
    }
    addDeleteItemLog(detail.getPurchaseOrderNo(), command.getUserId(), detail.getErpCode());
    // 删除明细
    return purchaseOrderDetailRepository.deleteById(command.getId());
  }

  @Override
  public boolean deleteDetailsByPurchaseOrderNo(String purchaseOrderNo) {
    return purchaseOrderDetailRepository.deleteByPurchaseOrderNo(purchaseOrderNo);
  }

  @Override
  public PurchaseOrderDetailQueryResult queryPurchaseOrderDetails(
      PurchaseOrderDetailQueryCommand command) {

    // 设置默认分页参数
    if (command.getCurrentPage() == null || command.getCurrentPage() < 1) {
      command.setCurrentPage(1);
    }
    if (command.getPageSize() == null || command.getPageSize() < 1) {
      command.setPageSize(10);
    }

    // 查询采购单明细列表
    List<PurchaseOrderDetailAggregate> detailAggregates = purchaseOrderDetailRepository.findByPurchaseOrderNoAndStatus(
        command.getPurchaseOrderNo(), command.getStatus(), command.getCurrentPage(),
        command.getPageSize(), command.getErpName(), command.getErpCode());

    // 查询总数
    long totalCount = purchaseOrderDetailRepository.countByPurchaseOrderNoAndStatus(
        command.getPurchaseOrderNo(), command.getStatus(), command.getErpName());

    // 计算总页数
    long totalPage = (totalCount + command.getPageSize() - 1) / command.getPageSize();

    // 构建查询结果
    PurchaseOrderDetailQueryResult result = new PurchaseOrderDetailQueryResult();
    result.setPurchaseOrderDetailAggregates(detailAggregates);
    result.setTotalCount(totalCount);
    result.setTotalPage(totalPage);
    result.setCurrentPage(command.getCurrentPage());
    result.setPageSize(command.getPageSize());

    return result;
  }

  private void addUpdateItemLog(String purchaseOrderNo, String userId, String erpCode, int orgCount,
      int newCount) {
    List<BizLogInfo> logs = new ArrayList<>();

    BizLogInfo bizLogInfo = new BizLogInfo();
    bizLogInfo.setBizNo(purchaseOrderNo);
    bizLogInfo.setBizScene(BizScene.PURCHASE_ORDER);
    bizLogInfo.setBizAction(BizAction.ORDER_COMMODITY_UPDATE);
    bizLogInfo.setBizResultDesc(
        String.format("修改商品%s采购数量，%s -> %s", erpCode, orgCount, newCount));
    bizLogInfo.setOperateTime(LocalDateTime.now());
    bizLogInfo.setOperatorId(userId);
    bizLogInfo.setExtensionNum1(PurchaseOrderStatusEnum.WAIT_CONFIRM.getCode());
    logs.add(bizLogInfo);

    bizLogService.log(logs);
  }

  private void addDeleteItemLog(String purchaseOrderNo, String userId, String erpCode) {
    List<BizLogInfo> logs = new ArrayList<>();

    BizLogInfo bizLogInfo = new BizLogInfo();
    bizLogInfo.setBizNo(purchaseOrderNo);
    bizLogInfo.setBizScene(BizScene.PURCHASE_ORDER);
    bizLogInfo.setBizAction(BizAction.ORDER_COMMODITY_REMOVE);
    bizLogInfo.setBizResultDesc(
        String.format("删除商品%s", erpCode));
    bizLogInfo.setOperateTime(LocalDateTime.now());
    bizLogInfo.setOperatorId(userId);
    bizLogInfo.setExtensionNum1(PurchaseOrderStatusEnum.WAIT_CONFIRM.getCode());
    logs.add(bizLogInfo);

    bizLogService.log(logs);
  }

}