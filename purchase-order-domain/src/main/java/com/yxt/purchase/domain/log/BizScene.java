package com.yxt.purchase.domain.log;

import com.yxt.order.common.order_world_dto.es.IBizScene;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BizScene implements IBizScene {

  PURCHASE_ORDER("采购单")
  ;

  private final String bizScene;

  @Override
  public String bizScene() {
    return bizScene;
  }

  public static IBizScene getBizScene(String bizScene) {
    //根据 bizScene 获取枚举
    for (BizScene bizSceneEnum : BizScene.values()) {
      if (bizSceneEnum.getBizScene().equalsIgnoreCase(bizScene)) {
        return bizSceneEnum;
      }
    }
    return null;
  }
}
