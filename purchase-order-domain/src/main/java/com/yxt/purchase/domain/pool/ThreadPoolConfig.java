package com.yxt.purchase.domain.pool;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.ttl.threadpool.*;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Data
@Configuration
@ConfigurationProperties(prefix = "threadpool")
public class ThreadPoolConfig {


  public final static String AUTH_ORG_QUERY_THREAD_POOL = "authOrgQueryThreadPool";


  private Integer authOrgQueryCoreSize = 16;
  private Integer authOrgQueryMaxSize = 64;
  private Long authOrgQueryKeepAliveTime = 3000L;
  private Integer authOrgQueryCapacity = 128;



  @Bean(AUTH_ORG_QUERY_THREAD_POOL)
  public Executor authOrgQueryThreadPool() {
    ThreadPoolExecutor executor = new ThreadPoolExecutor(
        authOrgQueryCoreSize,
        authOrgQueryMaxSize,
        authOrgQueryKeepAliveTime,
        TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(authOrgQueryCapacity),
        ThreadFactoryBuilder.create().setNamePrefix("auth-org-pool").build(),
        new CallerRunsPolicy()
    );
    return TtlExecutors.getTtlExecutor(executor);
  }

}
