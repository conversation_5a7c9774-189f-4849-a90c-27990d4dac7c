package com.yxt.purchase.domain.log;

import com.yxt.order.common.order_world_dto.es.IBizAction;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BizAction implements IBizAction {
  ORDER_CREATE("创建采购单"),
  ORDER_CONFIRM("审核确认"),
  ORDER_CANCEL("已取消"),
  ORDER_CONFIRM_FAIL("订单确认失败"),
  ORDER_COMMODITY_UPDATE("修改商品"),
  ORDER_COMMODITY_REMOVE("删除商品"),

  UNKNOWN("未知操作")
  ;

  private final String bizAction;

  @Override
  public String bizAction() {
    return bizAction;
  }

  public static IBizAction getBizAction(String bizAction) {
    //根据 bizAction 获取枚举
    for (BizAction bizActionEnum : BizAction.values()) {
      if (bizActionEnum.bizAction.equals(bizAction)) {
        return bizActionEnum;
      }
    }
    return UNKNOWN;
  }
}
