package com.yxt.purchase.domain.service;

import com.yxt.purchase.domain.PurchaseOrderDetailAggregate;
import com.yxt.purchase.domain.command.CreatePurchaseOrderDetailCommand;
import com.yxt.purchase.domain.command.RemovePurchaseOrderDetailCommand;
import com.yxt.purchase.domain.command.UpdatePurchaseOrderDetailCommand;
import java.util.List;
import java.util.Optional;

/**
 * 采购商品明细领域服务接口
 */
public interface PurchaseOrderDetailDomainService {

  /**
   * 创建采购商品明细
   *
   * @param command 创建采购商品明细命令
   * @return 创建后的采购商品明细聚合根
   */
  PurchaseOrderDetailAggregate createDetail(CreatePurchaseOrderDetailCommand command);

  /**
   * 根据ID查询采购商品明细
   *
   * @param id 明细ID
   * @return 采购商品明细聚合根
   */
  Optional<PurchaseOrderDetailAggregate> getDetailById(Long id);

  /**
   * 根据采购单号查询采购商品明细列表
   *
   * @param purchaseOrderNo 采购单号
   * @return 采购商品明细聚合根列表
   */
  List<PurchaseOrderDetailAggregate> getDetailsByPurchaseOrderNo(String purchaseOrderNo);

  /**
   * 更新采购商品明细
   *
   * @param command 更新采购商品明细命令
   * @return 更新后的采购商品明细聚合根
   */
  PurchaseOrderDetailAggregate updateDetail(UpdatePurchaseOrderDetailCommand command);

  /**
   * 将采购商品明细标记为异常状态
   *
   * @param id 明细ID
   * @param errorMessage 错误信息
   * @return 更新后的采购商品明细聚合根
   */
  PurchaseOrderDetailAggregate markDetailAsException(Long id, String errorMessage);

  /**
   * 将采购商品明细标记为正常状态
   *
   * @param id 明细ID
   * @return 更新后的采购商品明细聚合根
   */
  PurchaseOrderDetailAggregate markDetailAsNormal(Long id);

  /**
   * 根据ID删除采购商品明细
   *
   * @param id 明细ID
   * @return 是否删除成功
   */
  boolean deleteDetailById(Long id);

  /**
   * 根据ID和采购单号删除采购商品明细，并验证采购单号是否匹配
   *
   * @param command 删除采购商品明细命令
   * @return 是否删除成功
   */
  boolean deleteDetail(RemovePurchaseOrderDetailCommand command);

  /**
   * 根据采购单号删除采购商品明细
   *
   * @param purchaseOrderNo 采购单号
   * @return 是否删除成功
   */
  boolean deleteDetailsByPurchaseOrderNo(String purchaseOrderNo);
}