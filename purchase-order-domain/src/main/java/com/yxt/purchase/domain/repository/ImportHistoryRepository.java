package com.yxt.purchase.domain.repository;

import com.yxt.purchase.domain.ImportHistoryAggregate;
import com.yxt.purchase.domain.command.ImportHistoryQueryCommand;
import java.util.List;

/**
 * 导入历史仓储接口
 */
public interface ImportHistoryRepository {

  /**
   * 保存导入历史记录
   *
   * @param importHistoryAggregate 导入历史聚合根
   * @return 保存后的聚合根
   */
  ImportHistoryAggregate save(ImportHistoryAggregate importHistoryAggregate);

  /**
   * 更新导入历史记录
   *
   * @param importHistoryAggregate 导入历史聚合根
   */
  void update(ImportHistoryAggregate importHistoryAggregate);

  /**
   * 查询记录信息
   * @param importId 导入id
   * @return 记录信息
   */
  ImportHistoryAggregate findByImportId(String importId);
  /**
   * 查询导入历史列表
   *
   * @param command 查询命令
   * @return 导入历史列表
   */
  List<ImportHistoryAggregate> queryImportHistory(ImportHistoryQueryCommand command);
}
