package com.yxt.purchase.domain.entity;

import com.yxt.purchase.domain.PurchaseOrderAggregate;
import java.util.List;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

/**
 * @Description: 处理器使用的山下问
 * @Author: yjf
 * @Date: 2025-05-19
 */
@Data
public class HandlerContext {

  /**
   * 聚合根，这里使用数据 主要是在处理过程中可能存在拆分 聚合根的情况
   */
  private List<PurchaseOrderAggregate> aggregates;
  private String systemCode;
  private String tradeMode;
}
