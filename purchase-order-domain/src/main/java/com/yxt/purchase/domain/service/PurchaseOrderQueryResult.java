package com.yxt.purchase.domain.service;

import com.yxt.purchase.domain.PurchaseOrderAggregate;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 采购单查询结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrderQueryResult {
    
    /**
     * 采购单聚合根列表
     */
    private List<PurchaseOrderAggregate> purchaseOrderAggregates;
    
    /**
     * 总记录数
     */
    private Long totalCount;
    
    /**
     * 总页数
     */
    private Long totalPage;
    
    /**
     * 当前页码
     */
    private Integer currentPage;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
}
