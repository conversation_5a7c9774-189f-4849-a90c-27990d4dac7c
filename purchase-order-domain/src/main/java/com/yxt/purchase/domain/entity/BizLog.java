package com.yxt.purchase.domain.entity;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 业务日志实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BizLog {
    
    /**
     * 日志ID
     */
    private Long id;
    
    /**
     * 业务ID
     */
    private String bizId;
    
    /**
     * 业务类型
     */
    private String bizType;
    
    /**
     * 操作类型
     */
    private String operationType;
    
    /**
     * 操作内容
     */
    private String operationContent;
    
    /**
     * 操作人ID
     */
    private String operatorId;
    
    /**
     * 操作人姓名
     */
    private String operatorName;
    
    /**
     * 操作时间
     */
    private LocalDateTime operationTime;
}
