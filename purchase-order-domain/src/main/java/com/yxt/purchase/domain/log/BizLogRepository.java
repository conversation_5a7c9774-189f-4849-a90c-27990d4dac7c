package com.yxt.purchase.domain.log;


import com.yxt.order.common.order_world_dto.es.BizLogInfo;
import com.yxt.purchase.domain.log.command.BizLogBatchSearchCommand;
import com.yxt.purchase.domain.log.command.BizLogSearchCommand;
import java.util.List;

public interface BizLogRepository {

  void saveLog(List<BizLogInfo> bizLogList);

  List<BizLogInfo> logSearch(BizLogSearchCommand command);

  List<BizLogInfo> logSearchBatch(BizLogBatchSearchCommand command);
}
