package com.yxt.purchase.domain.service;

import com.yxt.purchase.domain.ImportHistoryAggregate;
import com.yxt.purchase.domain.command.ImportHistoryQueryCommand;
import java.util.List;

/**
 * 导入历史领域服务接口
 */
public interface ImportHistoryDomainService {
    
    /**
     * 查询导入历史列表
     *
     * @param command 查询命令
     * @return 导入历史列表
     */
    List<ImportHistoryAggregate> queryImportHistory(ImportHistoryQueryCommand command);
    
    /**
     * 创建导入历史记录
     *
     * @param fileName 文件名
     * @param userId 用户ID
     * @param userName 用户名
     * @return 导入历史聚合根
     */
    ImportHistoryAggregate createImportHistory( String fileName, String userId, String userName);
    
    /**
     * 更新导入历史状态
     *
     * @param importId 导入ID
     * @param status 状态
     * @param errorFilePath 结果文件
     * @param purchaseOrderNos 采购单号列表
     */
    void updateImportHistoryStatus(String importId, Integer status,  String errorFilePath,
                                                   List<String> purchaseOrderNos);
}
