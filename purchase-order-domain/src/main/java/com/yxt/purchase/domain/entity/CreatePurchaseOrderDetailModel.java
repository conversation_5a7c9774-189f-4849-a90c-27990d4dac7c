package com.yxt.purchase.domain.entity;

import com.yxt.purchase.enums.CommodityTypeEnum;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class CreatePurchaseOrderDetailModel {

  /**
   * 商品名称
   */
  private String erpName;
  /**
   * erp编码
   */
  private String erpCode;

  /**
   * 商品规格
   */
  private String commoditySpec;


  /**
   * 生产商
   */
  private String manufacture;

  /**
   * 错误消息
   */
  private String ex_msg;

  /**
   * 状态 -1 异常 0 正常
   */
  private String status;

  /**
   * 备注
   */
  private String remark;

  /**
   * 数量
   */
  private BigDecimal qty;

  /**
   * 是否冷链
   */
  private Boolean isColdChain;

  /**
   * 商品类型 消耗品要直接调用ERP 接口，不在经过pos 接口了
   */
  private CommodityTypeEnum commodityType;
}
