package com.yxt.purchase.domain;

import com.yxt.common.ddd.domain.model.BaseAggregateRoot;
import com.yxt.purchase.domain.event.PurchaseOrderDetailDomainEvent;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 采购商品明细聚合根
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderDetailAggregate extends BaseAggregateRoot<PurchaseOrderDetailDomainEvent<?>> {
    /**
     * 明细ID
     */
    private Long id;

    /**
     * 采购订单编号
     */
    private String purchaseOrderNo;

    /**
     * 商品编码
     */
    private String erpCode;

    /**
     * 商品名称
     */
    private String erpName;

    /**
     * 商品规格
     */
    private String commoditySpec;

    /**
     * 生产商
     */
    private String manufacture;

    /**
     * 商品数量
     */
    private BigDecimal commodityCount;

    /**
     * 状态：-1 异常 0 正常
     */
    private String status;

    /**
     * 错误信息，如果status为-1这个字段必定有值
     */
    private String exMsg;

    /**
     * 系统创建时间
     */
    private LocalDateTime sysCreateTime;

    /**
     * 系统更新时间
     */
    private LocalDateTime sysUpdateTime;

    /**
     * 设置商品明细为异常状态
     *
     * @param errorMessage 错误信息
     */
    public void markAsException(String errorMessage) {
        this.status = "-1";
        this.exMsg = errorMessage;
        this.sysUpdateTime = LocalDateTime.now();


    }

    /**
     * 设置商品明细为正常状态
     */
    public void markAsNormal() {
        this.status = "0";
        this.exMsg = null;
        this.sysUpdateTime = LocalDateTime.now();

    }

    /**
     * 更新商品数量
     *
     * @param newCount 新数量
     */
    public void updateCommodityCount(BigDecimal newCount) {
        if (newCount == null || newCount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("商品数量必须大于0");
        }
        this.status="0";
        this.exMsg="";
        this.commodityCount = newCount;
        this.sysUpdateTime = LocalDateTime.now();
    }

    /**
     * 更新商品信息
     *
     * @param erpName 商品名称
     * @param commoditySpec 商品规格
     * @param manufacture 生产商
     */
    public void updateCommodityInfo(String erpName, String commoditySpec, String manufacture) {
        if (erpName != null && !erpName.isEmpty()) {
            this.erpName = erpName;
        }

        this.commoditySpec = commoditySpec;
        this.manufacture = manufacture;
        this.sysUpdateTime = LocalDateTime.now();
    }

    /**
     * 检查明细是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        // 基本验证逻辑
        if (this.erpCode == null || this.erpCode.isEmpty()) {
            return false;
        }
        if (this.erpName == null || this.erpName.isEmpty()) {
            return false;
        }
        if (this.commodityCount == null || this.commodityCount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        return true;
    }
}