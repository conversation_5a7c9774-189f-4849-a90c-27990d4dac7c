package com.yxt.purchase.domain.log;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.yxt.order.common.order_world_dto.es.BizLogInfo;

import com.yxt.purchase.domain.log.command.BizLogSearchCommand;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class BizLogService {

  @Resource
  private BizLogRepository bizLogRepository;


  public void log(BizLogInfo bizLogInfo) {
    bizLogRepository.saveLog(ListUtil.toList(bizLogInfo));
  }

  public void log(List<BizLogInfo> bizLogList) {
    if(CollUtil.isEmpty(bizLogList)){
      return;
    }
    bizLogRepository.saveLog(bizLogList);
  }

  public List<BizLogInfo> logSearch(BizLogSearchCommand command) {
    return bizLogRepository.logSearch(command);
  }
}
