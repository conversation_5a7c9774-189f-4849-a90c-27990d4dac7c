package com.yxt.purchase.domain.handler;

import com.yxt.purchase.domain.entity.HandlerContext;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * 采购订单详情处理抽象类
 */
@Slf4j
public abstract class AbstractPurchaseOrderHandler<T extends HandlerContext> implements AbstractHandler<T>{

  /**
   * 上一个处理器
   * */
  private AbstractPurchaseOrderHandler preHandler;

  /**
   * 下一个处理器
   * */
  private AbstractPurchaseOrderHandler nextHandler;

  @Override
  public void execute(T context) {
    log.info("处理器 {} 开始执行...",this.getClass().getName());
    doHandleReal(context);
    log.info("处理器 {} 执行完成... ",this.getClass().getName());
    //调用下一个处理器处理
    if (null != nextHandler) {
      nextHandler.execute(context);
    }
  }
  /**
   * 每个处理器特有的业务处理
   * */
  public abstract void doHandleReal(T context);

  /**
   * 设置上一个处理器
   * @param preHandler 上一个处理器
   * @return 上一个处理器
   * */
  public AbstractPurchaseOrderHandler setPreHandler(AbstractPurchaseOrderHandler preHandler) {
    this.preHandler = preHandler;
    return preHandler;
  }

  /**
   * 设置下一个处理器  同时制定下个处理器的前一个处理器为当前处理器
   * @param nextHandler 下一个处理器
   * @return 下一个处理器
   * */
  public AbstractPurchaseOrderHandler setNextHandler(AbstractPurchaseOrderHandler nextHandler) {
    this.nextHandler = nextHandler;
    nextHandler.setPreHandler(this);
    return nextHandler;
  }

}
