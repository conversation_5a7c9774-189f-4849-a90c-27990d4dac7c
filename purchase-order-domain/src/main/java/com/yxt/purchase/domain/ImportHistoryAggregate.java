package com.yxt.purchase.domain;

import com.yxt.purchase.enums.ImportStatusEnum;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 导入历史聚合根
 */
@Data
public class ImportHistoryAggregate {


    /**
     * 导入记录ID
     */
    private String importId;


    /**
     * 导入文件名
     */
    private String fileName;
    
    /**
     * 导入状态
     */
    private ImportStatusEnum importStatus;
    
    /**
     * 导入时间
     */
    private LocalDateTime importTime;
    
    /**
     * 导入人ID
     */
    private String importBy;
    
    /**
     * 导入人姓名
     */
    private String importByName;

    /**
     * 错误文件路径
     */
    private String errorFilePath;
    
    /**
     * 生成的采购单号列表
     */
    private List<String> purchaseOrderNos;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
