package com.yxt.purchase.domain.command;

import com.yxt.purchase.domain.entity.PurchaseOrderDetailModel;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * 批量确认命令
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BatchReConfirmCommand extends BaseQueryCommand {
  /**
   * 采购单ID列表
   */
  private List<String> purchaseOrderIds;

}
