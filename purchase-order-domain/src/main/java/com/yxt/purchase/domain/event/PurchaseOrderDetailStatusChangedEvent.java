package com.yxt.purchase.domain.event;

import lombok.Getter;

/**
 * 采购商品明细状态变更事件
 */
@Getter
public class PurchaseOrderDetailStatusChangedEvent extends PurchaseOrderDetailDomainEvent<Long> {
  private final Integer status;
  private final String exMsg;

  public PurchaseOrderDetailStatusChangedEvent(Long aggregateId, Integer status, String exMsg) {
    super(aggregateId);
    this.status = status;
    this.exMsg = exMsg;
  }
}



