package com.yxt.purchase.domain.command;

import java.util.List;
import lombok.Data;

/**
 * 采购单查询命令
 */
@Data
public class BaseQueryCommand {

    /**
     * 分公司编码
     */
    private String companyCode;
    
    /**
     * 所属机构编码
     */
    private String organizationCode;
    
    /**
     * 采购单标签 办公用品:CONSUMABLES 导入铺货: MULTIPLE_STORE
     */
    private String purchaseOrderLabel;
    
    /**
     * 采购单状态 待确认:WAIT_CONFIRM 已确认:CONFIRMED 已完成:FINISHED 已取消:CANCELED
     */
    private String state;
    
    /**
     * 采购单号
     */
    private String purchaseOrderNo;
    
    /**
     * 父采购单号
     */
    private String parentPurchaseOrderNo;
    
    /**
     * 关联订单号
     */
    private String relatedOrderNo;
    
    /**
     * 用户ID
     */
    private String userId;

    /**
     * 创建人id
     */
    private String createdBy;
    
    /**
     * 采购订单创建开始时间
     */
    private String orderStartTime;
    
    /**
     * 采购订单创建结束时间
     */
    private String orderEndTime;
    
    /**
     * 是否生成订单
     */
    private Boolean isRelatedOrder;


    /**
     * 有权限的分公司列表
     */
    private List<String> companyCodeList;

    /**
     * 有权限的门店列表
     */
    private List<String> storeCodeList;
}
