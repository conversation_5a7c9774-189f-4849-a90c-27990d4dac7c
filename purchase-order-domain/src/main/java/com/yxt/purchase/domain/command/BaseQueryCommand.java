package com.yxt.purchase.domain.command;

import lombok.Data;

/**
 * 采购单查询命令
 */
@Data
public class PurchaseOrderQueryCommand {

    /**
     * 分公司编码
     */
    private String companyCode;
    
    /**
     * 所属机构编码
     */
    private String organizationCode;
    
    /**
     * 采购单标签 办公用品:CONSUMABLES 导入铺货: MULTIPLE_STORE
     */
    private String purchaseOrderLabel;
    
    /**
     * 采购单状态 待确认:WAIT_CONFIRM 已确认:CONFIRMED 已完成:FINISHED 已取消:CANCELED
     */
    private String state;
    
    /**
     * 采购单号
     */
    private String purchaseOrderNo;
    
    /**
     * 父采购单号
     */
    private String parentPurchaseOrderNo;
    
    /**
     * 关联订单号
     */
    private String relatedOrderNo;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 采购订单创建开始时间
     */
    private String orderStartTime;
    
    /**
     * 采购订单创建结束时间
     */
    private String orderEndTime;
    
    /**
     * 是否生成订单
     */
    private Boolean isRelatedOrder;
    
    /**
     * 当前页码
     */
    private Integer currentPage;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
}
