package com.yxt.purchase.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.domain.command.PurchaseOrderCreateCommand;
import com.yxt.purchase.domain.entity.HandlerContext;
import com.yxt.purchase.domain.entity.PurchaseOrder;
import com.yxt.purchase.domain.entity.PurchaseOrderDetailModel;
import com.yxt.purchase.domain.handler.CreatePurchaseOrderHandler;
import com.yxt.purchase.domain.repository.PurchaseOrderRepository;
import com.yxt.purchase.domain.service.PurchaseOrderDomainService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 采购单领域服务实现类
 */
@Service
public class PurchaseOrderDomainServiceImpl implements PurchaseOrderDomainService {

  @Autowired
  private CreatePurchaseOrderHandler<HandlerContext> createPurchaseOrderHandler;
  @Value("${trade.systemCode}")
  private String systemCode;
  @Value("${trade.tradeMode}")
  private String tradeMode;
  @Override
  public List<String> createPurchaseOrder(PurchaseOrderCreateCommand command) {
    // 调用商品接口先过滤无效商品
    checkPurchaseOrder(command);
    List<String> successPurchaseOrder = new ArrayList<>();
    // 例如: 为每个组织创建一个采购单
    List<PurchaseOrderAggregate> purchaseOrderAggregates = createPurchaseOrderAggregate(
        command);
    // 使用创建采购单责任链 来创建采购单
    // 将全部报错收集之后统一抛出错误，提升操作人员修改文档的效率
    List<String> errorMsgg = new ArrayList<>();
    for (PurchaseOrderAggregate purchaseOrderAggregate : purchaseOrderAggregates) {
      // 创建采购单
      // 外层是根据门店拆分的采购单
      try {
        HandlerContext handlerContext = new HandlerContext();
        handlerContext.setAggregates(Collections.singletonList(purchaseOrderAggregate));
        createPurchaseOrderHandler.initHandler().execute(handlerContext);
        // 处理成功的采购单号
        successPurchaseOrder.addAll(handlerContext.getAggregates().stream().map(item -> {
          return item.getPurchaseOrderNo().getPurchaseOrderNo();
        }).collect(Collectors.toList()));
      } catch (Exception e) {
        errorMsgg.add(e.getMessage());
      }
    }
    if (CollUtil.isNotEmpty(errorMsgg)) {
      throw new RuntimeException(CollUtil.join(errorMsgg, ",\n"));
    }
    return successPurchaseOrder;
  }


  /**
   * 校验采购单信息基础校验 主要是为了校验调用的数据是否符合基本非空原则
   *
   * @param command 采购单创建命令
   */
  private void checkPurchaseOrder(PurchaseOrderCreateCommand command) {
    // 校验 采购商品中信息是否完整
    if (CollUtil.isEmpty(command.getItems())) {
      throw new RuntimeException("采购商品不能为空");
    }
    Integer rowId = 0;
    // 商品的 编码 数量 和 商家都不能为空
    List<Integer> errRowIds = new java.util.ArrayList<>();
    for (PurchaseOrderDetailModel item : command.getItems()) {
      rowId++;
      if (StrUtil.isBlank(item.getErpCode()) || StrUtil.isBlank(item.getOrganizationCode())
          || StrUtil.isBlank(item.getQty()) || Long.parseLong(item.getQty()) <= 0) {
        errRowIds.add(rowId);
      }
    }
    if (CollUtil.isNotEmpty(errRowIds)) {
      String errMsg = "第" + CollUtil.join(errRowIds, ",") + "行商品信息无效请核对后再试。";
      throw new RuntimeException(errMsg);
    }
  }

  /**
   * 基础拆分，根据门店将传入的采购商品信息分成多个采购单聚合根
   *
   * @param command 采购单创建命令
   * @return
   */
  private List<PurchaseOrderAggregate> createPurchaseOrderAggregate(
      PurchaseOrderCreateCommand command) {
    List<PurchaseOrderAggregate> purchaseOrderAggregates = new java.util.ArrayList<>();
    //  根据门店将传入的采购商品信息分成多个采购单聚合根
    Map<String, List<PurchaseOrderDetailModel>> groupedByStore = command.getItems().stream()
        .collect(Collectors.groupingBy(PurchaseOrderDetailModel::getOrganizationCode));
    for (Map.Entry<String, List<PurchaseOrderDetailModel>> entry : groupedByStore.entrySet()) {
      String storeCode = entry.getKey();
      List<PurchaseOrderDetailModel> items = entry.getValue();
      PurchaseOrderAggregate purchaseOrderAggregate = PurchaseOrderAggregate.build(items,
          command.getIsAutoPayment(), command.getOverduePaymentTime(), command.getAutoConfirm(),
          command.getUserId(), storeCode);
      purchaseOrderAggregates.add(purchaseOrderAggregate);
    }
    return purchaseOrderAggregates;
  }


}
