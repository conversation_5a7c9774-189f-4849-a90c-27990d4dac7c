package com.yxt.purchase.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.domain.command.BatchRejectPurchaseOrderCommand;
import com.yxt.purchase.domain.command.PurchaseOrderCreateCommand;
import com.yxt.purchase.domain.command.PurchaseOrderInfoQueryCommand;
import com.yxt.purchase.domain.command.PurchaseOrderQueryCommand;
import com.yxt.purchase.domain.command.UpdateAutoDeductionCommand;
import com.yxt.purchase.domain.service.PurchaseOrderInfoQueryResult;
import com.yxt.purchase.domain.entity.HandlerContext;
import com.yxt.purchase.domain.entity.PurchaseOrderDetailModel;
import com.yxt.purchase.domain.handler.CreatePurchaseOrderHandler;
import com.yxt.purchase.domain.repository.PurchaseOrderRepository;
import com.yxt.purchase.domain.service.PurchaseOrderDomainService;
import com.yxt.purchase.domain.service.PurchaseOrderQueryResult;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 采购单领域服务实现类
 */
@Service
public class PurchaseOrderDomainServiceImpl implements PurchaseOrderDomainService {

  @Autowired
  private CreatePurchaseOrderHandler<HandlerContext> createPurchaseOrderHandler;

  @Autowired
  private PurchaseOrderRepository purchaseOrderRepository;

  @Value("${trade.systemCode}")
  private String systemCode;
  @Value("${trade.tradeMode}")
  private String tradeMode;
  @Override
  public List<String> createPurchaseOrder(PurchaseOrderCreateCommand command) {
    // 调用商品接口先过滤无效商品
    checkPurchaseOrder(command);
    List<String> successPurchaseOrder = new ArrayList<>();
    // 例如: 为每个组织创建一个采购单
    List<PurchaseOrderAggregate> purchaseOrderAggregates = createPurchaseOrderAggregate(
        command);
    // 使用创建采购单责任链 来创建采购单
    // 将全部报错收集之后统一抛出错误，提升操作人员修改文档的效率
    List<String> errorMsgg = new ArrayList<>();
    for (PurchaseOrderAggregate purchaseOrderAggregate : purchaseOrderAggregates) {
      // 创建采购单
      // 外层是根据门店拆分的采购单
      try {
        HandlerContext handlerContext = new HandlerContext();
        handlerContext.setAggregates(Collections.singletonList(purchaseOrderAggregate));
        handlerContext.setSystemCode(systemCode);
        handlerContext.setTradeMode(tradeMode);
        createPurchaseOrderHandler.initHandler().execute(handlerContext);
        // 处理成功的采购单号
        successPurchaseOrder.addAll(handlerContext.getAggregates().stream().map(item -> {
          return item.getPurchaseOrderNo().getPurchaseOrderNo();
        }).collect(Collectors.toList()));
      } catch (Exception e) {
        errorMsgg.add(e.getMessage());
      }
    }
    if (CollUtil.isNotEmpty(errorMsgg)) {
      throw new RuntimeException(CollUtil.join(errorMsgg, ",\n"));
    }
    return successPurchaseOrder;
  }


  /**
   * 校验采购单信息基础校验 主要是为了校验调用的数据是否符合基本非空原则
   *
   * @param command 采购单创建命令
   */
  private void checkPurchaseOrder(PurchaseOrderCreateCommand command) {
    // 校验 采购商品中信息是否完整
    if (CollUtil.isEmpty(command.getItems())) {
      throw new RuntimeException("采购商品不能为空");
    }
    Integer rowId = 0;
    // 商品的 编码 数量 和 商家都不能为空
    List<Integer> errRowIds = new java.util.ArrayList<>();
    for (PurchaseOrderDetailModel item : command.getItems()) {
      rowId++;
      if (StrUtil.isBlank(item.getErpCode()) || StrUtil.isBlank(item.getOrganizationCode())
          || StrUtil.isBlank(item.getQty()) || Long.parseLong(item.getQty()) <= 0) {
        errRowIds.add(rowId);
      }
    }
    if (CollUtil.isNotEmpty(errRowIds)) {
      String errMsg = "第" + CollUtil.join(errRowIds, ",") + "行商品信息无效请核对后再试。";
      throw new RuntimeException(errMsg);
    }
  }

  /**
   * 基础拆分，根据门店将传入的采购商品信息分成多个采购单聚合根
   *
   * @param command 采购单创建命令
   * @return
   */
  private List<PurchaseOrderAggregate> createPurchaseOrderAggregate(
      PurchaseOrderCreateCommand command) {
    List<PurchaseOrderAggregate> purchaseOrderAggregates = new java.util.ArrayList<>();
    //  根据门店将传入的采购商品信息分成多个采购单聚合根
    Map<String, List<PurchaseOrderDetailModel>> groupedByStore = command.getItems().stream()
        .collect(Collectors.groupingBy(PurchaseOrderDetailModel::getOrganizationCode));
    for (Map.Entry<String, List<PurchaseOrderDetailModel>> entry : groupedByStore.entrySet()) {
      String storeCode = entry.getKey();
      List<PurchaseOrderDetailModel> items = entry.getValue();
      PurchaseOrderAggregate purchaseOrderAggregate = PurchaseOrderAggregate.build(items,
          command.getIsAutoPayment(), command.getOverduePaymentTime(), command.getAutoConfirm(),
          command.getUserId(), storeCode);
      purchaseOrderAggregates.add(purchaseOrderAggregate);
    }
    return purchaseOrderAggregates;
  }

  @Override
  public PurchaseOrderQueryResult queryPurchaseOrders(PurchaseOrderQueryCommand command) {

    // 设置默认分页参数
    if (command.getCurrentPage() == null || command.getCurrentPage() < 1) {
      command.setCurrentPage(1);
    }
    if (command.getPageSize() == null || command.getPageSize() < 1) {
      command.setPageSize(10);
    }

    // 调用仓储层查询采购单列表
    return purchaseOrderRepository.queryPurchaseOrders(command);
  }

  @Override
  public boolean updateAutoDeductionConfig(UpdateAutoDeductionCommand command) {
    // 参数校验

    // 查询采购单是否存在
    boolean exists = purchaseOrderRepository.checkPurchaseOrderExists(command.getPurchaseOrderId());
    if (!exists) {
      throw new IllegalArgumentException("采购单不存在: " + command.getPurchaseOrderId());
    }

    // 调用仓储层更新自动扣款配置
    return purchaseOrderRepository.updateAutoDeductionConfig(
        command.getPurchaseOrderId(),
        command.getIsAutoPayment(),
        command.getOverduePaymentTime(),
        command.getUserId());
  }

  @Override
  public List<String> batchRejectPurchaseOrder(BatchRejectPurchaseOrderCommand command) {
    // 参数校验
    if (command == null) {
      throw new IllegalArgumentException("批量驳回命令不能为空");
    }

    // 如果指定了采购单ID列表，则根据ID列表驳回
    if (CollUtil.isNotEmpty(command.getPurchaseOrderIds())) {
      return purchaseOrderRepository.batchRejectByIds(
          command.getPurchaseOrderIds(),
          command.getRejectReason(),
          command.getUserId());
    }

    // 如果没有指定采购单ID列表，则根据查询条件查询并驳回
    PurchaseOrderQueryCommand queryCommand = new PurchaseOrderQueryCommand();
    queryCommand.setCompanyCode(command.getCompanyCode());
    queryCommand.setOrganizationCode(command.getOrganizationCode());
    queryCommand.setPurchaseOrderLabel(command.getPurchaseOrderLabel());
    queryCommand.setState(command.getState());
    queryCommand.setPurchaseOrderNo(command.getPurchaseOrderNo());
    queryCommand.setParentPurchaseOrderNo(command.getParentPurchaseOrderNo());
    queryCommand.setRelatedOrderNo(command.getRelatedOrderNo());
    queryCommand.setOrderStartTime(command.getOrderStartTime());
    queryCommand.setOrderEndTime(command.getOrderEndTime());

    return purchaseOrderRepository.batchRejectByCondition(
        queryCommand,
        command.getRejectReason(),
        command.getUserId());
  }

  @Override
  public PurchaseOrderInfoQueryResult queryPurchaseOrderInfo(PurchaseOrderInfoQueryCommand command) {
    // 参数校验
    if (command == null) {
      throw new IllegalArgumentException("查询命令不能为空");
    }
    if (command.getPurchaseOrderNo() == null || command.getPurchaseOrderNo().isEmpty()) {
      throw new IllegalArgumentException("采购单号不能为空");
    }

    // 查询采购单聚合根
    PurchaseOrderAggregate purchaseOrderAggregate = purchaseOrderRepository.findByPurchaseOrderNo(command.getPurchaseOrderNo());
    if (purchaseOrderAggregate == null) {
      throw new IllegalArgumentException("采购单不存在: " + command.getPurchaseOrderNo());
    }

    // 查询采购单相关的操作日志
    List<BizLog> operationLogs = purchaseOrderRepository.findBizLogsByPurchaseOrderNo(command.getPurchaseOrderNo());

    // 构建查询结果
    PurchaseOrderInfoQueryResult result = new PurchaseOrderInfoQueryResult();
    result.setPurchaseOrderAggregate(purchaseOrderAggregate);
    result.setOperationLogs(operationLogs);

    return result;
  }

}
