package com.yxt.purchase.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.yxt.order.common.order_world_dto.es.BizLogInfo;
import com.yxt.purchase.domain.ImportHistoryAggregate;
import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.domain.PurchaseOrderDetailAggregate;
import com.yxt.purchase.domain.command.BatchReConfirmCommand;
import com.yxt.purchase.domain.command.BatchRejectPurchaseOrderCommand;
import com.yxt.purchase.domain.command.CopyPurchaseOrderCommand;
import com.yxt.purchase.domain.command.PurchaseOrderCreateCommand;
import com.yxt.purchase.domain.command.PurchaseOrderInfoQueryCommand;
import com.yxt.purchase.domain.command.PurchaseOrderQueryCommand;
import com.yxt.purchase.domain.command.UpdateAutoDeductionCommand;
import com.yxt.purchase.domain.command.UpdatePurchaseOrderCancelCommand;
import com.yxt.purchase.domain.command.UpdatePurchaseOrderCompletedCommand;
import com.yxt.purchase.domain.entity.BizLogModel;
import com.yxt.purchase.domain.entity.CreatePurchaseOrderDetailModel;
import com.yxt.purchase.domain.entity.EmployeeInfo;
import com.yxt.purchase.domain.entity.ImportPurchaseProcessModel;
import com.yxt.purchase.domain.entity.PurchaseOrderInfoQueryResult;
import com.yxt.purchase.domain.entity.HandlerContext;
import com.yxt.purchase.domain.entity.PurchaseOrderDetailModel;
import com.yxt.purchase.domain.handler.CreatePurchaseOrderHandler;
import com.yxt.purchase.domain.handler.ReCreateOrderHandler;
import com.yxt.purchase.domain.log.BizAction;
import com.yxt.purchase.domain.log.BizLogService;
import com.yxt.purchase.domain.log.BizScene;
import com.yxt.purchase.domain.log.command.BizLogSearchCommand;
import com.yxt.purchase.domain.repository.EmployeeRepository;
import com.yxt.purchase.domain.repository.PurchaseOrderDetailRepository;
import com.yxt.purchase.domain.repository.PurchaseOrderRepository;
import com.yxt.purchase.domain.service.ImportHistoryDomainService;
import com.yxt.purchase.enums.PurchaseOrderStatusEnum;
import com.yxt.purchase.enums.SettlementStatusEnum;
import com.yxt.purchase.types.OperationUser;
import java.time.LocalDateTime;
import java.util.Map;
import com.yxt.purchase.domain.service.PurchaseOrderDomainService;
import com.yxt.purchase.domain.entity.PurchaseOrderQueryResult;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 采购单领域服务实现类
 */
@Slf4j
@Service
public class PurchaseOrderDomainServiceImpl implements PurchaseOrderDomainService {

  @Autowired
  private CreatePurchaseOrderHandler<HandlerContext> createPurchaseOrderHandler;

  @Autowired
  private ReCreateOrderHandler<HandlerContext> reCreateOrderHandler;
  @Autowired
  private PurchaseOrderRepository purchaseOrderRepository;

  @Autowired
  private PurchaseOrderDetailRepository purchaseOrderDetailRepository;

  @Value("${trade.systemCode}")
  private String systemCode;
  @Value("${trade.tradeMode}")
  private String tradeMode;
  @Autowired
  private BizLogService bizLogService;

  @Autowired
  private EmployeeRepository employeeRepository;

  @Autowired
  private ImportHistoryDomainService importHistoryDomainService;

  @Override
  public List<String> createPurchaseOrder(PurchaseOrderCreateCommand command) {

    // 插入导入记录
    EmployeeInfo emp = employeeRepository.getEmployeeInfo(command.getUserId());
    ImportHistoryAggregate importData = importHistoryDomainService.createImportHistory(
        command.getFileName(), command.getUserId(), emp.getUserName());
    // 调用商品接口先过滤无效商品
    checkPurchaseOrder(command);

    List<PurchaseOrderAggregate> successPurchaseOrder = new ArrayList<>();
    // 例如: 为每个组织创建一个采购单
    List<PurchaseOrderAggregate> purchaseOrderAggregates = createPurchaseOrderAggregate(
        command);
    // 使用创建采购单责任链 来创建采购单
    // 将全部报错收集之后统一抛出错误，提升操作人员修改文档的效率
    List<String> errorMsgg = new ArrayList<>();
    for (PurchaseOrderAggregate purchaseOrderAggregate : purchaseOrderAggregates) {
      // 创建采购单
      // 外层是根据门店拆分的采购单
      try {
        HandlerContext handlerContext = new HandlerContext();
        handlerContext.setAggregates(Collections.singletonList(purchaseOrderAggregate));
        handlerContext.setSystemCode(systemCode);
        handlerContext.setTradeMode(tradeMode);
        createPurchaseOrderHandler.initHandler().execute(handlerContext);
        // 处理成功的采购单号
        successPurchaseOrder.addAll(handlerContext.getAggregates());
      } catch (Exception e) {
        errorMsgg.add(e.getMessage());
      }
    }
    // 日志
    List<String> orderNos = importDataLog(successPurchaseOrder, command.getUserId());
    // 更新导入结果


    if (CollUtil.isNotEmpty(errorMsgg)) {
      throw new RuntimeException(CollUtil.join(errorMsgg, ",\n"));
    }
    return orderNos;
  }


  private void saveProcessResult()
  {
    List<ImportPurchaseProcessModel> data=new ArrayList<>();

  }

  @Override
  public PurchaseOrderQueryResult queryPurchaseOrders(PurchaseOrderQueryCommand command) {

    // 设置默认分页参数
    if (command.getCurrentPage() == null || command.getCurrentPage() < 1) {
      command.setCurrentPage(1);
    }
    if (command.getPageSize() == null || command.getPageSize() < 1) {
      command.setPageSize(10);
    }
    // 处理门店和公司查询 权限过滤
    if(StrUtil.isNotBlank(command.getOrganizationCode())){
      command.setStoreCodeList(new ArrayList<>());
      command.getStoreCodeList().add(command.getOrganizationCode());
    }
    if(StrUtil.isNotBlank(command.getCompanyCode())){
      command.setCompanyCodeList(new ArrayList<>());
      command.getCompanyCodeList().add(command.getCompanyCode());
    }
    // 调用仓储层查询采购单列表
    return purchaseOrderRepository.queryPurchaseOrders(command);
  }

  @Override
  public boolean updateAutoDeductionConfig(UpdateAutoDeductionCommand command) {
    // 参数校验

    // 查询采购单是否存在
    PurchaseOrderAggregate order = purchaseOrderRepository.findByPurchaseOrderId(
        command.getPurchaseOrderId());
    if (order == null) {
      throw new IllegalArgumentException("采购单不存在: " + command.getPurchaseOrderId());
    }
    if (order.getState() != PurchaseOrderStatusEnum.WAIT_CONFIRM) {
      throw new IllegalArgumentException("只有待确认的采购单才能修改");
    }

    // 调用仓储层更新自动扣款配置
    boolean updateFlag = purchaseOrderRepository.updateAutoDeductionConfig(
        command.getPurchaseOrderId(),
        command.getIsAutoPayment(),
        command.getOverduePaymentTime(),
        command.getUserId());
    if (updateFlag) { // 记录日志
      addModifyConfigLog(command.getUserId(), order.getPurchaseOrderNo().getPurchaseOrderNo(),
          order.getAutoPayment(), order.getOverduePaymentTime(), command.getIsAutoPayment(),
          command.getOverduePaymentTime());
    }
    return updateFlag;
  }

  @Override
  public List<String> batchRejectPurchaseOrder(BatchRejectPurchaseOrderCommand command) {
    // 参数校验
    if (command == null) {
      throw new IllegalArgumentException("批量驳回命令不能为空");
    }
    List<String> rejectPurchaseOrderNos = new ArrayList<>();
    // 如果指定了采购单ID列表，则根据ID列表驳回
    if (CollUtil.isNotEmpty(command.getPurchaseOrderIds())) {
      rejectPurchaseOrderNos.addAll(purchaseOrderRepository.batchRejectByIds(
          command.getPurchaseOrderIds(),
          command.getRejectReason(),
          command.getUserId()));
    } else {
      // 如果没有指定采购单ID列表，则根据查询条件查询并驳回
      PurchaseOrderQueryCommand queryCommand = new PurchaseOrderQueryCommand();
      queryCommand.setCompanyCode(command.getCompanyCode());
      queryCommand.setOrganizationCode(command.getOrganizationCode());
      queryCommand.setPurchaseOrderLabel(command.getPurchaseOrderLabel());
      queryCommand.setState(command.getState());
      queryCommand.setPurchaseOrderNo(command.getPurchaseOrderNo());
      queryCommand.setParentPurchaseOrderNo(command.getParentPurchaseOrderNo());
      queryCommand.setRelatedOrderNo(command.getRelatedOrderNo());
      queryCommand.setOrderStartTime(command.getOrderStartTime());
      queryCommand.setOrderEndTime(command.getOrderEndTime());
      // 只能处理待确认的采购单
      queryCommand.setState(PurchaseOrderStatusEnum.WAIT_CONFIRM.getCode());

      // 处理门店和公司查询 权限过滤
      if(StrUtil.isNotBlank(command.getOrganizationCode())){
        command.setStoreCodeList(new ArrayList<>());
        command.getStoreCodeList().add(command.getOrganizationCode());
      }
      if(StrUtil.isNotBlank(command.getCompanyCode())){
        command.setCompanyCodeList(new ArrayList<>());
        command.getCompanyCodeList().add(command.getCompanyCode());
      }
      rejectPurchaseOrderNos.addAll(purchaseOrderRepository.batchRejectByCondition(
          queryCommand,
          command.getRejectReason(),
          command.getUserId()));
    }
    addRejectLog(rejectPurchaseOrderNos, command.getUserId());
    return rejectPurchaseOrderNos;

  }

  @Override
  public PurchaseOrderInfoQueryResult queryPurchaseOrderInfo(
      PurchaseOrderInfoQueryCommand command) {

    // 查询采购单聚合根
    PurchaseOrderAggregate purchaseOrderAggregate = purchaseOrderRepository.findByPurchaseOrderNo(
        command.getPurchaseOrderNo());
    if (purchaseOrderAggregate == null) {
      throw new IllegalArgumentException("采购单不存在: " + command.getPurchaseOrderNo());
    }

    // 查询采购单相关的操作日志
    BizLogSearchCommand logQueryCommand = new BizLogSearchCommand();
    logQueryCommand.setBizNo(command.getPurchaseOrderNo());
    List<BizLogInfo> operationLogs = bizLogService.logSearch(logQueryCommand);

    // 构建查询结果
    PurchaseOrderInfoQueryResult result = new PurchaseOrderInfoQueryResult();
    result.setPurchaseOrderAggregate(purchaseOrderAggregate);
    result.setOperationLogs(convertToBizLogModel(operationLogs));

    return result;
  }

  @Override
  public String copyPurchaseOrder(CopyPurchaseOrderCommand command) {
    // 查询源采购单
    PurchaseOrderAggregate sourcePurchaseOrder = purchaseOrderRepository.findByPurchaseOrderId(
        command.getSourcePurchaseOrderId());
    if (sourcePurchaseOrder == null) {
      throw new IllegalArgumentException("源采购单不存在: " + command.getSourcePurchaseOrderId());
    }
    String purchaseOrderNo = purchaseOrderRepository.copyPurchaseOrder(
        sourcePurchaseOrder,
        command.getUserId());
    addCreateLog(Collections.singletonList(purchaseOrderNo), command.getUserId());
    // 调用仓储层复制采购单
    return purchaseOrderNo;
  }

  @Override
  public List<String> batchReConfirmOrder(BatchReConfirmCommand command) {

    // 处理门店和公司查询 权限过滤
    if(StrUtil.isNotBlank(command.getOrganizationCode())){
      command.setStoreCodeList(new ArrayList<>());
      command.getStoreCodeList().add(command.getOrganizationCode());
    }
    if(StrUtil.isNotBlank(command.getCompanyCode())){
      command.setCompanyCodeList(new ArrayList<>());
      command.getCompanyCodeList().add(command.getCompanyCode());
    }
    // 获取主表信息
    List<PurchaseOrderAggregate> orderAggregates = purchaseOrderRepository.getReConfirmByCondition(
        command);
    if (CollUtil.isEmpty(orderAggregates)) {
      return new ArrayList<>();
    }
    // 获取商品信息
    List<String> purchaseOrders = orderAggregates.stream().map(item -> {
      return item.getPurchaseOrderNo().getPurchaseOrderNo();
    }).collect(
        Collectors.toList());
    List<PurchaseOrderDetailAggregate> items = purchaseOrderDetailRepository.findByPurchaseOrderNos(
        purchaseOrders);
    // 将采购单明细组装到采购单中
    assembleDetailsIntoPurchaseOrders(orderAggregates, items, command.getUserId());
    List<String> successPurchaseOrder = new ArrayList<>();
    // 处理流程
    List<String> errorMsg = new ArrayList<>();
    for (PurchaseOrderAggregate purchaseOrderAggregate : orderAggregates) {
      try {
        HandlerContext handlerContext = new HandlerContext();
        handlerContext.setAggregates(Collections.singletonList(purchaseOrderAggregate));
        handlerContext.setSystemCode(systemCode);
        handlerContext.setTradeMode(tradeMode);
        purchaseOrderAggregate.setConfirmBy(new OperationUser(command.getUserId()));
        purchaseOrderAggregate.setConfirmTime(LocalDateTime.now());
        reCreateOrderHandler.initHandler().execute(handlerContext);
        if (purchaseOrderAggregate.getState().equals(PurchaseOrderStatusEnum.WAIT_CONFIRM) &&
            purchaseOrderAggregate.getSettlementStatus()
                .equals(SettlementStatusEnum.SETTLEMENT_FAILED)) {
          errorMsg.add(String.format("%s:%s",
              purchaseOrderAggregate.getPurchaseOrderNo().getPurchaseOrderNo(),
              purchaseOrderAggregate.getMsg()));
        } else {
          successPurchaseOrder.add(
              purchaseOrderAggregate.getPurchaseOrderNo().getPurchaseOrderNo());
        }
      } catch (Exception e) {
        errorMsg.add(
            String.format("%s:%s", purchaseOrderAggregate.getPurchaseOrderNo().getPurchaseOrderNo(),
                e.getMessage()));
      }
    }
    addReConfirmLog(orderAggregates, command.getUserId());
    if (CollUtil.isNotEmpty(errorMsg)) {
      throw new RuntimeException(String.join(",", errorMsg));
    }
    return successPurchaseOrder;
  }


  private void assembleDetailsIntoPurchaseOrders(
      List<PurchaseOrderAggregate> purchaseOrderAggregates,
      List<PurchaseOrderDetailAggregate> items, String userId) {
    if (CollUtil.isEmpty(items)) {
      return;
    }

    Map<String, List<PurchaseOrderDetailAggregate>> groupedByNo = items.stream()
        .collect(Collectors.groupingBy(PurchaseOrderDetailAggregate::getPurchaseOrderNo));

    // 将明细组装到对应的采购单中
    for (PurchaseOrderAggregate purchaseOrder : purchaseOrderAggregates) {
      // 设置初始值 ，跳过检查
      purchaseOrder.setAutoConfirm(Boolean.TRUE);
      purchaseOrder.setSettlementStatus(SettlementStatusEnum.WAIT_SETTLEMENT);
      purchaseOrder.setConfirmBy(new OperationUser(userId, null, null));
      purchaseOrder.setConfirmTime(LocalDateTime.now());
      String purchaseOrderNo = purchaseOrder.getPurchaseOrderNo().getPurchaseOrderNo();
      if (!groupedByNo.containsKey(purchaseOrderNo)) {
        continue;
      }
      // 处理明细
      List<PurchaseOrderDetailAggregate> itemAggregate = groupedByNo.get(
          purchaseOrderNo);
      purchaseOrder.setPurchaseOrderDetailList(
          itemAggregate.stream().map(this::toDetailModel).collect(Collectors.toList()));
    }
  }


  private CreatePurchaseOrderDetailModel toDetailModel(PurchaseOrderDetailAggregate aggregate) {
    CreatePurchaseOrderDetailModel result = new CreatePurchaseOrderDetailModel();
    result.setStatus("0");
    result.setErpCode(aggregate.getErpCode());
    result.setCommoditySpec(aggregate.getCommoditySpec());
    result.setQty(aggregate.getCommodityCount());
    result.setEx_msg("");
    return result;
  }


  /**
   * 校验采购单信息基础校验 主要是为了校验调用的数据是否符合基本非空原则
   *
   * @param command 采购单创建命令
   */
  private void checkPurchaseOrder(PurchaseOrderCreateCommand command) {
    // 校验 采购商品中信息是否完整
    if (CollUtil.isEmpty(command.getItems())) {
      throw new RuntimeException("采购商品不能为空");
    }
    Integer rowId = 1;
    // 商品的 编码 数量 和 商家都不能为空
    List<Integer> errRowIds = new java.util.ArrayList<>();
    for (PurchaseOrderDetailModel item : command.getItems()) {
      rowId++;
      if (StrUtil.isBlank(item.getErpCode()) || StrUtil.isBlank(item.getOrganizationCode())
          || StrUtil.isBlank(item.getQty()) || Long.parseLong(item.getQty()) <= 0) {
        errRowIds.add(rowId);
      }
    }
    if (CollUtil.isNotEmpty(errRowIds)) {
      String errMsg = "第" + CollUtil.join(errRowIds, ",") + "行商品信息无效请核对后再试。";
      throw new RuntimeException(errMsg);
    }
  }

  /**
   * 基础拆分，根据门店将传入的采购商品信息分成多个采购单聚合根
   *
   * @param command 采购单创建命令
   * @return 采购单聚合根列表
   */
  private List<PurchaseOrderAggregate> createPurchaseOrderAggregate(
      PurchaseOrderCreateCommand command) {
    List<PurchaseOrderAggregate> purchaseOrderAggregates = new java.util.ArrayList<>();
    //  根据门店将传入的采购商品信息分成多个采购单聚合根
    Map<String, List<PurchaseOrderDetailModel>> groupedByStore = command.getItems().stream()
        .collect(Collectors.groupingBy(PurchaseOrderDetailModel::getOrganizationCode));
    for (Map.Entry<String, List<PurchaseOrderDetailModel>> entry : groupedByStore.entrySet()) {
      String storeCode = entry.getKey();
      List<PurchaseOrderDetailModel> items = entry.getValue();
      PurchaseOrderAggregate purchaseOrderAggregate = PurchaseOrderAggregate.build(items,
          command.getIsAutoPayment(), command.getOverduePaymentTime(), command.getAutoConfirm(),
          command.getUserId(), storeCode);
      purchaseOrderAggregates.add(purchaseOrderAggregate);
    }
    return purchaseOrderAggregates;
  }

  private void addRejectLog(List<String> purchaseOrderNos, String userId) {
    List<BizLogInfo> logs = new ArrayList<>();
    for (String purchaseOrderNo : purchaseOrderNos) {
      BizLogInfo bizLogInfo = new BizLogInfo();
      bizLogInfo.setBizNo(purchaseOrderNo);
      bizLogInfo.setBizScene(BizScene.PURCHASE_ORDER);
      bizLogInfo.setBizAction(BizAction.ORDER_CANCEL);
      bizLogInfo.setBizResultDesc("采购单已取消");
      bizLogInfo.setOperateTime(LocalDateTime.now());
      bizLogInfo.setOperatorId(userId);
      bizLogInfo.setExtensionNum1(PurchaseOrderStatusEnum.CANCELED.getCode());
      logs.add(bizLogInfo);
    }
    bizLogService.log(logs);
  }

  private void addCreateLog(List<String> purchaseOrderNos, String userId) {
    List<BizLogInfo> logs = new ArrayList<>();
    for (String purchaseOrderNo : purchaseOrderNos) {
      BizLogInfo bizLogInfo = new BizLogInfo();
      bizLogInfo.setBizNo(purchaseOrderNo);
      bizLogInfo.setBizAction(BizAction.ORDER_CREATE);
      bizLogInfo.setBizScene(BizScene.PURCHASE_ORDER);
      bizLogInfo.setBizResultDesc("导入铺货明细生成采购单，采购单号：" + purchaseOrderNo);
      bizLogInfo.setOperateTime(LocalDateTime.now());
      bizLogInfo.setOperatorId(userId);
      bizLogInfo.setExtensionNum1(PurchaseOrderStatusEnum.WAIT_CONFIRM.getCode());
      logs.add(bizLogInfo);
    }
    bizLogService.log(logs);
  }

  private List<String> importDataLog(List<PurchaseOrderAggregate> orders, String userId) {
    List<String> creatOrders = orders.stream().map(order -> {
      return order.getPurchaseOrderNo().getPurchaseOrderNo();
    }).collect(
        Collectors.toList());
    addCreateLog(creatOrders, userId);
    // 确认日志
    addReConfirmLog(orders, userId);
    return creatOrders;
  }


  private void addReConfirmLog(List<PurchaseOrderAggregate> aggregates, String userId) {
    List<BizLogInfo> logs = new ArrayList<>();
    for (PurchaseOrderAggregate aggregate : aggregates) {
      if (aggregate.getState().equals(PurchaseOrderStatusEnum.CONFIRMED) &&
          aggregate.getSettlementStatus()
              .equals(SettlementStatusEnum.SETTLEMENT)) {
        BizLogInfo bizLogInfo = new BizLogInfo();
        bizLogInfo.setBizNo(aggregate.getPurchaseOrderNo().getPurchaseOrderNo());
        bizLogInfo.setBizAction(BizAction.ORDER_CONFIRM);
        bizLogInfo.setBizScene(BizScene.PURCHASE_ORDER);
        bizLogInfo.setBizResultDesc(
            "订单已确认，生成待支付订单，订单号：" + aggregate.getRelatedOrderNo());
        bizLogInfo.setOperateTime(LocalDateTime.now());
        bizLogInfo.setOperatorId(userId);
        bizLogInfo.setExtensionNum1(PurchaseOrderStatusEnum.CONFIRMED.getCode());
        logs.add(bizLogInfo);
      } else {
        BizLogInfo bizLogInfo = new BizLogInfo();
        bizLogInfo.setBizNo(aggregate.getPurchaseOrderNo().getPurchaseOrderNo());
        bizLogInfo.setBizAction(BizAction.ORDER_CONFIRM);
        bizLogInfo.setBizScene(BizScene.PURCHASE_ORDER);
        bizLogInfo.setBizResultDesc(aggregate.getMsg());
        bizLogInfo.setOperateTime(LocalDateTime.now());
        bizLogInfo.setOperatorId(userId);
        bizLogInfo.setExtensionNum1(PurchaseOrderStatusEnum.WAIT_CONFIRM.getCode());
        logs.add(bizLogInfo);
      }
    }
    bizLogService.log(logs);
  }

  private void addReConfirmErrorLog(List<PurchaseOrderAggregate> aggregates, String userId) {

    List<BizLogInfo> logs = new ArrayList<>();
    for (PurchaseOrderAggregate aggregate : aggregates) {
      if (aggregate.getState().equals(PurchaseOrderStatusEnum.WAIT_CONFIRM) &&
          aggregate.getSettlementStatus()
              .equals(SettlementStatusEnum.SETTLEMENT_FAILED)) {

      } else {

      }

    }
    bizLogService.log(logs);
  }

  private List<BizLogModel> convertToBizLogModel(List<BizLogInfo> bizLogInfos) {
    List<BizLogModel> bizLogModels = new ArrayList<>();
    // 获取操作人姓名
    List<String> userIds = bizLogInfos.stream().map(BizLogInfo::getOperatorId).distinct()
        .collect(Collectors.toList());

    List<EmployeeInfo> employeeInfos = employeeRepository.getEmployeeInfo(userIds);
    for (BizLogInfo bizLogInfo : bizLogInfos) {
      BizLogModel bizLogModel = new BizLogModel();
      bizLogModel.setOperationId(bizLogInfo.getBizNo());
      bizLogModel.setOperationType(bizLogInfo.getBizAction().bizAction());
      bizLogModel.setOperationDesc(bizLogInfo.getBizResultDesc());
      bizLogModel.setOperationTime(LocalDateTimeUtil.format(bizLogInfo.getOperateTime(),
          DatePattern.NORM_DATETIME_PATTERN));
      bizLogModel.setOperatorId(bizLogInfo.getOperatorId());
      bizLogModel.setStatus(bizLogInfo.getExtensionNum1());
      bizLogModel.setOperatorName(
          employeeInfos.stream()
              .filter(employeeInfo -> employeeInfo.getUserId().equals(bizLogInfo.getOperatorId()))
              .findFirst().orElse(new EmployeeInfo()).getUserName());
      if("system".equals(bizLogInfo.getOperatorId())){
        bizLogModel.setOperatorName("system");
      }
      bizLogModels.add(bizLogModel);
    }

    return bizLogModels;
  }

  private void addModifyConfigLog(String userId, String purchaseOrderNo, Boolean orgAutoPayment,
      Integer orgOverduePaymentTime,
      Boolean autoPayment, Integer overduePaymentTime

  ) {

    BizLogInfo bizLogInfo = new BizLogInfo();
    bizLogInfo.setBizNo(purchaseOrderNo);
    bizLogInfo.setBizAction(BizAction.ORDER_PAY_UPDATE);
    bizLogInfo.setBizScene(BizScene.PURCHASE_ORDER);
    bizLogInfo.setBizResultDesc(
        String.format("修改支付配置，自动扣款:%s->%s ,支付时间:%s->%s", orgAutoPayment, autoPayment,
            orgOverduePaymentTime, overduePaymentTime));
    bizLogInfo.setOperateTime(LocalDateTime.now());
    bizLogInfo.setOperatorId(userId);
    bizLogInfo.setExtensionNum1(PurchaseOrderStatusEnum.WAIT_CONFIRM.getCode());
    bizLogService.log(bizLogInfo);
  }

  @Override
  public PurchaseOrderAggregate updatePurchaseOrderCompleted(UpdatePurchaseOrderCompletedCommand command) {
    // 参数校验
    if (command == null) {
      throw new IllegalArgumentException("更新采购单完成状态命令不能为空");
    }
    // 查询采购单
    PurchaseOrderAggregate purchaseOrderAggregate = purchaseOrderRepository.findByPurchaseOrderNo(command.getPurchaseOrderNo());
    if (purchaseOrderAggregate == null) {
      throw new IllegalArgumentException("采购单不存在: " + command.getPurchaseOrderNo());
    }
    // 更新采购单状态为已完成
    purchaseOrderAggregate.markAsCompleted();

    // 保存更新后的采购单
    PurchaseOrderAggregate updatedAggregate = purchaseOrderRepository.updatePurchaseOrderCompleted(purchaseOrderAggregate, command.getUserId());

    // 添加完成日志
    addCompletedLog(command.getPurchaseOrderNo(), command.getUserId());

    return updatedAggregate;
  }

  /**
   * 检查采购单是否可以更新为已完成状态
   */
  private boolean canUpdateToCompleted(PurchaseOrderAggregate purchaseOrder) {
    // 只有已确认状态的采购单才能更新为已完成
    return PurchaseOrderStatusEnum.CONFIRMED.equals(purchaseOrder.getState());
  }

  /**
   * 添加采购单完成日志
   */
  private void addCompletedLog(String purchaseOrderNo, String userId) {
    BizLogInfo bizLogInfo = new BizLogInfo();
    bizLogInfo.setBizNo(purchaseOrderNo);
    bizLogInfo.setBizAction(BizAction.ORDER_COMPLETE);
    bizLogInfo.setBizScene(BizScene.PURCHASE_ORDER);
    bizLogInfo.setBizResultDesc("采购单商品全部发货，状态变更为已完成");
    bizLogInfo.setOperateTime(LocalDateTime.now());
    bizLogInfo.setOperatorId(userId);
    bizLogInfo.setExtensionNum1(PurchaseOrderStatusEnum.FINISHED.getCode());
    bizLogService.log(bizLogInfo);
  }

  @Override
  public PurchaseOrderAggregate updatePurchaseOrderCancel(UpdatePurchaseOrderCancelCommand command) {
    // 参数校验
    if (command == null) {
      throw new IllegalArgumentException("取消采购单命令不能为空");
    }
    if (StrUtil.isBlank(command.getPurchaseOrderNo())) {
      throw new IllegalArgumentException("采购单号不能为空");
    }

    // 查询采购单
    PurchaseOrderAggregate purchaseOrderAggregate = purchaseOrderRepository.findByPurchaseOrderNo(command.getPurchaseOrderNo());
    if (purchaseOrderAggregate == null) {
      throw new IllegalArgumentException("采购单不存在: " + command.getPurchaseOrderNo());
    }

    // 检查采购单状态是否可以取消
    if (!canCancel(purchaseOrderAggregate)) {
      throw new IllegalArgumentException("采购单当前状态不允许取消");
    }

    // 更新采购单状态为已取消
    purchaseOrderAggregate.markAsCanceled();

    // 保存更新后的采购单
    PurchaseOrderAggregate updatedAggregate = purchaseOrderRepository.updatePurchaseOrderCancel(purchaseOrderAggregate, command.getUserId());

    // 添加取消日志
    addCancelLog(command.getPurchaseOrderNo(), command.getUserId(), command.getCancelReason());

    return updatedAggregate;
  }

  /**
   * 检查采购单是否可以取消
   */
  private boolean canCancel(PurchaseOrderAggregate purchaseOrder) {
    // 只有待确认和已确认状态的采购单才能取消
    return PurchaseOrderStatusEnum.WAIT_CONFIRM.equals(purchaseOrder.getState())
        || PurchaseOrderStatusEnum.CONFIRMED.equals(purchaseOrder.getState());
  }

  /**
   * 添加采购单取消日志
   */
  private void addCancelLog(String purchaseOrderNo, String userId, String cancelReason) {
    BizLogInfo bizLogInfo = new BizLogInfo();
    bizLogInfo.setBizNo(purchaseOrderNo);
    bizLogInfo.setBizAction(BizAction.ORDER_CANCEL);
    bizLogInfo.setBizScene(BizScene.PURCHASE_ORDER);
    bizLogInfo.setBizResultDesc("采购单已取消" + (StrUtil.isNotBlank(cancelReason) ? "，原因：" + cancelReason : ""));
    bizLogInfo.setOperateTime(LocalDateTime.now());
    bizLogInfo.setOperatorId(userId);
    bizLogInfo.setExtensionNum1(PurchaseOrderStatusEnum.CANCELED.getCode());
    bizLogService.log(bizLogInfo);
  }

}
