package com.yxt.purchase.domain.repository;

import com.yxt.purchase.domain.PurchaseOrderAggregate;


/**
 * 采购单仓储接口
 */
public interface PurchaseOrderRepository {

    /**
     * 保存采购单
     *
     * @param purchaseOrderAggregate 采购单聚合根
     */
    void savePurchaseOrder(PurchaseOrderAggregate purchaseOrderAggregate);

    /**
     * 获取采购单号
     * 采购单编号规则为 PO + yyyyMMdd+ storeCode + 当日6位门店序列号
     * @param storeCode 门店编码
     */
    Long getPurchaseOrderNo(String storeCode);

    /**
     * 将采购单状态设置为失败
     *
     * @param purchaseOrderNo 采购单号
     * @param failReason 失败原因
     * @param updatedBy 更新人
     * @return 是否设置成功
     */
    boolean setPurchaseOrderStateToFail(String purchaseOrderNo, String failReason, String updatedBy);


}
