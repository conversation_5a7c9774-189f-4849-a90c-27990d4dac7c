package com.yxt.purchase.domain.repository;

import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.domain.command.PurchaseOrderQueryCommand;
import com.yxt.purchase.domain.service.PurchaseOrderQueryResult;
import java.util.List;

/**
 * 采购单仓储接口
 */
public interface PurchaseOrderRepository {

  /**
   * 保存采购单
   *
   * @param purchaseOrderAggregate 采购单聚合根
   */
  void savePurchaseOrder(PurchaseOrderAggregate purchaseOrderAggregate);

  /**
   * 获取采购单号 采购单编号规则为 PO + yyyyMMdd+ storeCode + 当日6位门店序列号
   *
   * @param storeCode 门店编码
   */
  Long getPurchaseOrderNo(String storeCode);

  /**
   * 将采购单状态设置为失败
   *
   * @param purchaseOrderNo 采购单号
   * @param failReason      失败原因
   * @param updatedBy       更新人
   * @return 是否设置成功
   */
  void setPurchaseOrderStateToFail(String purchaseOrderNo, String failReason, String updatedBy);

  /**
   * 查询采购单列表
   *
   * @param command 查询命令
   * @return 采购单聚合根列表和总数
   */
  PurchaseOrderQueryResult queryPurchaseOrders(PurchaseOrderQueryCommand command);

  /**
   * 检查采购单是否存在
   *
   * @param purchaseOrderNo 采购单号
   * @return 是否存在
   */
  boolean checkPurchaseOrderExists(String purchaseOrderNo);

  /**
   * 更新采购单自动扣款配置
   *
   * @param purchaseOrderId 采购单号
   * @param isAutoPayment 是否自动支付
   * @param overduePaymentTime 超时支付时间
   * @param updatedBy 更新人
   * @return 是否更新成功
   */
  boolean updateAutoDeductionConfig(String purchaseOrderId, Boolean isAutoPayment, Integer overduePaymentTime, String updatedBy);

  /**
   * 根据ID列表批量驳回采购单
   *
   * @param purchaseOrderIds 采购单ID列表
   * @param rejectReason 驳回原因
   * @param updatedBy 更新人
   * @return 驳回成功的采购单号列表
   */
  List<String> batchRejectByIds(List<String> purchaseOrderIds, String rejectReason, String updatedBy);

  /**
   * 根据查询条件批量驳回采购单
   *
   * @param queryCommand 查询命令
   * @param rejectReason 驳回原因
   * @param updatedBy 更新人
   * @return 驳回成功的采购单号列表
   */
  List<String> batchRejectByCondition(PurchaseOrderQueryCommand queryCommand, String rejectReason, String updatedBy);

}
