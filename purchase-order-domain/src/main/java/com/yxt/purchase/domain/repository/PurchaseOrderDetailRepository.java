package com.yxt.purchase.domain.repository;

import com.yxt.purchase.domain.PurchaseOrderDetailAggregate;
import java.util.List;
import java.util.Optional;

/**
 * 采购商品明细仓储接口
 */
public interface PurchaseOrderDetailRepository {

  /**
   * 保存采购商品明细
   *
   * @param detail 采购商品明细聚合根
   * @return 保存后的采购商品明细聚合根
   */
  PurchaseOrderDetailAggregate save(PurchaseOrderDetailAggregate detail);

  /**
   * 根据ID查询采购商品明细
   *
   * @param id 明细ID
   * @return 采购商品明细聚合根
   */
  Optional<PurchaseOrderDetailAggregate> findById(Long id);

  /**
   * 根据采购单号查询采购商品明细列表
   *
   * @param purchaseOrderNo 采购单号
   * @return 采购商品明细聚合根列表
   */
  List<PurchaseOrderDetailAggregate> findByPurchaseOrderNo(String purchaseOrderNo);

  /**
   * 更新采购商品明细
   *
   * @param detail 采购商品明细聚合根
   * @return 更新后的采购商品明细聚合根
   */
  PurchaseOrderDetailAggregate update(PurchaseOrderDetailAggregate detail);

  /**
   * 根据ID删除采购商品明细
   *
   * @param id 明细ID
   * @return 是否删除成功
   */
  boolean deleteById(Long id);

  /**
   * 根据采购单号删除采购商品明细
   *
   * @param purchaseOrderNo 采购单号
   * @return 是否删除成功
   */
  boolean deleteByPurchaseOrderNo(String purchaseOrderNo);

  /**
   * 根据采购单号和状态查询采购商品明细列表，并进行分页
   *
   * @param purchaseOrderNo 采购单号
   * @param status 状态，可以为空
   * @param currentPage 当前页码
   * @param pageSize 每页大小
   * @return 采购商品明细聚合根列表
   */
  List<PurchaseOrderDetailAggregate> findByPurchaseOrderNoAndStatus(String purchaseOrderNo, String status, Integer currentPage, Integer pageSize);

  /**
   * 根据采购单号和状态统计采购商品明细数量
   *
   * @param purchaseOrderNo 采购单号
   * @param status 状态，可以为空
   * @return 采购商品明细数量
   */
  long countByPurchaseOrderNoAndStatus(String purchaseOrderNo, String status);

  /**
   *  检查商品是否符合采购要求 ，主要是从商品有效性和库存数量限制上检查
   * @param storeCode 门店编码
   * @param erpCode 商品编码
   * @param qty 数量
   * @return 是否符合采购要求
   */
  boolean checkCommodityIsValid(String storeCode,String erpCode,String qty);

}