package com.yxt.purchase.domain.entity;

import com.yxt.purchase.enums.AsyncTaskStatusEnum;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 异步任务实体
 */
@Data
public class AsyncTask {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务类型
     */
    private String taskType;
    
    /**
     * 任务状态
     */
    private AsyncTaskStatusEnum status;
    
    /**
     * 任务进度（0-100）
     */
    private Integer progress;
    
    /**
     * 任务结果
     */
    private String result;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime completedTime;
    
    /**
     * 任务参数
     */
    private String taskParams;
}
