package com.yxt.purchase.domain.service;

import com.yxt.purchase.domain.command.PurchaseOrderCreateCommand;
import com.yxt.purchase.domain.entity.PurchaseOrder;
import java.util.List;
import java.util.Optional;

/**
 * 采购单领域服务接口
 */
public interface PurchaseOrderDomainService {


    /**
     * 创建采购单
     *
     * @param command 创建采购单命令
     */
    List<String>  createPurchaseOrder(PurchaseOrderCreateCommand command);

}
