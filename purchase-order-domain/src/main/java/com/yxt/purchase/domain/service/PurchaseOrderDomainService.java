package com.yxt.purchase.domain.service;

import com.yxt.purchase.domain.PurchaseOrderAggregate;
import com.yxt.purchase.domain.command.BatchReConfirmCommand;
import com.yxt.purchase.domain.command.BatchRejectPurchaseOrderCommand;
import com.yxt.purchase.domain.command.CopyPurchaseOrderCommand;
import com.yxt.purchase.domain.command.PurchaseOrderCreateCommand;
import com.yxt.purchase.domain.command.PurchaseOrderInfoQueryCommand;
import com.yxt.purchase.domain.command.PurchaseOrderQueryCommand;
import com.yxt.purchase.domain.command.UpdateAutoDeductionCommand;
import com.yxt.purchase.domain.command.UpdatePurchaseOrderCompletedCommand;
import com.yxt.purchase.domain.entity.PurchaseOrderInfoQueryResult;
import com.yxt.purchase.domain.entity.PurchaseOrderQueryResult;
import java.util.List;

/**
 * 采购单领域服务接口
 */
public interface PurchaseOrderDomainService {


  /**
   * 创建采购单
   *
   * @param command 创建采购单命令
   */
  List<String> createPurchaseOrder(PurchaseOrderCreateCommand command);

  /**
   * 查询采购单列表
   *
   * @param command 查询采购单命令
   * @return 采购单聚合根列表和总数
   */
  PurchaseOrderQueryResult queryPurchaseOrders(PurchaseOrderQueryCommand command);

  /**
   * 更新采购单自动扣款配置
   *
   * @param command 更新自动扣款配置命令
   * @return 是否更新成功
   */
  boolean updateAutoDeductionConfig(UpdateAutoDeductionCommand command);

  /**
   * 批量驳回采购单
   *
   * @param command 批量驳回采购单命令
   * @return 驳回成功的采购单号列表
   */
  List<String> batchRejectPurchaseOrder(BatchRejectPurchaseOrderCommand command);

  /**
   * 查询采购单详情
   *
   * @param command 采购单详情查询命令
   * @return 采购单详情查询结果
   */
  PurchaseOrderInfoQueryResult queryPurchaseOrderInfo(PurchaseOrderInfoQueryCommand command);

  /**
   * 复制采购单
   *
   * @param command 复制采购单命令
   * @return 新创建的采购单号
   */
  String copyPurchaseOrder(CopyPurchaseOrderCommand command);


  /**
   * 批量驳回采购单
   *
   * @param command 批量驳回采购单命令
   * @return 驳回成功的采购单号列表
   */
  List<String> batchReConfirmOrder(BatchReConfirmCommand command);


}
