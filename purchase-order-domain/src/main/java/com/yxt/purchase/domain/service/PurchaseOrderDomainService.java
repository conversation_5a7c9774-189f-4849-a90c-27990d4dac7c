package com.yxt.purchase.domain.service;

import com.yxt.purchase.domain.command.BatchRejectPurchaseOrderCommand;
import com.yxt.purchase.domain.command.PurchaseOrderCreateCommand;
import com.yxt.purchase.domain.command.PurchaseOrderQueryCommand;
import com.yxt.purchase.domain.command.UpdateAutoDeductionCommand;
import java.util.List;

/**
 * 采购单领域服务接口
 */
public interface PurchaseOrderDomainService {


    /**
     * 创建采购单
     *
     * @param command 创建采购单命令
     */
    List<String> createPurchaseOrder(PurchaseOrderCreateCommand command);

    /**
     * 查询采购单列表
     *
     * @param command 查询采购单命令
     * @return 采购单聚合根列表和总数
     */
    PurchaseOrderQueryResult queryPurchaseOrders(PurchaseOrderQueryCommand command);

    /**
     * 更新采购单自动扣款配置
     *
     * @param command 更新自动扣款配置命令
     * @return 是否更新成功
     */
    boolean updateAutoDeductionConfig(UpdateAutoDeductionCommand command);

}
