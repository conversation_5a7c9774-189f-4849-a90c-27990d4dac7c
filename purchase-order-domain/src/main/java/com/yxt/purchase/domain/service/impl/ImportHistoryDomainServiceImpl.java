package com.yxt.purchase.domain.service.impl;

import cn.hutool.core.util.StrUtil;
import com.yxt.purchase.domain.ImportHistoryAggregate;
import com.yxt.purchase.domain.command.ImportHistoryQueryCommand;
import com.yxt.purchase.domain.repository.ImportHistoryRepository;
import com.yxt.purchase.domain.service.ImportHistoryDomainService;
import com.yxt.purchase.enums.ImportStatusEnum;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 导入历史领域服务实现类
 */
@Slf4j
@Service
public class ImportHistoryDomainServiceImpl implements ImportHistoryDomainService {
    
    @Autowired
    private ImportHistoryRepository importHistoryRepository;
    
    @Override
    public List<ImportHistoryAggregate> queryImportHistory(ImportHistoryQueryCommand command) {
        // 参数校验
        if (command == null) {
            throw new IllegalArgumentException("查询命令不能为空");
        }
        if (StrUtil.isBlank(command.getUserId())) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        
        // 调用仓储层查询导入历史
        return importHistoryRepository.queryImportHistory(command);
    }
    
    @Override
    public ImportHistoryAggregate createImportHistory(String fileName, String userId, String userName) {
        // 创建导入历史聚合根
        ImportHistoryAggregate aggregate = new ImportHistoryAggregate();
        aggregate.setFileName(fileName);
        aggregate.setImportStatus(ImportStatusEnum.PROCESSING);
        aggregate.setImportTime(LocalDateTime.now());
        aggregate.setImportBy(userId);
        aggregate.setImportByName(userName);
        
        // 保存到仓储
        return importHistoryRepository.save(aggregate);
    }
    
    @Override
    public void updateImportHistoryStatus(String importId, Integer status,  String errorFilePath,
                                                          List<String> purchaseOrderNos) {
        // 参数校验
        if (StrUtil.isBlank(importId)) {
            throw new IllegalArgumentException("导入ID不能为空");
        }
        // 查询现有记录
        ImportHistoryAggregate aggregate = importHistoryRepository.findByImportId(importId);
        if (aggregate == null) {
            throw new IllegalArgumentException("导入历史记录不存在: " + importId);
        }
        // 更新状态
        aggregate.setImportStatus(ImportStatusEnum.getByCode(status));
        aggregate.setPurchaseOrderNos(purchaseOrderNos);
        aggregate.setErrorFilePath(errorFilePath);
        // 保存更新
        importHistoryRepository.update(aggregate);
    }
}
