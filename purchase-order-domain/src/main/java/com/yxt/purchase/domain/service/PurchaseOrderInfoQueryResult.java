package com.yxt.purchase.domain.service;
import com.yxt.purchase.domain.PurchaseOrderAggregate;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 采购单详情查询结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrderInfoQueryResult {
    
    /**
     * 采购单聚合根
     */
    private PurchaseOrderAggregate purchaseOrderAggregate;
    
    /**
     * 操作日志列表
     */
    private List<BizLog> operationLogs;
}
