package com.yxt.purchase.domain;


import cn.hutool.core.util.BooleanUtil;
import com.yxt.common.ddd.domain.model.BaseAggregateRoot;
import com.yxt.purchase.domain.entity.CreatePurchaseOrderDetailModel;
import com.yxt.purchase.domain.entity.PurchaseOrderDetailModel;
import com.yxt.purchase.domain.entity.ReceiveInfoModel;
import com.yxt.purchase.domain.event.PurchaseOrderDomainEvent;
import com.yxt.purchase.enums.CommodityTypeEnum;
import com.yxt.purchase.enums.PurchaseOrderLabelEnum;
import com.yxt.purchase.enums.PurchaseOrderStatusEnum;
import com.yxt.purchase.enums.SettlementStatusEnum;
import com.yxt.purchase.types.OperationUser;
import com.yxt.purchase.types.PurchaseOrderNo;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 采购订单聚合根
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderAggregate extends BaseAggregateRoot<PurchaseOrderDomainEvent<?>> {

  private Long Id;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 采购单号 PO+yyMMdd000001
   */
  private PurchaseOrderNo purchaseOrderNo;

  /**
   * 采购单状态  待确认:WAIT_CONFIRM  已确认:CONFIRMED  已完成 FINISHED  已取消 CANCELED
   */
  private PurchaseOrderStatusEnum state;

  /**
   * 提单状态
   */
  private SettlementStatusEnum  settlementStatus;
  /**
   * 采购单状态信息
   */
  private String msg;

  /**
   * 关联订单号，采购转订单后的单号
   */
  private String relatedOrderNo;

  /**
   * 父采购单号 关联一次请求或者一次导入
   */
  private String parentPurchaseOrderNo;

  /**
   * 是否超时自动支付 0:不支付  1:超时自动支付
   */
  private Boolean autoPayment;

  /**
   * 设置超期自动支付时间 天为单位，0 表示超期不会自动支付
   */
  private Integer overduePaymentTime;

  /**
   * 是否自动确认
   */
  private Boolean autoConfirm;

  /**
   * 分公司编码
   */
  private String companyCode;
  /**
   * 分公司名称
   */
  private String companyName;

  /**
   * 所属机构编码
   */
  private String organizationCode;

  /**
   * 所属机构名称
   */
  private String organizationName;

  /**
   * 门店大仓
   */
  private String warehouseCode;


  /**
   * 采购单标签 办公用品:CONSUMABLES 导入铺货: MULTIPLE_STORE
   */
  private PurchaseOrderLabelEnum purchaseOrderLabel;

  /**
   * 确认人
   */
  private OperationUser confirmBy;

  /**
   * 确认时间
   */
  private LocalDateTime confirmTime;

  /**
   * 创建时间
   */
  private LocalDateTime createdTime;

  /**
   * 修改时间
   */
  private LocalDateTime modifiedTime;

  /**
   * 创建人
   */
  private OperationUser createdBy;

  /**
   * 确认 审核人
   */
  private OperationUser approvedBy;

  /**
   * 修改人
   */
  private OperationUser modifiedBy;


  private List<CreatePurchaseOrderDetailModel> purchaseOrderDetailList;

  /**
   * 收货信息
   */
  private ReceiveInfoModel address;


  public static ReceiveInfoModel buildReceiveInfoModel(String contacter, String mobile,
      String province, String city,
      String area, String address
  ) {
    ReceiveInfoModel receiveInfoModel = new ReceiveInfoModel();
    receiveInfoModel.setReceiveName(contacter);
    receiveInfoModel.setReceiveMobile(mobile);
    receiveInfoModel.setAddress(address);
    receiveInfoModel.setDistrict(area);
    receiveInfoModel.setProvince(province);
    receiveInfoModel.setCity(city);
    return receiveInfoModel;
  }


  public static PurchaseOrderAggregate build(List<PurchaseOrderDetailModel> items,
      Boolean isAutoPayment, Integer overduePaymentTime, Boolean autoConfirm, String userId,
      String storeCode) {

    PurchaseOrderAggregate purchaseOrderAggregate = new PurchaseOrderAggregate();
    purchaseOrderAggregate.setMerCode("500001");
    purchaseOrderAggregate.setAutoPayment(isAutoPayment);
    purchaseOrderAggregate.setOverduePaymentTime(overduePaymentTime);
    purchaseOrderAggregate.setAutoConfirm(autoConfirm);
    purchaseOrderAggregate.setCreatedBy(new OperationUser(userId, null, null));
    purchaseOrderAggregate.setState(PurchaseOrderStatusEnum.WAIT_CONFIRM);
    purchaseOrderAggregate.setSettlementStatus(SettlementStatusEnum.WAIT_SETTLEMENT);
    purchaseOrderAggregate.setOrganizationCode(storeCode);
    purchaseOrderAggregate.setCreatedTime(LocalDateTime.now());
    purchaseOrderAggregate.setModifiedTime(purchaseOrderAggregate.getCreatedTime());
    //purchaseOrderAggregate.setPurchaseOrderNo(PurchaseOrderNo.create(null));
    purchaseOrderAggregate.setPurchaseOrderLabel(PurchaseOrderLabelEnum.MULTIPLE_STORE);
    purchaseOrderAggregate.setPurchaseOrderDetailList(new ArrayList<>());
    // 根据商品code 合并数量items
    Map<String, List<PurchaseOrderDetailModel>> groupedByErpCode = items.stream()
        .collect(Collectors.groupingBy(PurchaseOrderDetailModel::getErpCode));
    for (Map.Entry<String, List<PurchaseOrderDetailModel>> entry : groupedByErpCode.entrySet()) {
      CreatePurchaseOrderDetailModel createPurchaseOrderDetailModel = new CreatePurchaseOrderDetailModel();
      createPurchaseOrderDetailModel.setErpCode(entry.getKey());
      createPurchaseOrderDetailModel.setQty(BigDecimal.valueOf(entry.getValue().stream()
          .mapToDouble(item -> new BigDecimal(item.getQty()).doubleValue()).sum()));
      // 默认全部都是异常，查询商品信息后将正确的改掉。
      createPurchaseOrderDetailModel.setStatus("-1");
      createPurchaseOrderDetailModel.setEx_msg("商品不存在");
      createPurchaseOrderDetailModel.setCommodityType(CommodityTypeEnum.COMMODITY);
      createPurchaseOrderDetailModel.setIsColdChain(false);
      purchaseOrderAggregate.getPurchaseOrderDetailList().add(createPurchaseOrderDetailModel);
    }
    // 自动确认
    if (BooleanUtil.isTrue(autoConfirm)) {
      purchaseOrderAggregate.setConfirmBy(new OperationUser(userId, null, null));
      purchaseOrderAggregate.setConfirmTime(purchaseOrderAggregate.getCreatedTime());
      purchaseOrderAggregate.setState(PurchaseOrderStatusEnum.CONFIRMED);
    }

    return purchaseOrderAggregate;
  }

}
